{"name": "insurance-backend", "version": "1.0.0", "description": "Backend API for vehicle insurance claims DApp", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "keywords": ["insurance", "blockchain", "api", "besu"], "author": "Student Project", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "web3": "^4.2.0", "mongoose": "^8.0.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "redis": "^4.6.10", "ipfs-http-client": "^60.0.1", "axios": "^1.6.0"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.7.0", "supertest": "^6.3.3"}}