import React from 'react';
import {
  Container,
  Typography,
  Paper,
  Box,
  Button,
  Grid,
  Card,
  CardContent,
  Chip,
} from '@mui/material';
import { Add, DirectionsCar } from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';

const VehicleList = () => {
  const navigate = useNavigate();

  // This would come from API
  const vehicles = [];

  return (
    <Container maxWidth="lg">
      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h4" component="h1">
          My Vehicles
        </Typography>
        <Button
          variant="contained"
          startIcon={<Add />}
          onClick={() => navigate('/vehicles/register')}
        >
          Register Vehicle
        </Button>
      </Box>

      {vehicles.length === 0 ? (
        <Paper sx={{ p: 4, textAlign: 'center' }}>
          <DirectionsCar sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
          <Typography variant="h6" gutterBottom>
            No vehicles registered yet
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            Register your first vehicle to get started with insurance policies and claims.
          </Typography>
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={() => navigate('/vehicles/register')}
          >
            Register Your First Vehicle
          </Button>
        </Paper>
      ) : (
        <Grid container spacing={3}>
          {vehicles.map((vehicle) => (
            <Grid item xs={12} md={6} lg={4} key={vehicle.id}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    {vehicle.year} {vehicle.make} {vehicle.model}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    VIN: {vehicle.vin}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Color: {vehicle.color}
                  </Typography>
                  <Box sx={{ mt: 2 }}>
                    <Chip label="Registered" color="success" size="small" />
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}
    </Container>
  );
};

export default VehicleList;
