import React from 'react';
import {
  Container,
  Grid,
  Paper,
  Typography,
  Box,
  Card,
  CardContent,
  Button,
  Chip,
} from '@mui/material';
import {
  DirectionsCar,
  Policy,
  Assignment,
  TrendingUp,
  Add,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';

import { useAuth } from '../../contexts/AuthContext';
import { useWeb3 } from '../../contexts/Web3Context';

const Dashboard = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { account, isConnected, chainId } = useWeb3();

  const quickActions = [
    {
      title: 'Register Vehicle',
      description: 'Add a new vehicle to the blockchain',
      icon: <DirectionsCar />,
      path: '/vehicles/register',
      color: 'primary',
      roles: ['policyholder'],
    },
    {
      title: 'Submit Claim',
      description: 'File a new insurance claim',
      icon: <Assignment />,
      path: '/claims/submit',
      color: 'secondary',
      roles: ['policyholder'],
    },
    {
      title: 'View Vehicles',
      description: 'Manage your registered vehicles',
      icon: <DirectionsCar />,
      path: '/vehicles',
      color: 'info',
      roles: ['policyholder', 'agent', 'admin'],
    },
    {
      title: 'View Policies',
      description: 'Check your insurance policies',
      icon: <Policy />,
      path: '/policies',
      color: 'success',
      roles: ['policyholder', 'agent', 'admin'],
    },
  ];

  const stats = [
    {
      title: 'My Vehicles',
      value: '0', // This would come from API
      icon: <DirectionsCar />,
      color: 'primary',
    },
    {
      title: 'Active Policies',
      value: '0', // This would come from API
      icon: <Policy />,
      color: 'success',
    },
    {
      title: 'Claims',
      value: '0', // This would come from API
      icon: <Assignment />,
      color: 'warning',
    },
    {
      title: 'Total Coverage',
      value: '$0', // This would come from API
      icon: <TrendingUp />,
      color: 'info',
    },
  ];

  const getWelcomeMessage = () => {
    const hour = new Date().getHours();
    let greeting = 'Good morning';
    if (hour >= 12 && hour < 17) greeting = 'Good afternoon';
    if (hour >= 17) greeting = 'Good evening';
    
    return `${greeting}, ${user?.firstName || 'User'}!`;
  };

  const getNetworkStatus = () => {
    if (!isConnected) return { status: 'Disconnected', color: 'error' };
    if (chainId !== 1337) return { status: 'Wrong Network', color: 'warning' };
    return { status: 'Connected to Besu', color: 'success' };
  };

  const networkStatus = getNetworkStatus();

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      {/* Welcome Section */}
      <Paper sx={{ p: 3, mb: 3, background: 'linear-gradient(45deg, #1976d2 30%, #42a5f5 90%)' }}>
        <Box sx={{ color: 'white' }}>
          <Typography variant="h4" gutterBottom>
            {getWelcomeMessage()}
          </Typography>
          <Typography variant="h6" sx={{ opacity: 0.9 }}>
            Welcome to your Vehicle Insurance Dashboard
          </Typography>
          <Box sx={{ mt: 2, display: 'flex', gap: 2, alignItems: 'center' }}>
            <Chip
              label={`Role: ${user?.role?.toUpperCase()}`}
              sx={{ backgroundColor: 'rgba(255,255,255,0.2)', color: 'white' }}
            />
            <Chip
              label={networkStatus.status}
              color={networkStatus.color}
              sx={{ backgroundColor: 'rgba(255,255,255,0.2)', color: 'white' }}
            />
            {account && (
              <Chip
                label={`${account.slice(0, 6)}...${account.slice(-4)}`}
                sx={{ backgroundColor: 'rgba(255,255,255,0.2)', color: 'white' }}
              />
            )}
          </Box>
        </Box>
      </Paper>

      {/* Stats Grid */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        {stats.map((stat, index) => (
          <Grid item xs={12} sm={6} md={3} key={index}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <Box
                    sx={{
                      p: 1,
                      borderRadius: 1,
                      backgroundColor: `${stat.color}.light`,
                      color: `${stat.color}.main`,
                      mr: 2,
                    }}
                  >
                    {stat.icon}
                  </Box>
                  <Typography variant="h6" component="div">
                    {stat.value}
                  </Typography>
                </Box>
                <Typography variant="body2" color="text.secondary">
                  {stat.title}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Quick Actions */}
      <Paper sx={{ p: 3 }}>
        <Typography variant="h5" gutterBottom>
          Quick Actions
        </Typography>
        <Grid container spacing={2}>
          {quickActions
            .filter(action => action.roles.includes(user?.role))
            .map((action, index) => (
              <Grid item xs={12} sm={6} md={3} key={index}>
                <Card 
                  sx={{ 
                    height: '100%',
                    cursor: 'pointer',
                    '&:hover': {
                      transform: 'translateY(-2px)',
                      boxShadow: 3,
                    },
                    transition: 'all 0.2s ease-in-out',
                  }}
                  onClick={() => navigate(action.path)}
                >
                  <CardContent sx={{ textAlign: 'center', p: 3 }}>
                    <Box
                      sx={{
                        p: 2,
                        borderRadius: '50%',
                        backgroundColor: `${action.color}.light`,
                        color: `${action.color}.main`,
                        display: 'inline-flex',
                        mb: 2,
                      }}
                    >
                      {action.icon}
                    </Box>
                    <Typography variant="h6" gutterBottom>
                      {action.title}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {action.description}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            ))}
        </Grid>
      </Paper>

      {/* Network Warning */}
      {!isConnected && (
        <Paper sx={{ p: 2, mt: 3, backgroundColor: 'warning.light' }}>
          <Typography variant="body1" color="warning.dark">
            ⚠️ Please connect your MetaMask wallet to interact with the blockchain features.
          </Typography>
        </Paper>
      )}

      {isConnected && chainId !== 1337 && (
        <Paper sx={{ p: 2, mt: 3, backgroundColor: 'error.light' }}>
          <Typography variant="body1" color="error.dark">
            ⚠️ Please switch to the Hyperledger Besu network (Chain ID: 1337) to use this application.
          </Typography>
        </Paper>
      )}
    </Container>
  );
};

export default Dashboard;
