const express = require('express');
const { body, validationResult } = require('express-validator');
const Policy = require('../models/Policy');
const Vehicle = require('../models/Vehicle');
const { auth, authorize } = require('../middleware/auth');
const { contracts, accounts, sendTransaction } = require('../config/blockchain');

const router = express.Router();

// Get user's policies
router.get('/my-policies', auth, async (req, res) => {
  try {
    const policies = await Policy.find({
      policyholder: req.user.walletAddress,
    }).sort({ createdAt: -1 });

    res.json({ policies });
  } catch (error) {
    console.error('Get user policies error:', error);
    res.status(500).json({ error: 'Failed to fetch your policies' });
  }
});

// Get all policies (admin/agent only)
router.get('/', auth, authorize('admin', 'agent'), async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const query = {};
    if (req.user.role === 'agent') {
      query.insurer = req.user.walletAddress;
    }

    const policies = await Policy.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const total = await Policy.countDocuments(query);

    res.json({
      policies,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Get policies error:', error);
    res.status(500).json({ error: 'Failed to fetch policies' });
  }
});

// Get policy by ID
router.get('/:id', auth, async (req, res) => {
  try {
    const policy = await Policy.findOne({
      blockchainId: req.params.id
    });

    if (!policy) {
      return res.status(404).json({ error: 'Policy not found' });
    }

    // Check access permissions
    const hasAccess = policy.policyholder === req.user.walletAddress ||
                     policy.insurer === req.user.walletAddress ||
                     req.user.role === 'admin';

    if (!hasAccess) {
      return res.status(403).json({ error: 'Access denied' });
    }

    res.json({ policy });
  } catch (error) {
    console.error('Get policy error:', error);
    res.status(500).json({ error: 'Failed to fetch policy' });
  }
});

// Create new policy (agent/admin only)
router.post('/create', auth, authorize('agent', 'admin'), [
  body('vehicleId').isInt({ min: 1 }).withMessage('Valid vehicle ID required'),
  body('policyholder').isEthereumAddress().withMessage('Valid policyholder address required'),
  body('coverageType').isIn(['Liability', 'Comprehensive', 'Collision', 'Full']).withMessage('Invalid coverage type'),
  body('premiumAmount').isFloat({ min: 0 }).withMessage('Premium amount must be positive'),
  body('coverageAmount').isFloat({ min: 0 }).withMessage('Coverage amount must be positive'),
  body('deductible').isFloat({ min: 0 }).withMessage('Deductible must be positive'),
  body('durationInDays').isInt({ min: 1 }).withMessage('Duration must be at least 1 day'),
  body('policyNumber').trim().isLength({ min: 1 }).withMessage('Policy number required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const {
      vehicleId,
      policyholder,
      coverageType,
      premiumAmount,
      coverageAmount,
      deductible,
      durationInDays,
      policyNumber,
      paymentFrequency,
      paymentMethod,
      beneficiaries,
      coverageDetails,
      discounts,
      isAutoRenewal,
      notes
    } = req.body;

    // Check if policy number already exists
    const existingPolicy = await Policy.findOne({ policyNumber });
    if (existingPolicy) {
      return res.status(400).json({ error: 'Policy number already exists' });
    }

    // Verify vehicle exists and policyholder owns it
    const vehicle = await Vehicle.findOne({ blockchainId: vehicleId });
    if (!vehicle) {
      return res.status(404).json({ error: 'Vehicle not found' });
    }

    if (vehicle.owner !== policyholder.toLowerCase()) {
      return res.status(400).json({ error: 'Policyholder must own the vehicle' });
    }

    // Create policy on blockchain
    const insurancePolicy = contracts.insurancePolicy;
    const coverageTypeIndex = ['Liability', 'Comprehensive', 'Collision', 'Full'].indexOf(coverageType);
    
    const transaction = insurancePolicy.methods.createPolicy(
      vehicleId,
      policyholder,
      coverageTypeIndex,
      Math.floor(premiumAmount * 100), // Convert to cents
      Math.floor(coverageAmount * 100),
      Math.floor(deductible * 100),
      durationInDays,
      policyNumber
    );

    const result = await sendTransaction(transaction, {
      address: req.user.walletAddress
    });

    // Get the policy ID from the event
    const policyCreatedEvent = result.events.PolicyCreated;
    const blockchainId = parseInt(policyCreatedEvent.returnValues.policyId);

    // Calculate dates
    const startDate = new Date();
    const endDate = new Date(startDate.getTime() + (durationInDays * 24 * 60 * 60 * 1000));
    const renewalDate = new Date(endDate.getTime() - (30 * 24 * 60 * 60 * 1000)); // 30 days before expiry

    // Save to database
    const policy = new Policy({
      blockchainId,
      policyNumber,
      vehicleId,
      policyholder: policyholder.toLowerCase(),
      insurer: req.user.walletAddress,
      coverageType,
      premiumAmount,
      coverageAmount,
      deductible,
      startDate,
      endDate,
      paymentFrequency,
      paymentMethod,
      beneficiaries,
      coverageDetails,
      discounts,
      renewalDate,
      isAutoRenewal,
      notes
    });

    await policy.save();

    res.status(201).json({
      message: 'Policy created successfully',
      policy,
      transactionHash: result.transactionHash
    });

  } catch (error) {
    console.error('Policy creation error:', error);
    
    if (error.message.includes('Policy number already exists')) {
      return res.status(400).json({ error: 'Policy number already exists on blockchain' });
    }
    
    res.status(500).json({ error: 'Failed to create policy' });
  }
});

// Update policy status (agent/admin only)
router.put('/:id/status', auth, authorize('agent', 'admin'), [
  body('status').isIn(['Active', 'Expired', 'Cancelled', 'Suspended']).withMessage('Invalid status'),
  body('reason').optional().trim()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { status, reason } = req.body;

    const policy = await Policy.findOne({
      blockchainId: req.params.id
    });

    if (!policy) {
      return res.status(404).json({ error: 'Policy not found' });
    }

    // Check if user can update this policy
    if (policy.insurer !== req.user.walletAddress && req.user.role !== 'admin') {
      return res.status(403).json({ error: 'Access denied' });
    }

    // Update status on blockchain
    const insurancePolicy = contracts.insurancePolicy;
    const statusIndex = ['Active', 'Expired', 'Cancelled', 'Suspended'].indexOf(status);
    
    const transaction = insurancePolicy.methods.updatePolicyStatus(
      parseInt(req.params.id),
      statusIndex
    );

    const result = await sendTransaction(transaction, {
      address: req.user.walletAddress
    });

    // Update database
    policy.status = status;
    if (reason) {
      policy.notes = (policy.notes || '') + `\nStatus changed to ${status}: ${reason}`;
    }
    await policy.save();

    res.json({
      message: 'Policy status updated successfully',
      policy,
      transactionHash: result.transactionHash
    });

  } catch (error) {
    console.error('Policy status update error:', error);
    res.status(500).json({ error: 'Failed to update policy status' });
  }
});

// Get policies for a specific vehicle
router.get('/vehicle/:vehicleId', auth, async (req, res) => {
  try {
    const { vehicleId } = req.params;

    // Verify user has access to this vehicle
    const vehicle = await Vehicle.findOne({ blockchainId: vehicleId });
    if (!vehicle) {
      return res.status(404).json({ error: 'Vehicle not found' });
    }

    const hasAccess = vehicle.owner === req.user.walletAddress ||
                     req.user.role === 'admin' ||
                     req.user.role === 'agent';

    if (!hasAccess) {
      return res.status(403).json({ error: 'Access denied' });
    }

    const policies = await Policy.find({ vehicleId: parseInt(vehicleId) })
      .sort({ createdAt: -1 });

    res.json({ policies });

  } catch (error) {
    console.error('Get vehicle policies error:', error);
    res.status(500).json({ error: 'Failed to fetch vehicle policies' });
  }
});

// Renew policy
router.post('/:id/renew', auth, authorize('agent', 'admin'), [
  body('durationInDays').isInt({ min: 1 }).withMessage('Duration must be at least 1 day'),
  body('premiumAmount').optional().isFloat({ min: 0 }).withMessage('Premium amount must be positive'),
  body('coverageAmount').optional().isFloat({ min: 0 }).withMessage('Coverage amount must be positive')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { durationInDays, premiumAmount, coverageAmount } = req.body;

    const policy = await Policy.findOne({
      blockchainId: req.params.id
    });

    if (!policy) {
      return res.status(404).json({ error: 'Policy not found' });
    }

    // Check if user can renew this policy
    if (policy.insurer !== req.user.walletAddress && req.user.role !== 'admin') {
      return res.status(403).json({ error: 'Access denied' });
    }

    // Generate new policy number for renewal
    const newPolicyNumber = `${policy.policyNumber}-R${Date.now()}`;

    // Create renewal policy on blockchain
    const insurancePolicy = contracts.insurancePolicy;
    const coverageTypeIndex = ['Liability', 'Comprehensive', 'Collision', 'Full'].indexOf(policy.coverageType);
    
    const transaction = insurancePolicy.methods.createPolicy(
      policy.vehicleId,
      policy.policyholder,
      coverageTypeIndex,
      Math.floor((premiumAmount || policy.premiumAmount) * 100),
      Math.floor((coverageAmount || policy.coverageAmount) * 100),
      Math.floor(policy.deductible * 100),
      durationInDays,
      newPolicyNumber
    );

    const result = await sendTransaction(transaction, {
      address: req.user.walletAddress
    });

    // Get the new policy ID from the event
    const policyCreatedEvent = result.events.PolicyCreated;
    const newBlockchainId = parseInt(policyCreatedEvent.returnValues.policyId);

    // Calculate new dates
    const startDate = new Date();
    const endDate = new Date(startDate.getTime() + (durationInDays * 24 * 60 * 60 * 1000));
    const renewalDate = new Date(endDate.getTime() - (30 * 24 * 60 * 60 * 1000));

    // Create new policy record
    const renewedPolicy = new Policy({
      blockchainId: newBlockchainId,
      policyNumber: newPolicyNumber,
      vehicleId: policy.vehicleId,
      policyholder: policy.policyholder,
      insurer: policy.insurer,
      coverageType: policy.coverageType,
      premiumAmount: premiumAmount || policy.premiumAmount,
      coverageAmount: coverageAmount || policy.coverageAmount,
      deductible: policy.deductible,
      startDate,
      endDate,
      paymentFrequency: policy.paymentFrequency,
      paymentMethod: policy.paymentMethod,
      beneficiaries: policy.beneficiaries,
      coverageDetails: policy.coverageDetails,
      discounts: policy.discounts,
      renewalDate,
      isAutoRenewal: policy.isAutoRenewal,
      notes: `Renewed from policy ${policy.policyNumber}`
    });

    await renewedPolicy.save();

    // Mark old policy as expired
    policy.status = 'Expired';
    policy.notes = (policy.notes || '') + `\nPolicy renewed as ${newPolicyNumber}`;
    await policy.save();

    res.status(201).json({
      message: 'Policy renewed successfully',
      policy: renewedPolicy,
      transactionHash: result.transactionHash
    });

  } catch (error) {
    console.error('Policy renewal error:', error);
    res.status(500).json({ error: 'Failed to renew policy' });
  }
});

module.exports = router;
