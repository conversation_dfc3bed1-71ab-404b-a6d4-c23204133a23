<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🛡️ Himaya Blockchain Insurance</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 50%, #1a1a1a 100%);
            color: #e5e5e5;
            min-height: 100vh;
            overflow-x: hidden;
        }
        
        /* Splash Screen */
        .splash-screen {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            text-align: center;
            background: linear-gradient(135deg, #1a1a1a, #8b5cf6, #1a1a1a);
            animation: gradientShift 3s ease infinite;
        }
        
        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }
        
        .splash-logo {
            font-size: 8rem;
            margin-bottom: 2rem;
            animation: logoFloat 2s ease-in-out infinite;
            text-shadow: 0 0 30px #8b5cf6;
        }
        
        @keyframes logoFloat {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
        
        .splash-title {
            font-size: 4rem;
            font-weight: 900;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, #8b5cf6, #ec4899);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .splash-subtitle {
            font-size: 1.5rem;
            color: #9ca3af;
            margin-bottom: 3rem;
        }
        
        .loading-bar {
            width: 300px;
            height: 6px;
            background: #374151;
            border-radius: 3px;
            overflow: hidden;
            margin-bottom: 2rem;
        }
        
        .loading-progress {
            height: 100%;
            background: linear-gradient(90deg, #8b5cf6, #ec4899);
            width: 0%;
            animation: loadProgress 3s ease-out forwards;
        }
        
        @keyframes loadProgress {
            to { width: 100%; }
        }
        
        /* Login Screen */
        .login-screen {
            display: none;
            min-height: 100vh;
            padding: 2rem;
        }
        
        .login-container {
            max-width: 800px;
            margin: 0 auto;
            padding-top: 5rem;
        }
        
        .login-header {
            text-align: center;
            margin-bottom: 4rem;
        }
        
        .login-title {
            font-size: 3rem;
            font-weight: 700;
            color: #f8f9fa;
            margin-bottom: 1rem;
        }
        
        .login-subtitle {
            font-size: 1.2rem;
            color: #9ca3af;
            margin-bottom: 2rem;
        }
        
        .language-selector {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 3rem;
        }
        
        .lang-btn {
            background: #374151;
            border: 2px solid #4b5563;
            border-radius: 8px;
            padding: 0.5rem 1rem;
            color: #e5e7eb;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .lang-btn.active {
            background: #8b5cf6;
            border-color: #8b5cf6;
            color: white;
        }
        
        .session-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }
        
        .session-card {
            background: #2d3748;
            border: 2px solid #4a5568;
            border-radius: 20px;
            padding: 2rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .session-card:hover {
            border-color: #8b5cf6;
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(139, 92, 246, 0.3);
        }

        .session-card[data-session="new-client"] {
            border-color: #10b981;
            background: linear-gradient(135deg, #2d3748 0%, rgba(16, 185, 129, 0.1) 100%);
        }

        .session-card[data-session="new-client"]:hover {
            border-color: #10b981;
            box-shadow: 0 15px 35px rgba(16, 185, 129, 0.3);
        }

        .session-card[data-session="new-client"] .session-btn {
            background: #10b981;
        }

        .session-card[data-session="new-client"] .session-btn:hover {
            background: #059669;
        }
        
        .session-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
        }
        
        .session-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #f7fafc;
            margin-bottom: 1rem;
        }
        
        .session-description {
            color: #9ca3af;
            margin-bottom: 2rem;
            font-size: 0.9rem;
        }
        
        .session-btn {
            background: #8b5cf6;
            border: none;
            border-radius: 12px;
            padding: 1rem 2rem;
            color: white;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            width: 100%;
        }
        
        .session-btn:hover {
            background: #7c3aed;
            transform: translateY(-2px);
        }
        
        /* Session Screens */
        .session-screen {
            display: none;
            min-height: 100vh;
            padding: 2rem;
        }
        
        .session-header {
            background: rgba(139, 92, 246, 0.1);
            border: 1px solid rgba(139, 92, 246, 0.3);
            border-radius: 12px;
            padding: 1.5rem 2rem;
            margin-bottom: 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .session-info h1 {
            color: #8b5cf6;
            font-size: 2rem;
            margin: 0;
        }
        
        .session-info p {
            color: #9ca3af;
            margin: 0;
        }
        
        .session-actions {
            display: flex;
            gap: 1rem;
        }
        
        .btn {
            background: #8b5cf6;
            border: 1px solid #8b5cf6;
            border-radius: 8px;
            padding: 0.75rem 1.5rem;
            color: white;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn:hover {
            background: #7c3aed;
            transform: translateY(-1px);
        }
        
        .btn-secondary {
            background: #374151;
            border-color: #4b5563;
        }
        
        .btn-secondary:hover {
            background: #4b5563;
        }
        
        .card {
            background: #2d3748;
            border: 1px solid #4a5568;
            border-radius: 16px;
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .card-title {
            font-size: 1.8rem;
            font-weight: 600;
            margin-bottom: 1.5rem;
            color: #f7fafc;
        }
        
        .form-input {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #4a5568;
            border-radius: 8px;
            background: #374151;
            color: #e5e7eb;
            margin-bottom: 1rem;
            transition: all 0.2s ease;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #8b5cf6;
            box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
        }
        
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }
        
        .hidden { display: none !important; }
        
        .notification {
            position: fixed;
            top: 2rem;
            right: 2rem;
            background: #374151;
            color: #e5e7eb;
            padding: 1rem 1.5rem;
            border-radius: 8px;
            border: 1px solid #8b5cf6;
            z-index: 1000;
            animation: slideIn 0.3s ease;
        }
        
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        
        /* Identity Verification Styles */
        .step-indicator {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #4a5568;
            color: #9ca3af;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .step-indicator.active {
            background: #8b5cf6;
            color: white;
        }

        .document-upload-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .document-upload-item {
            text-align: center;
        }

        .document-preview {
            width: 100%;
            height: 200px;
            border: 2px dashed #4a5568;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
            cursor: pointer;
            overflow: hidden;
        }

        .document-preview:hover {
            border-color: #8b5cf6;
            background: rgba(139, 92, 246, 0.05);
        }

        .document-preview.has-image {
            border-color: #10b981;
            border-style: solid;
        }

        .upload-placeholder {
            text-align: center;
            color: #9ca3af;
        }

        .upload-btn {
            background: #8b5cf6;
            border: none;
            border-radius: 8px;
            padding: 0.75rem 1.5rem;
            color: white;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            width: 100%;
        }

        .upload-btn:hover {
            background: #7c3aed;
            transform: translateY(-1px);
        }

        .upload-btn.uploaded {
            background: #10b981;
        }

        .verification-status {
            text-align: center;
            padding: 2rem;
        }

        .status-pending {
            color: #f59e0b;
        }

        .status-approved {
            color: #10b981;
        }

        .status-rejected {
            color: #ef4444;
        }

        .status-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
        }

        .status-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .status-description {
            color: #9ca3af;
            margin-bottom: 2rem;
        }

        .wallet-credentials {
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid #10b981;
            border-radius: 12px;
            padding: 1.5rem;
            margin: 1rem 0;
        }

        .credential-item {
            background: #374151;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .credential-label {
            color: #8b5cf6;
            font-weight: 600;
        }

        .credential-value {
            color: #e5e7eb;
            font-family: monospace;
            background: rgba(0,0,0,0.3);
            padding: 0.5rem;
            border-radius: 4px;
            cursor: pointer;
        }

        .copy-btn {
            background: #6b7280;
            border: none;
            border-radius: 4px;
            padding: 0.25rem 0.5rem;
            color: white;
            cursor: pointer;
            font-size: 0.8rem;
            margin-left: 0.5rem;
        }

        .copy-btn:hover {
            background: #4b5563;
        }

        @media (max-width: 768px) {
            .splash-title { font-size: 2.5rem; }
            .login-title { font-size: 2rem; }
            .session-options { grid-template-columns: 1fr; }
            .document-upload-grid { grid-template-columns: 1fr; }
        }
    </style>
</head>
<body>
    <!-- Splash Screen -->
    <div id="splashScreen" class="splash-screen">
        <div class="splash-logo">🛡️</div>
        <h1 class="splash-title">HIMAYA</h1>
        <p class="splash-subtitle">Blockchain Insurance Platform</p>
        <div class="loading-bar">
            <div class="loading-progress"></div>
        </div>
        <p style="color: #9ca3af;">Initializing blockchain connection...</p>
    </div>

    <!-- Login Screen -->
    <div id="loginScreen" class="login-screen">
        <div class="login-container">
            <div class="login-header">
                <h1 class="login-title">🛡️ Bienvenue sur Himaya</h1>
                <p class="login-subtitle">Plateforme d'Assurance Blockchain Sécurisée</p>

                <div style="background: rgba(139, 92, 246, 0.1); border: 1px solid #8b5cf6; border-radius: 12px; padding: 1.5rem; margin: 2rem 0;">
                    <h3 style="color: #8b5cf6; margin-bottom: 1rem;">🔐 Système de Vérification Sécurisé</h3>
                    <p style="color: #e5e7eb; margin: 0; text-align: center;">
                        Seuls les clients vérifiés avec documents d'identité peuvent accéder à la plateforme.<br>
                        <strong style="color: #10b981;">Nouveau client ?</strong> Commencez par la vérification d'identité ci-dessous.
                    </p>
                </div>

                <div class="language-selector">
                    <button class="lang-btn active" data-lang="fr">🇫🇷 Français</button>
                    <button class="lang-btn" data-lang="ar">🇲🇦 العربية</button>
                    <button class="lang-btn" data-lang="en">🇬🇧 English</button>
                </div>
            </div>

            <div class="session-options">
                <div class="session-card" data-session="new-client" onclick="showIdentityVerification()">
                    <div class="session-icon">👤</div>
                    <h3 class="session-title">Nouveau Client</h3>
                    <p class="session-description">Créez votre compte et soumettez vos documents d'identité pour vérification</p>
                    <button class="session-btn">📝 S'inscrire</button>
                </div>

                <div class="session-card" data-session="client">
                    <div class="session-icon">🔐</div>
                    <h3 class="session-title">Client Vérifié</h3>
                    <p class="session-description">Accédez à votre compte avec votre wallet vérifié</p>
                    <button class="session-btn">🔑 Accéder</button>
                </div>

                <div class="session-card" data-session="insurer">
                    <div class="session-icon">🏢</div>
                    <h3 class="session-title">Session Assureur</h3>
                    <p class="session-description">Gérez les polices, approuvez les réclamations et analysez les données</p>
                    <button class="session-btn">🏢 Accéder</button>
                </div>

                <div class="session-card" data-session="admin">
                    <div class="session-icon">⚙️</div>
                    <h3 class="session-title">Session Admin</h3>
                    <p class="session-description">Administration complète du système et monitoring blockchain</p>
                    <button class="session-btn">⚙️ Accéder</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Identity Verification Screen -->
    <div id="identityVerificationScreen" class="session-screen">
        <div class="session-header">
            <div class="session-info">
                <h1>📋 Vérification d'Identité</h1>
                <p>Soumettez vos documents pour créer votre compte Himaya</p>
            </div>
            <div class="session-actions">
                <button class="btn btn-secondary" onclick="showScreen('loginScreen')">🔙 Retour</button>
            </div>
        </div>

        <!-- Verification Process Steps -->
        <div class="card">
            <h2 class="card-title">🔐 Processus de Vérification Sécurisé</h2>
            <div style="background: rgba(139, 92, 246, 0.1); border: 1px solid #8b5cf6; border-radius: 12px; padding: 1.5rem; margin-bottom: 2rem;">
                <h3 style="color: #8b5cf6; margin-bottom: 1rem;">🛡️ Pourquoi cette vérification ?</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem; color: #e5e7eb;">
                    <div>✅ <strong>Sécurité maximale</strong><br>Protection contre la fraude</div>
                    <div>✅ <strong>Conformité légale</strong><br>Respect des réglementations</div>
                    <div>✅ <strong>Wallet sécurisé</strong><br>Credentials fournis par l'assureur</div>
                    <div>✅ <strong>Transactions protégées</strong><br>Seuls les vrais clients accèdent</div>
                </div>
            </div>

            <!-- Step Indicator -->
            <div style="display: flex; justify-content: center; margin-bottom: 2rem;">
                <div style="display: flex; align-items: center; gap: 1rem;">
                    <div class="step-indicator active">1</div>
                    <div style="width: 50px; height: 2px; background: #4a5568;"></div>
                    <div class="step-indicator">2</div>
                    <div style="width: 50px; height: 2px; background: #4a5568;"></div>
                    <div class="step-indicator">3</div>
                </div>
            </div>
        </div>

        <!-- Personal Information -->
        <div class="card">
            <h2 class="card-title">👤 Informations Personnelles</h2>
            <div class="grid">
                <div>
                    <input type="text" id="verifyFullName" class="form-input" placeholder="Nom complet *" required>
                    <input type="text" id="verifyCinNumber" class="form-input" placeholder="Numéro CIN *" required>
                    <input type="date" id="verifyBirthDate" class="form-input" placeholder="Date de naissance *" required>
                </div>
                <div>
                    <input type="tel" id="verifyPhoneNumber" class="form-input" placeholder="Numéro de téléphone *" required>
                    <input type="email" id="verifyEmailAddress" class="form-input" placeholder="Adresse email *" required>
                    <input type="text" id="verifyAddress" class="form-input" placeholder="Adresse complète *" required>
                </div>
            </div>
        </div>

        <!-- Document Upload -->
        <div class="card">
            <h2 class="card-title">📄 Documents d'Identité</h2>
            <div class="document-upload-grid">
                <!-- CIN Front -->
                <div class="document-upload-item">
                    <div class="document-preview" id="cinFrontPreview">
                        <div class="upload-placeholder">
                            <div style="font-size: 3rem; margin-bottom: 1rem;">🆔</div>
                            <div style="font-weight: 600; margin-bottom: 0.5rem;">CIN - Recto</div>
                            <div style="font-size: 0.9rem; color: #9ca3af;">Cliquez pour télécharger</div>
                        </div>
                    </div>
                    <input type="file" id="cinFrontFile" accept="image/*" style="display: none;">
                    <button class="upload-btn" onclick="document.getElementById('cinFrontFile').click()">
                        📤 Télécharger Recto CIN
                    </button>
                </div>

                <!-- CIN Back -->
                <div class="document-upload-item">
                    <div class="document-preview" id="cinBackPreview">
                        <div class="upload-placeholder">
                            <div style="font-size: 3rem; margin-bottom: 1rem;">🆔</div>
                            <div style="font-weight: 600; margin-bottom: 0.5rem;">CIN - Verso</div>
                            <div style="font-size: 0.9rem; color: #9ca3af;">Cliquez pour télécharger</div>
                        </div>
                    </div>
                    <input type="file" id="cinBackFile" accept="image/*" style="display: none;">
                    <button class="upload-btn" onclick="document.getElementById('cinBackFile').click()">
                        📤 Télécharger Verso CIN
                    </button>
                </div>

                <!-- Selfie -->
                <div class="document-upload-item">
                    <div class="document-preview" id="selfiePreview">
                        <div class="upload-placeholder">
                            <div style="font-size: 3rem; margin-bottom: 1rem;">🤳</div>
                            <div style="font-weight: 600; margin-bottom: 0.5rem;">Photo Selfie</div>
                            <div style="font-size: 0.9rem; color: #9ca3af;">Visage visible et net</div>
                        </div>
                    </div>
                    <input type="file" id="selfieFile" accept="image/*" style="display: none;">
                    <button class="upload-btn" onclick="document.getElementById('selfieFile').click()">
                        📤 Télécharger Selfie
                    </button>
                </div>
            </div>

            <!-- Upload Guidelines -->
            <div style="background: rgba(245, 158, 11, 0.1); border: 1px solid #f59e0b; border-radius: 8px; padding: 1rem; margin-top: 2rem;">
                <h4 style="color: #f59e0b; margin-bottom: 0.5rem;">📋 Instructions de Téléchargement</h4>
                <ul style="color: #e5e7eb; margin: 0; padding-left: 1.5rem;">
                    <li>Photos claires et nettes (min 1MB, max 10MB)</li>
                    <li>Format: JPG, PNG ou PDF</li>
                    <li>CIN entièrement visible sans reflets</li>
                    <li>Selfie avec visage bien éclairé</li>
                    <li>Tous les textes doivent être lisibles</li>
                </ul>
            </div>
        </div>

        <!-- Contact Preferences -->
        <div class="card">
            <h2 class="card-title">📞 Préférences de Contact</h2>
            <div style="background: #374151; padding: 1.5rem; border-radius: 12px;">
                <h3 style="color: #8b5cf6; margin-bottom: 1rem;">Comment souhaitez-vous être contacté pour la vérification ?</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                    <label style="display: flex; align-items: center; gap: 0.5rem; cursor: pointer;">
                        <input type="checkbox" id="contactEmail" checked style="margin: 0;">
                        <span>📧 Email</span>
                    </label>
                    <label style="display: flex; align-items: center; gap: 0.5rem; cursor: pointer;">
                        <input type="checkbox" id="contactPhone" checked style="margin: 0;">
                        <span>📱 SMS/Appel</span>
                    </label>
                    <label style="display: flex; align-items: center; gap: 0.5rem; cursor: pointer;">
                        <input type="checkbox" id="contactWhatsapp" style="margin: 0;">
                        <span>💬 WhatsApp</span>
                    </label>
                </div>

                <div style="margin-top: 1.5rem;">
                    <label style="color: #8b5cf6; font-weight: 600; display: block; margin-bottom: 0.5rem;">Créneaux de disponibilité:</label>
                    <select id="availabilityTime" class="form-input">
                        <option value="morning">🌅 Matin (8h-12h)</option>
                        <option value="afternoon">☀️ Après-midi (12h-17h)</option>
                        <option value="evening">🌆 Soir (17h-20h)</option>
                        <option value="anytime">🕐 N'importe quand</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Submit Button -->
        <div class="card" style="text-align: center;">
            <div style="background: rgba(139, 92, 246, 0.1); border: 1px solid #8b5cf6; border-radius: 12px; padding: 1.5rem; margin-bottom: 2rem;">
                <h3 style="color: #8b5cf6; margin-bottom: 1rem;">🔄 Processus de Vérification</h3>
                <div style="color: #e5e7eb; margin-bottom: 1rem;">
                    Après soumission, notre équipe vérifiera vos documents sous 24-48h.
                    Vous recevrez vos identifiants de wallet sécurisé par email une fois approuvé.
                </div>
                <div style="font-size: 0.9rem; color: #9ca3af;">
                    ⏱️ Temps de traitement: 1-2 jours ouvrables<br>
                    🔐 Wallet MetaMask fourni après approbation
                </div>
            </div>

            <button class="btn" onclick="submitIdentityVerification()" style="font-size: 1.1rem; padding: 1rem 3rem;">
                🚀 Soumettre ma Demande de Vérification
            </button>
        </div>
    </div>

    <!-- Verification Status Screen -->
    <div id="verificationStatusScreen" class="session-screen">
        <div class="session-header">
            <div class="session-info">
                <h1>⏳ Vérification en Cours</h1>
                <p>Votre demande est en cours de traitement</p>
            </div>
            <div class="session-actions">
                <button class="btn btn-secondary" onclick="showScreen('loginScreen')">🔙 Retour Accueil</button>
                <button class="btn" onclick="checkVerificationStatus()">🔄 Vérifier le Statut</button>
            </div>
        </div>

        <!-- Status Display -->
        <div class="card">
            <div id="verificationStatusContent">
                <!-- Content will be dynamically updated -->
            </div>
        </div>

        <!-- What's Next -->
        <div class="card">
            <h2 class="card-title">📋 Prochaines Étapes</h2>
            <div style="background: #374151; padding: 1.5rem; border-radius: 12px;">
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1.5rem;">
                    <div style="text-align: center;">
                        <div style="font-size: 2.5rem; margin-bottom: 0.5rem;">📋</div>
                        <h4 style="color: #8b5cf6; margin-bottom: 0.5rem;">1. Vérification</h4>
                        <p style="color: #9ca3af; font-size: 0.9rem;">Notre équipe vérifie vos documents d'identité</p>
                    </div>
                    <div style="text-align: center;">
                        <div style="font-size: 2.5rem; margin-bottom: 0.5rem;">🔐</div>
                        <h4 style="color: #8b5cf6; margin-bottom: 0.5rem;">2. Wallet Sécurisé</h4>
                        <p style="color: #9ca3af; font-size: 0.9rem;">Réception des identifiants MetaMask par email</p>
                    </div>
                    <div style="text-align: center;">
                        <div style="font-size: 2.5rem; margin-bottom: 0.5rem;">🛡️</div>
                        <h4 style="color: #8b5cf6; margin-bottom: 0.5rem;">3. Accès Complet</h4>
                        <p style="color: #9ca3af; font-size: 0.9rem;">Utilisation complète de la plateforme Himaya</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Contact Support -->
        <div class="card">
            <h2 class="card-title">📞 Besoin d'Aide ?</h2>
            <div style="background: rgba(139, 92, 246, 0.1); border: 1px solid #8b5cf6; border-radius: 12px; padding: 1.5rem;">
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                    <div>
                        <strong style="color: #8b5cf6;">📧 Email Support</strong><br>
                        <span style="color: #e5e7eb;"><EMAIL></span>
                    </div>
                    <div>
                        <strong style="color: #8b5cf6;">📱 Téléphone</strong><br>
                        <span style="color: #e5e7eb;">+212 5XX-XXXXXX</span>
                    </div>
                    <div>
                        <strong style="color: #8b5cf6;">⏰ Horaires</strong><br>
                        <span style="color: #e5e7eb;">Lun-Ven 9h-18h</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- CLIENT SESSION -->
    <div id="clientSession" class="session-screen">
        <div class="session-header">
            <div class="session-info">
                <h1 data-translate="client_dashboard">👤 Dashboard Client</h1>
                <p data-translate="manage_insurance">Gérez vos assurances et véhicules</p>
            </div>
            <div class="session-actions">
                <button class="btn btn-secondary" id="clientReturnBtn" data-translate="return">🔙 Retour</button>
                <button class="btn" id="clientWalletBtn" data-translate="connect_wallet">🦊 Connecter Wallet</button>
            </div>
        </div>

        <!-- Wallet Connection -->
        <div class="card">
            <h2 class="card-title" data-translate="wallet_connection">🔗 Connexion Blockchain</h2>
            <div class="grid">
                <div>
                    <label data-translate="wallet_address">Adresse Wallet:</label>
                    <div id="clientWalletAddress" class="form-input" style="background: rgba(0,0,0,0.4);">Non connecté</div>
                    <label data-translate="balance">Solde:</label>
                    <div id="clientBalance" class="form-input" style="background: rgba(0,0,0,0.4);">0.0000 ETH</div>
                </div>
                <div>
                    <label data-translate="network">Réseau:</label>
                    <div id="clientNetwork" class="form-input" style="background: rgba(0,0,0,0.4);">Non connecté</div>
                    <label data-translate="status">Statut:</label>
                    <div id="clientStatus" class="form-input" style="background: rgba(0,0,0,0.4);">Déconnecté</div>
                </div>
            </div>
        </div>

        <!-- Insurance Plans -->
        <div class="card">
            <h2 class="card-title" data-translate="insurance_plans">📋 Plans d'Assurance</h2>
            <div class="grid">
                <div style="background: rgba(139, 92, 246, 0.1); padding: 1.5rem; border-radius: 12px; text-align: center;">
                    <h3 style="color: #8b5cf6;">🥉 Plan Basique</h3>
                    <div style="font-size: 1.5rem; font-weight: 600; margin: 1rem 0;">0.05 ETH/mois</div>
                    <ul style="text-align: left; margin: 1rem 0;">
                        <li>✅ Couverture accidents</li>
                        <li>✅ Vol et vandalisme</li>
                        <li>✅ Assistance 24h/7j</li>
                    </ul>
                    <button class="btn" onclick="subscribePlan('basic')" data-translate="subscribe">Souscrire</button>
                </div>
                <div style="background: rgba(16, 185, 129, 0.1); padding: 1.5rem; border-radius: 12px; text-align: center;">
                    <h3 style="color: #10b981;">🥈 Plan Standard</h3>
                    <div style="font-size: 1.5rem; font-weight: 600; margin: 1rem 0;">0.08 ETH/mois</div>
                    <ul style="text-align: left; margin: 1rem 0;">
                        <li>✅ Tout du plan basique</li>
                        <li>✅ Tous risques</li>
                        <li>✅ Véhicule de remplacement</li>
                    </ul>
                    <button class="btn" onclick="subscribePlan('standard')" data-translate="subscribe">Souscrire</button>
                </div>
                <div style="background: rgba(245, 158, 11, 0.1); padding: 1.5rem; border-radius: 12px; text-align: center;">
                    <h3 style="color: #f59e0b;">🥇 Plan Premium</h3>
                    <div style="font-size: 1.5rem; font-weight: 600; margin: 1rem 0;">0.12 ETH/mois</div>
                    <ul style="text-align: left; margin: 1rem 0;">
                        <li>✅ Tout du plan standard</li>
                        <li>✅ Couverture internationale</li>
                        <li>✅ Réparations premium</li>
                    </ul>
                    <button class="btn" onclick="subscribePlan('premium')" data-translate="subscribe">Souscrire</button>
                </div>
            </div>
        </div>

        <!-- Vehicle Registration -->
        <div class="card">
            <h2 class="card-title" data-translate="register_vehicle">🚗 Enregistrer un Véhicule</h2>
            <div class="grid">
                <div>
                    <input type="text" id="vehicleVin" class="form-input" placeholder="Immatriculation" data-translate-placeholder="license_plate">
                    <input type="text" id="vehicleMake" class="form-input" placeholder="Marque" data-translate-placeholder="brand">
                    <input type="text" id="vehicleModel" class="form-input" placeholder="Modèle" data-translate-placeholder="model">
                </div>
                <div>
                    <input type="number" id="vehicleYear" class="form-input" placeholder="Année" data-translate-placeholder="year">
                    <select id="vehicleType" class="form-input">
                        <option value="">Type de véhicule</option>
                        <option value="car">🚗 Voiture</option>
                        <option value="motorcycle">🏍️ Moto</option>
                        <option value="truck">🚚 Camion</option>
                    </select>
                    <button class="btn" onclick="registerVehicle()" data-translate="register">📝 Enregistrer</button>
                </div>
            </div>
        </div>

        <!-- My Vehicles -->
        <div class="card">
            <h2 class="card-title" data-translate="my_vehicles">🚗 Mes Véhicules</h2>
            <div id="clientVehiclesList">
                <p style="text-align: center; color: #9ca3af; padding: 2rem;" data-translate="no_vehicles">Aucun véhicule enregistré</p>
            </div>
        </div>

        <!-- Claims -->
        <div class="card">
            <h2 class="card-title" data-translate="submit_claim">🔍 Soumettre une Réclamation</h2>

            <!-- Wallet Verification Notice -->
            <div id="walletVerificationNotice" style="background: rgba(139, 92, 246, 0.1); border: 1px solid #8b5cf6; border-radius: 8px; padding: 1rem; margin-bottom: 2rem;">
                <h3 style="color: #8b5cf6; margin-bottom: 0.5rem;">🛡️ Wallet Vérifié par l'Assureur</h3>
                <p style="color: #e5e7eb; margin: 0; font-size: 0.9rem;">
                    Votre wallet a été assigné et vérifié par notre équipe d'assurance. Cela garantit l'authenticité de votre identité et la sécurité des transactions.
                </p>
            </div>

            <div class="grid">
                <div>
                    <select id="claimType" class="form-input">
                        <option value="">Type de sinistre</option>
                        <option value="accident">🚗 Accident de circulation</option>
                        <option value="theft">🔒 Vol du véhicule</option>
                        <option value="vandalism">💥 Vandalisme</option>
                        <option value="fire">🔥 Incendie</option>
                        <option value="natural">🌪️ Catastrophe naturelle</option>
                    </select>
                    <input type="date" id="claimDate" class="form-input">
                    <input type="number" id="claimAmount" class="form-input" placeholder="Montant estimé (ETH)" step="0.001">
                </div>
                <div>
                    <textarea id="claimDescription" class="form-input" rows="3" placeholder="Description détaillée du sinistre..."></textarea>

                    <!-- File Upload Section -->
                    <div style="margin: 1rem 0;">
                        <label style="color: #8b5cf6; font-weight: 600; margin-bottom: 0.5rem; display: block;">📎 Documents Justificatifs</label>
                        <input type="file" id="claimFiles" class="form-input" multiple accept=".pdf,.jpg,.jpeg,.png,.doc,.docx" style="padding: 0.5rem;">
                        <div style="font-size: 0.8rem; color: #9ca3af; margin-top: 0.5rem;">
                            Formats acceptés: PDF, Images (JPG, PNG), Documents Word. Max 5MB par fichier.
                        </div>
                    </div>

                    <button class="btn" onclick="submitClaim()" data-translate="submit">📤 Soumettre Réclamation</button>
                </div>
            </div>

            <!-- Uploaded Files Preview -->
            <div id="uploadedFilesPreview" style="margin-top: 2rem; display: none;">
                <h3 style="color: #8b5cf6;">📁 Fichiers Téléchargés</h3>
                <div id="filesList"></div>
            </div>
        </div>

        <!-- My Claims -->
        <div class="card">
            <h2 class="card-title" data-translate="my_claims">📋 Mes Réclamations</h2>
            <div id="clientClaimsList">
                <p style="text-align: center; color: #9ca3af; padding: 2rem;" data-translate="no_claims">Aucune réclamation trouvée</p>
            </div>
        </div>
    </div>

    <!-- INSURER SESSION -->
    <div id="insurerSession" class="session-screen">
        <div class="session-header">
            <div class="session-info">
                <h1 data-translate="insurer_dashboard">🏢 Dashboard Assureur</h1>
                <p data-translate="manage_policies">Gérez les polices et réclamations</p>
            </div>
            <div class="session-actions">
                <button class="btn btn-secondary" id="insurerReturnBtn" data-translate="return">🔙 Retour</button>
                <button class="btn" data-translate="refresh">🔄 Actualiser</button>
            </div>
        </div>

        <!-- Analytics -->
        <div class="card">
            <h2 class="card-title" data-translate="analytics">📊 Analytics</h2>
            <div class="grid">
                <div style="text-align: center; background: rgba(139, 92, 246, 0.1); padding: 1.5rem; border-radius: 12px;">
                    <div style="font-size: 2rem;">📋</div>
                    <div style="font-size: 1.5rem; font-weight: 600; color: #8b5cf6;" id="totalPolicies">1,247</div>
                    <div data-translate="active_policies">Polices Actives</div>
                </div>
                <div style="text-align: center; background: rgba(16, 185, 129, 0.1); padding: 1.5rem; border-radius: 12px;">
                    <div style="font-size: 2rem;">💰</div>
                    <div style="font-size: 1.5rem; font-weight: 600; color: #10b981;" id="totalPremiums">2.85M MAD</div>
                    <div data-translate="collected_premiums">Primes Collectées</div>
                </div>
                <div style="text-align: center; background: rgba(245, 158, 11, 0.1); padding: 1.5rem; border-radius: 12px;">
                    <div style="font-size: 2rem;">🔍</div>
                    <div style="font-size: 1.5rem; font-weight: 600; color: #f59e0b;" id="pendingClaims">23</div>
                    <div data-translate="pending_claims">Réclamations Pendantes</div>
                </div>
            </div>
        </div>

        <!-- Client Wallet Assignment -->
        <div class="card">
            <h2 class="card-title">👥 Assignation de Wallets Clients</h2>
            <div style="background: rgba(139, 92, 246, 0.1); border: 1px solid #8b5cf6; border-radius: 8px; padding: 1rem; margin-bottom: 2rem;">
                <h3 style="color: #8b5cf6; margin-bottom: 0.5rem;">🛡️ Système de Vérification des Clients</h3>
                <p style="color: #e5e7eb; margin: 0; font-size: 0.9rem;">
                    Les wallets sont assignés par l'assureur aux clients vérifiés. Cela garantit que seuls les vrais utilisateurs peuvent utiliser la plateforme et évite les fraudes.
                </p>
            </div>

            <div class="grid">
                <div>
                    <input type="text" id="clientWalletAddress" class="form-input" placeholder="Adresse Wallet Client (0x...)">
                    <input type="text" id="clientName" class="form-input" placeholder="Nom complet du client">
                    <input type="text" id="clientId" class="form-input" placeholder="CIN / Passeport">
                </div>
                <div>
                    <input type="tel" id="clientPhone" class="form-input" placeholder="Numéro de téléphone">
                    <input type="email" id="clientEmail" class="form-input" placeholder="Email du client">
                    <button class="btn" onclick="assignClientWallet()">👤 Assigner Wallet Client</button>
                </div>
            </div>

            <!-- Assigned Wallets List -->
            <div style="margin-top: 2rem;">
                <h3 style="color: #8b5cf6;">📋 Wallets Clients Assignés</h3>
                <div id="assignedWalletsList">
                    <p style="text-align: center; color: #9ca3af; padding: 1rem;">Chargement des wallets assignés...</p>
                </div>
            </div>
        </div>

        <!-- Claims Management -->
        <div class="card">
            <h2 class="card-title" data-translate="claims_management">🔍 Gestion des Réclamations</h2>
            <div id="insurerClaimsList">
                <p style="text-align: center; color: #9ca3af; padding: 2rem;">Chargement des réclamations...</p>
            </div>
        </div>
    </div>

    <!-- ADMIN SESSION -->
    <div id="adminSession" class="session-screen">
        <div class="session-header">
            <div class="session-info">
                <h1 data-translate="admin_dashboard">⚙️ Dashboard Admin</h1>
                <p data-translate="system_control">Contrôle total du système</p>
            </div>
            <div class="session-actions">
                <button class="btn btn-secondary" id="adminReturnBtn" data-translate="return">🔙 Retour</button>
                <button class="btn" data-translate="system_status">🔧 État Système</button>
            </div>
        </div>

        <!-- System Overview -->
        <div class="card">
            <h2 class="card-title" data-translate="system_overview">🏠 Vue d'ensemble Système</h2>
            <div class="grid">
                <div style="text-align: center; background: rgba(139, 92, 246, 0.1); padding: 1.5rem; border-radius: 12px;">
                    <div style="font-size: 2rem;">👥</div>
                    <div style="font-size: 1.5rem; font-weight: 600; color: #8b5cf6;">5,432</div>
                    <div data-translate="total_users">Utilisateurs Total</div>
                </div>
                <div style="text-align: center; background: rgba(16, 185, 129, 0.1); padding: 1.5rem; border-radius: 12px;">
                    <div style="font-size: 2rem;">⛓️</div>
                    <div style="font-size: 1.5rem; font-weight: 600; color: #10b981;">99.9%</div>
                    <div data-translate="blockchain_uptime">Uptime Blockchain</div>
                </div>
                <div style="text-align: center; background: rgba(245, 158, 11, 0.1); padding: 1.5rem; border-radius: 12px;">
                    <div style="font-size: 2rem;">💰</div>
                    <div style="font-size: 1.5rem; font-weight: 600; color: #f59e0b;">12.3M MAD</div>
                    <div data-translate="total_volume">Volume Total</div>
                </div>
            </div>
        </div>

        <!-- Blockchain Controls -->
        <div class="card">
            <h2 class="card-title" data-translate="blockchain_controls">⛓️ Contrôles Blockchain</h2>
            <div class="grid">
                <div>
                    <label data-translate="current_block">Bloc Actuel:</label>
                    <div id="adminCurrentBlock" class="form-input" style="background: rgba(0,0,0,0.4);">2,847,392</div>
                    <label data-translate="gas_price">Prix Gas:</label>
                    <div id="adminGasPrice" class="form-input" style="background: rgba(0,0,0,0.4);">23.5 gwei</div>
                </div>
                <div>
                    <label data-translate="active_nodes">Nœuds Actifs:</label>
                    <div id="adminActiveNodes" class="form-input" style="background: rgba(0,0,0,0.4);">8/8</div>
                    <button class="btn" onclick="restartBlockchain()" data-translate="restart_blockchain">🔄 Redémarrer Blockchain</button>
                </div>
            </div>
        </div>

        <!-- Identity Verification Management -->
        <div class="card">
            <h2 class="card-title">🆔 Gestion des Vérifications d'Identité</h2>
            <div style="background: rgba(139, 92, 246, 0.1); border: 1px solid #8b5cf6; border-radius: 8px; padding: 1rem; margin-bottom: 2rem;">
                <h3 style="color: #8b5cf6; margin-bottom: 0.5rem;">🔐 Processus de Vérification</h3>
                <p style="color: #e5e7eb; margin: 0; font-size: 0.9rem;">
                    Examinez les demandes de vérification d'identité et assignez des wallets sécurisés aux clients approuvés.
                </p>
            </div>

            <div id="verificationRequestsList">
                <p style="text-align: center; color: #9ca3af; padding: 2rem;">Chargement des demandes de vérification...</p>
            </div>
        </div>

        <!-- User Management -->
        <div class="card">
            <h2 class="card-title" data-translate="user_management">👥 Gestion Utilisateurs Vérifiés</h2>
            <div id="adminUsersList">
                <p style="text-align: center; color: #9ca3af; padding: 2rem;">Chargement des utilisateurs vérifiés...</p>
            </div>
        </div>
    </div>

    <!-- Rejection Modal -->
    <div id="rejectModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.8); z-index: 1000; align-items: center; justify-content: center;">
        <div style="background: #2d3748; border-radius: 16px; padding: 2rem; max-width: 500px; width: 90%; border: 1px solid #8b5cf6;">
            <h3 style="color: #8b5cf6; margin-bottom: 1rem;">❌ Rejeter la Réclamation</h3>
            <p style="color: #e5e7eb; margin-bottom: 1rem;">Veuillez indiquer la raison du rejet:</p>
            <textarea id="rejectionReason" style="width: 100%; padding: 0.75rem; border: 1px solid #4a5568; border-radius: 8px; background: #374151; color: #e5e7eb; margin-bottom: 1rem; min-height: 100px;" placeholder="Raison du rejet..."></textarea>
            <div style="display: flex; gap: 1rem; justify-content: flex-end;">
                <button class="btn btn-secondary" onclick="closeRejectModal()">Annuler</button>
                <button class="btn" onclick="confirmRejectClaim()" style="background: #ef4444;">Confirmer le Rejet</button>
            </div>
        </div>
    </div>

    <!-- Web3 and JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/web3@4.2.0/dist/web3.min.js"></script>
    <script>
        // Application State
        const HimayaApp = {
            web3: null,
            account: null,
            contract: null,
            currentSession: null,
            currentLanguage: 'fr',
            connected: false,
            uploadedFiles: [],
            currentClaimId: null,
            verificationData: {
                personalInfo: {},
                documents: {},
                contactPrefs: {},
                submissionId: null,
                status: 'not_submitted' // not_submitted, pending, approved, rejected
            }
        };

        // Smart Contract Configuration - REAL CONTRACT
        const CONTRACT_CONFIG = {
            address: '0x5FbDB2315678afecb367f032d93F642f64180aa3', // Local Hardhat deployment
            abi: [
                {
                    "inputs": [{"type": "string", "name": "_planType"}],
                    "name": "subscribeToPlan",
                    "outputs": [],
                    "stateMutability": "payable",
                    "type": "function"
                },
                {
                    "inputs": [
                        {"type": "string", "name": "_vehicleId"},
                        {"type": "string", "name": "_make"},
                        {"type": "string", "name": "_model"},
                        {"type": "uint256", "name": "_year"},
                        {"type": "string", "name": "_vehicleType"},
                        {"type": "string", "name": "_city"}
                    ],
                    "name": "registerVehicle",
                    "outputs": [],
                    "type": "function"
                },
                {
                    "inputs": [
                        {"type": "string", "name": "_claimId"},
                        {"type": "string", "name": "_vehicleId"},
                        {"type": "string", "name": "_claimType"},
                        {"type": "string", "name": "_description"},
                        {"type": "uint256", "name": "_amount"}
                    ],
                    "name": "submitClaim",
                    "outputs": [],
                    "type": "function"
                },
                {
                    "inputs": [{"type": "string", "name": "_claimId"}, {"type": "bool", "name": "_approved"}],
                    "name": "processClaim",
                    "outputs": [],
                    "type": "function"
                },
                {
                    "inputs": [{"type": "address", "name": "_user"}],
                    "name": "getUserVehicles",
                    "outputs": [{"type": "string[]"}],
                    "type": "function"
                },
                {
                    "inputs": [{"type": "address", "name": "_user"}],
                    "name": "getUserSubscription",
                    "outputs": [{"type": "tuple", "components": [
                        {"type": "string", "name": "planType"},
                        {"type": "uint256", "name": "subscribedAt"},
                        {"type": "uint256", "name": "expiresAt"},
                        {"type": "uint256", "name": "paidAmount"},
                        {"type": "bool", "name": "isActive"}
                    ]}],
                    "type": "function"
                },
                {
                    "inputs": [{"type": "string", "name": "_vehicleId"}],
                    "name": "getVehicle",
                    "outputs": [{"type": "tuple", "components": [
                        {"type": "string", "name": "vehicleId"},
                        {"type": "string", "name": "make"},
                        {"type": "string", "name": "model"},
                        {"type": "uint256", "name": "year"},
                        {"type": "string", "name": "vehicleType"},
                        {"type": "string", "name": "city"},
                        {"type": "address", "name": "owner"},
                        {"type": "string", "name": "planType"},
                        {"type": "uint256", "name": "registeredAt"},
                        {"type": "bool", "name": "isActive"}
                    ]}],
                    "type": "function"
                },
                {
                    "inputs": [{"type": "string", "name": "_claimId"}],
                    "name": "getClaim",
                    "outputs": [{"type": "tuple", "components": [
                        {"type": "string", "name": "claimId"},
                        {"type": "address", "name": "claimant"},
                        {"type": "string", "name": "vehicleId"},
                        {"type": "string", "name": "claimType"},
                        {"type": "string", "name": "description"},
                        {"type": "uint256", "name": "amount"},
                        {"type": "uint256", "name": "submittedAt"},
                        {"type": "bool", "name": "processed"},
                        {"type": "bool", "name": "approved"},
                        {"type": "uint256", "name": "paidAmount"},
                        {"type": "uint256", "name": "processedAt"}
                    ]}],
                    "type": "function"
                },
                {
                    "inputs": [],
                    "name": "getPendingClaims",
                    "outputs": [{"type": "string[]"}],
                    "type": "function"
                },
                {
                    "inputs": [{"type": "address", "name": "_user"}],
                    "name": "getClaimsByUser",
                    "outputs": [{"type": "string[]"}],
                    "type": "function"
                },
                {
                    "inputs": [],
                    "name": "getContractStats",
                    "outputs": [
                        {"type": "uint256", "name": "totalPremiums"},
                        {"type": "uint256", "name": "totalClaims"},
                        {"type": "uint256", "name": "activeUsers"},
                        {"type": "uint256", "name": "balance"},
                        {"type": "uint256", "name": "totalVehicles"},
                        {"type": "uint256", "name": "totalClaimsCount"}
                    ],
                    "type": "function"
                },
                {
                    "inputs": [],
                    "name": "getContractBalance",
                    "outputs": [{"type": "uint256"}],
                    "type": "function"
                }
            ]
        };

        // Multi-language translations
        const translations = {
            fr: {
                welcome: "🛡️ Bienvenue sur Himaya",
                choose_session: "Choisissez votre type de session pour continuer",
                client_session: "Session Client",
                client_desc: "Gérez vos assurances véhicules, souscrivez aux plans et suivez vos réclamations",
                insurer_session: "Session Assureur",
                insurer_desc: "Gérez les polices, approuvez les réclamations et analysez les données",
                admin_session: "Session Admin",
                admin_desc: "Administration complète du système et monitoring blockchain",
                access: "Accéder",
                return: "🔙 Retour",
                connect_wallet: "🦊 Connecter Wallet",
                client_dashboard: "👤 Dashboard Client",
                manage_insurance: "Gérez vos assurances et véhicules",
                wallet_connection: "🔗 Connexion Blockchain",
                wallet_address: "Adresse Wallet:",
                balance: "Solde:",
                network: "Réseau:",
                status: "Statut:",
                insurance_plans: "📋 Plans d'Assurance",
                subscribe: "Souscrire",
                register_vehicle: "🚗 Enregistrer un Véhicule",
                register: "📝 Enregistrer",
                my_vehicles: "🚗 Mes Véhicules",
                no_vehicles: "Aucun véhicule enregistré",
                submit_claim: "🔍 Soumettre une Réclamation",
                submit: "📤 Soumettre"
            },
            ar: {
                welcome: "🛡️ مرحباً بكم في حماية",
                choose_session: "اختر نوع الجلسة للمتابعة",
                client_session: "جلسة العميل",
                client_desc: "إدارة تأمين المركبات والاشتراك في الخطط ومتابعة المطالبات",
                insurer_session: "جلسة شركة التأمين",
                insurer_desc: "إدارة البوالص والموافقة على المطالبات وتحليل البيانات",
                admin_session: "جلسة المدير",
                admin_desc: "إدارة كاملة للنظام ومراقبة البلوك تشين",
                access: "دخول",
                return: "🔙 عودة",
                connect_wallet: "🦊 ربط المحفظة",
                client_dashboard: "👤 لوحة العميل",
                manage_insurance: "إدارة التأمين والمركبات",
                wallet_connection: "🔗 اتصال البلوك تشين",
                wallet_address: "عنوان المحفظة:",
                balance: "الرصيد:",
                network: "الشبكة:",
                status: "الحالة:",
                insurance_plans: "📋 خطط التأمين",
                subscribe: "اشتراك",
                register_vehicle: "🚗 تسجيل مركبة",
                register: "📝 تسجيل",
                my_vehicles: "🚗 مركباتي",
                no_vehicles: "لا توجد مركبات مسجلة",
                submit_claim: "🔍 تقديم مطالبة",
                submit: "📤 تقديم"
            },
            en: {
                welcome: "🛡️ Welcome to Himaya",
                choose_session: "Choose your session type to continue",
                client_session: "Client Session",
                client_desc: "Manage your vehicle insurance, subscribe to plans and track claims",
                insurer_session: "Insurer Session",
                insurer_desc: "Manage policies, approve claims and analyze data",
                admin_session: "Admin Session",
                admin_desc: "Complete system administration and blockchain monitoring",
                access: "Access",
                return: "🔙 Return",
                connect_wallet: "🦊 Connect Wallet",
                client_dashboard: "👤 Client Dashboard",
                manage_insurance: "Manage your insurance and vehicles",
                wallet_connection: "🔗 Blockchain Connection",
                wallet_address: "Wallet Address:",
                balance: "Balance:",
                network: "Network:",
                status: "Status:",
                insurance_plans: "📋 Insurance Plans",
                subscribe: "Subscribe",
                register_vehicle: "🚗 Register Vehicle",
                register: "📝 Register",
                my_vehicles: "🚗 My Vehicles",
                no_vehicles: "No vehicles registered",
                submit_claim: "🔍 Submit Claim",
                submit: "📤 Submit"
            }
        };

        // Utility Functions
        function showNotification(message, type = 'info') {
            const existing = document.querySelectorAll('.notification');
            existing.forEach(n => n.remove());

            const notification = document.createElement('div');
            notification.className = 'notification';
            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 4000);
        }

        function updateLanguage(lang) {
            HimayaApp.currentLanguage = lang;

            // Update language buttons
            document.querySelectorAll('.lang-btn').forEach(btn => {
                btn.classList.remove('active');
                if (btn.getAttribute('data-lang') === lang) {
                    btn.classList.add('active');
                }
            });

            // Update all translatable elements
            document.querySelectorAll('[data-translate]').forEach(element => {
                const key = element.getAttribute('data-translate');
                if (translations[lang] && translations[lang][key]) {
                    element.textContent = translations[lang][key];
                }
            });

            // Update placeholders
            document.querySelectorAll('[data-translate-placeholder]').forEach(element => {
                const key = element.getAttribute('data-translate-placeholder');
                if (translations[lang] && translations[lang][key]) {
                    element.placeholder = translations[lang][key];
                }
            });
        }

        // Screen Management
        function showScreen(screenId) {
            // Hide all screens
            document.getElementById('splashScreen').style.display = 'none';
            document.getElementById('loginScreen').style.display = 'none';
            document.getElementById('identityVerificationScreen').style.display = 'none';
            document.getElementById('verificationStatusScreen').style.display = 'none';
            document.getElementById('clientSession').style.display = 'none';
            document.getElementById('insurerSession').style.display = 'none';
            document.getElementById('adminSession').style.display = 'none';

            // Show target screen
            document.getElementById(screenId).style.display = 'block';
        }

        function switchToSession(sessionType) {
            HimayaApp.currentSession = sessionType;
            showScreen(sessionType + 'Session');
            showNotification(`Session ${sessionType} activée`);

            // Load real data when entering sessions
            if (HimayaApp.connected) {
                if (sessionType === 'client') {
                    loadRealUserData();
                } else if (sessionType === 'insurer') {
                    loadRealInsurerClaims();
                    loadRealContractStats();
                    loadAssignedWallets();
                } else if (sessionType === 'admin') {
                    loadRealContractStats();
                    loadVerificationRequests();
                }
            } else if (sessionType === 'admin') {
                // Admin can view verification requests without wallet connection
                loadVerificationRequests();
            }
        }

        // Blockchain Functions
        async function connectWallet() {
            if (!window.ethereum) {
                showNotification('❌ MetaMask non détecté. Veuillez installer MetaMask.');
                return;
            }

            try {
                showNotification('🔄 Connexion au wallet...');

                const accounts = await window.ethereum.request({
                    method: 'eth_requestAccounts'
                });

                if (accounts.length > 0) {
                    HimayaApp.account = accounts[0];
                    HimayaApp.web3 = new Web3(window.ethereum);
                    HimayaApp.connected = true;

                    // Initialize contract
                    HimayaApp.contract = new HimayaApp.web3.eth.Contract(
                        CONTRACT_CONFIG.abi,
                        CONTRACT_CONFIG.address
                    );

                    const balance = await HimayaApp.web3.eth.getBalance(HimayaApp.account);
                    const ethBalance = HimayaApp.web3.utils.fromWei(balance, 'ether');

                    // Update UI based on current session
                    if (HimayaApp.currentSession === 'client') {
                        document.getElementById('clientWalletAddress').textContent =
                            `${HimayaApp.account.slice(0, 6)}...${HimayaApp.account.slice(-4)}`;
                        document.getElementById('clientBalance').textContent =
                            `${parseFloat(ethBalance).toFixed(4)} ETH`;
                        document.getElementById('clientNetwork').textContent = 'Himaya Network';
                        document.getElementById('clientStatus').textContent = 'Connecté ✅';
                    }

                    showNotification('🎉 Wallet connecté avec succès!');
                    await loadRealUserData();
                }
            } catch (error) {
                console.error('Wallet connection failed:', error);
                showNotification('❌ Échec de la connexion au wallet');
            }
        }

        // REAL Blockchain Interaction Functions with ACTUAL FUND TRANSFERS
        async function subscribePlan(planType) {
            if (!HimayaApp.connected) {
                showNotification('⚠️ Veuillez d\'abord connecter votre wallet');
                return;
            }

            try {
                showNotification('🔄 Souscription au plan avec transfert de fonds...');

                // Real ETH prices for plans
                const planPrices = { basic: '0.05', standard: '0.08', premium: '0.12' };
                const priceWei = HimayaApp.web3.utils.toWei(planPrices[planType], 'ether');

                // Get user's current balance to verify they have enough funds
                const balance = await HimayaApp.web3.eth.getBalance(HimayaApp.account);
                const balanceEth = parseFloat(HimayaApp.web3.utils.fromWei(balance, 'ether'));
                const requiredEth = parseFloat(planPrices[planType]);

                if (balanceEth < requiredEth) {
                    showNotification(`❌ Solde insuffisant. Requis: ${requiredEth} ETH, Disponible: ${balanceEth.toFixed(4)} ETH`);
                    return;
                }

                // Execute REAL blockchain transaction with ETH transfer
                const tx = await HimayaApp.contract.methods.subscribeToPlan(planType).send({
                    from: HimayaApp.account,
                    value: priceWei,
                    gas: 500000,
                    gasPrice: await HimayaApp.web3.eth.getGasPrice()
                });

                showNotification(`🎉 Plan ${planType} souscrit! Fonds transférés: ${planPrices[planType]} ETH`);
                console.log('Transaction hash:', tx.transactionHash);
                console.log('Funds transferred:', planPrices[planType], 'ETH');

                // Refresh user data to show updated subscription
                await loadRealUserData();

            } catch (error) {
                console.error('Plan subscription failed:', error);
                if (error.message.includes('insufficient funds')) {
                    showNotification('❌ Fonds insuffisants pour la transaction');
                } else if (error.message.includes('User denied')) {
                    showNotification('❌ Transaction annulée par l\'utilisateur');
                } else {
                    showNotification('❌ Échec de la souscription au plan');
                }
            }
        }

        async function registerVehicle() {
            if (!HimayaApp.connected) {
                showNotification('⚠️ Veuillez d\'abord connecter votre wallet');
                return;
            }

            const vehicleVin = document.getElementById('vehicleVin').value;
            const vehicleMake = document.getElementById('vehicleMake').value;
            const vehicleModel = document.getElementById('vehicleModel').value;
            const vehicleYear = parseInt(document.getElementById('vehicleYear').value);
            const vehicleType = document.getElementById('vehicleType').value;

            if (!vehicleVin || !vehicleMake || !vehicleModel || !vehicleYear || !vehicleType) {
                showNotification('⚠️ Veuillez remplir tous les champs');
                return;
            }

            if (vehicleYear < 1990 || vehicleYear > 2024) {
                showNotification('⚠️ Année du véhicule invalide (1990-2024)');
                return;
            }

            try {
                showNotification('🔄 Enregistrement du véhicule sur la blockchain...');

                // Check if user has active subscription
                const subscription = await HimayaApp.contract.methods.getUserSubscription(HimayaApp.account).call();
                if (!subscription.isActive || subscription.expiresAt * 1000 < Date.now()) {
                    showNotification('❌ Vous devez avoir un plan d\'assurance actif pour enregistrer un véhicule');
                    return;
                }

                const vehicleId = `${vehicleVin}-${Date.now()}`;
                const city = 'Casablanca'; // Default city, could be made dynamic

                // REAL blockchain transaction to store vehicle data
                const tx = await HimayaApp.contract.methods.registerVehicle(
                    vehicleId,
                    vehicleMake,
                    vehicleModel,
                    vehicleYear,
                    vehicleType,
                    city
                ).send({
                    from: HimayaApp.account,
                    gas: 500000,
                    gasPrice: await HimayaApp.web3.eth.getGasPrice()
                });

                // Clear form
                document.getElementById('vehicleVin').value = '';
                document.getElementById('vehicleMake').value = '';
                document.getElementById('vehicleModel').value = '';
                document.getElementById('vehicleYear').value = '';
                document.getElementById('vehicleType').value = '';

                showNotification('🎉 Véhicule enregistré sur la blockchain avec succès!');
                console.log('Vehicle registration transaction hash:', tx.transactionHash);
                console.log('Vehicle data stored on blockchain:', {vehicleId, vehicleMake, vehicleModel, vehicleYear, vehicleType});

                // Reload real vehicle data from blockchain
                await loadRealUserVehicles();

            } catch (error) {
                console.error('Vehicle registration failed:', error);
                if (error.message.includes('No active plan')) {
                    showNotification('❌ Plan d\'assurance requis pour enregistrer un véhicule');
                } else if (error.message.includes('User denied')) {
                    showNotification('❌ Transaction annulée par l\'utilisateur');
                } else {
                    showNotification('❌ Échec de l\'enregistrement du véhicule');
                }
            }
        }

        async function submitClaim() {
            if (!HimayaApp.connected) {
                showNotification('⚠️ Veuillez d\'abord connecter votre wallet');
                return;
            }

            const claimType = document.getElementById('claimType').value;
            const claimDate = document.getElementById('claimDate').value;
            const claimAmount = document.getElementById('claimAmount').value;
            const claimDescription = document.getElementById('claimDescription').value;

            if (!claimType || !claimDate || !claimAmount || !claimDescription) {
                showNotification('⚠️ Veuillez remplir tous les champs');
                return;
            }

            if (parseFloat(claimAmount) <= 0) {
                showNotification('⚠️ Le montant doit être supérieur à 0');
                return;
            }

            try {
                showNotification('🔄 Soumission de la réclamation sur la blockchain...');

                // Get user's vehicles to select the first one (in real app, user would select)
                const userVehicles = await HimayaApp.contract.methods.getUserVehicles(HimayaApp.account).call();
                if (userVehicles.length === 0) {
                    showNotification('❌ Vous devez d\'abord enregistrer un véhicule');
                    return;
                }

                const claimId = `CLM-${Date.now()}`;
                const vehicleId = userVehicles[0]; // Use first vehicle
                const amountWei = HimayaApp.web3.utils.toWei(claimAmount, 'ether');

                // REAL blockchain transaction to store claim data
                const tx = await HimayaApp.contract.methods.submitClaim(
                    claimId,
                    vehicleId,
                    claimType,
                    claimDescription,
                    amountWei
                ).send({
                    from: HimayaApp.account,
                    gas: 500000,
                    gasPrice: await HimayaApp.web3.eth.getGasPrice()
                });

                // Store claim ID for file uploads
                HimayaApp.currentClaimId = claimId;

                // Upload files to blockchain if any
                await uploadFilesToBlockchain(claimId);

                // Clear form
                document.getElementById('claimType').value = '';
                document.getElementById('claimDate').value = '';
                document.getElementById('claimAmount').value = '';
                document.getElementById('claimDescription').value = '';
                document.getElementById('claimFiles').value = '';
                document.getElementById('uploadedFilesPreview').style.display = 'none';
                HimayaApp.uploadedFiles = [];

                showNotification('🎉 Réclamation et fichiers soumis sur la blockchain avec succès!');
                console.log('Claim submission transaction hash:', tx.transactionHash);
                console.log('Claim data stored on blockchain:', {claimId, vehicleId, claimType, claimAmount});

                // Reload real claims data
                await loadRealUserClaims();

            } catch (error) {
                console.error('Claim submission failed:', error);
                if (error.message.includes('No active plan')) {
                    showNotification('❌ Plan d\'assurance actif requis');
                } else if (error.message.includes('exceeds plan limit')) {
                    showNotification('❌ Montant dépasse la limite de votre plan');
                } else if (error.message.includes('User denied')) {
                    showNotification('❌ Transaction annulée par l\'utilisateur');
                } else {
                    showNotification('❌ Échec de la soumission de la réclamation');
                }
            }
        }

        async function approveClaim(claimId) {
            if (!HimayaApp.connected) {
                showNotification('⚠️ Veuillez d\'abord connecter votre wallet');
                return;
            }

            try {
                showNotification('🔄 Approbation de la réclamation avec transfert de fonds...');

                // Get claim details to show amount being transferred
                const claimData = await HimayaApp.contract.methods.getClaim(claimId).call();
                const amountEth = HimayaApp.web3.utils.fromWei(claimData.amount, 'ether');

                // Check contract balance
                const contractBalance = await HimayaApp.contract.methods.getContractBalance().call();
                const balanceEth = HimayaApp.web3.utils.fromWei(contractBalance, 'ether');

                if (parseFloat(balanceEth) < parseFloat(amountEth)) {
                    showNotification(`❌ Solde du contrat insuffisant. Requis: ${amountEth} ETH, Disponible: ${balanceEth} ETH`);
                    return;
                }

                // REAL blockchain transaction that transfers ETH to claimant
                const tx = await HimayaApp.contract.methods.processClaim(claimId, true).send({
                    from: HimayaApp.account,
                    gas: 500000,
                    gasPrice: await HimayaApp.web3.eth.getGasPrice()
                });

                showNotification(`🎉 Réclamation approuvée! ${amountEth} ETH transférés au client!`);
                console.log('Claim approval transaction hash:', tx.transactionHash);
                console.log(`Funds transferred to claimant: ${amountEth} ETH`);
                console.log(`Claimant address: ${claimData.claimant}`);

                // Reload real claims data
                await loadRealInsurerClaims();

            } catch (error) {
                console.error('Claim approval failed:', error);
                if (error.message.includes('Insufficient contract balance')) {
                    showNotification('❌ Solde du contrat insuffisant pour le paiement');
                } else if (error.message.includes('Only owner')) {
                    showNotification('❌ Seul le propriétaire peut approuver les réclamations');
                } else if (error.message.includes('User denied')) {
                    showNotification('❌ Transaction annulée par l\'utilisateur');
                } else {
                    showNotification('❌ Échec de l\'approbation');
                }
            }
        }

        // REJECTION MODAL FUNCTIONS
        let currentRejectClaimId = null;

        function showRejectModal(claimId) {
            currentRejectClaimId = claimId;
            document.getElementById('rejectModal').style.display = 'flex';
            document.getElementById('rejectionReason').value = '';
            document.getElementById('rejectionReason').focus();
        }

        function closeRejectModal() {
            document.getElementById('rejectModal').style.display = 'none';
            currentRejectClaimId = null;
        }

        async function confirmRejectClaim() {
            const rejectionReason = document.getElementById('rejectionReason').value.trim();

            if (!rejectionReason) {
                showNotification('⚠️ Veuillez indiquer une raison pour le rejet');
                return;
            }

            try {
                showNotification('🔄 Rejet de la réclamation avec raison...');

                const tx = await HimayaApp.contract.methods.processClaim(
                    currentRejectClaimId,
                    false,
                    rejectionReason
                ).send({
                    from: HimayaApp.account,
                    gas: 500000
                });

                closeRejectModal();
                showNotification(`❌ Réclamation ${currentRejectClaimId} rejetée`);
                console.log('Rejection transaction hash:', tx.transactionHash);

                // Reload claims
                await loadRealInsurerClaims();

            } catch (error) {
                console.error('Claim rejection failed:', error);
                showNotification('❌ Échec du rejet de la réclamation');
            }
        }

        async function rejectClaim(claimId) {
            showRejectModal(claimId);
        }

        // REAL DATA LOADING FUNCTIONS
        async function loadRealUserData() {
            if (!HimayaApp.connected) return;

            try {
                await Promise.all([
                    loadRealUserSubscription(),
                    loadRealUserVehicles(),
                    loadRealUserClaims(),
                    loadRealContractStats()
                ]);
                console.log('✅ All real user data loaded from blockchain');
            } catch (error) {
                console.error('Failed to load real user data:', error);
            }
        }

        async function loadRealUserSubscription() {
            if (!HimayaApp.connected) return;

            try {
                const subscription = await HimayaApp.contract.methods.getUserSubscription(HimayaApp.account).call();

                if (subscription.isActive && subscription.expiresAt * 1000 > Date.now()) {
                    const expiryDate = new Date(subscription.expiresAt * 1000);
                    const paidAmount = HimayaApp.web3.utils.fromWei(subscription.paidAmount, 'ether');

                    console.log('✅ Active subscription found:', {
                        planType: subscription.planType,
                        expiresAt: expiryDate,
                        paidAmount: paidAmount + ' ETH'
                    });

                    showNotification(`✅ Plan ${subscription.planType} actif jusqu'au ${expiryDate.toLocaleDateString()}`);
                }
            } catch (error) {
                console.error('Failed to load subscription:', error);
            }
        }

        async function loadRealUserVehicles() {
            if (!HimayaApp.connected) return;

            try {
                const vehicleIds = await HimayaApp.contract.methods.getUserVehicles(HimayaApp.account).call();

                const vehiclesList = document.getElementById('clientVehiclesList');
                if (vehicleIds.length === 0) {
                    vehiclesList.innerHTML = '<p style="text-align: center; color: #9ca3af; padding: 2rem;">Aucun véhicule enregistré sur la blockchain</p>';
                } else {
                    // Load detailed vehicle data for each vehicle
                    const vehicleDetails = await Promise.all(
                        vehicleIds.map(id => HimayaApp.contract.methods.getVehicle(id).call())
                    );

                    vehiclesList.innerHTML = vehicleDetails.map(vehicle => `
                        <div style="background: #374151; padding: 1.5rem; border-radius: 12px; margin-bottom: 1rem; border: 1px solid #8b5cf6;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                                <h3 style="color: #8b5cf6; margin: 0;">${vehicle.make} ${vehicle.model}</h3>
                                <span style="background: #10b981; color: white; padding: 0.25rem 0.75rem; border-radius: 12px; font-size: 0.8rem;">
                                    ${vehicle.planType.toUpperCase()}
                                </span>
                            </div>
                            <div style="grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); display: grid; gap: 0.5rem; color: #e5e7eb;">
                                <div><strong>Année:</strong> ${vehicle.year}</div>
                                <div><strong>Type:</strong> ${vehicle.vehicleType}</div>
                                <div><strong>Ville:</strong> ${vehicle.city}</div>
                                <div><strong>Enregistré:</strong> ${new Date(vehicle.registeredAt * 1000).toLocaleDateString()}</div>
                            </div>
                            <div style="margin-top: 1rem; font-size: 0.9rem; color: #9ca3af;">
                                🔗 ID Blockchain: ${vehicle.vehicleId}
                            </div>
                        </div>
                    `).join('');

                    console.log('✅ Loaded', vehicleDetails.length, 'vehicles from blockchain');
                }
            } catch (error) {
                console.error('Failed to load real vehicles:', error);
            }
        }

        async function loadRealUserClaims() {
            if (!HimayaApp.connected) return;

            try {
                const claimIds = await HimayaApp.contract.methods.getClaimsByUser(HimayaApp.account).call();

                if (claimIds.length === 0) {
                    console.log('No claims found for user');
                    return;
                }

                // Load detailed claim data
                const claimDetails = await Promise.all(
                    claimIds.map(id => HimayaApp.contract.methods.getClaim(id).call())
                );

                console.log('✅ Loaded', claimDetails.length, 'claims from blockchain:', claimDetails);

            } catch (error) {
                console.error('Failed to load real claims:', error);
            }
        }

        async function loadRealInsurerClaims() {
            if (!HimayaApp.connected) return;

            try {
                const pendingClaimIds = await HimayaApp.contract.methods.getPendingClaims().call();

                if (pendingClaimIds.length === 0) {
                    document.getElementById('insurerClaimsList').innerHTML =
                        '<p style="text-align: center; color: #9ca3af; padding: 2rem;">Aucune réclamation pendante</p>';
                    return;
                }

                // Load detailed claim data
                const claimDetails = await Promise.all(
                    pendingClaimIds.map(id => HimayaApp.contract.methods.getClaim(id).call())
                );

                document.getElementById('insurerClaimsList').innerHTML = claimDetails.map(claim => {
                    const amountEth = HimayaApp.web3.utils.fromWei(claim.amount, 'ether');
                    const submittedDate = new Date(claim.submittedAt * 1000);

                    return `
                        <div style="background: #374151; padding: 1.5rem; border-radius: 8px; margin-bottom: 1rem; border: 1px solid #4a5568;">
                            <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 1rem;">
                                <div style="flex: 1;">
                                    <div style="display: flex; align-items: center; gap: 1rem; margin-bottom: 0.5rem;">
                                        <strong style="color: #8b5cf6; font-size: 1.1rem;">#${claim.claimId}</strong>
                                        <span style="background: rgba(245, 158, 11, 0.2); color: #f59e0b; padding: 0.25rem 0.75rem; border-radius: 12px; font-size: 0.8rem;">
                                            ${claim.claimType}
                                        </span>
                                    </div>
                                    <div style="color: #9ca3af; font-size: 0.9rem; margin-bottom: 0.5rem;">
                                        👤 Client: ${claim.claimant.slice(0, 6)}...${claim.claimant.slice(-4)} |
                                        💰 Montant: ${amountEth} ETH |
                                        📅 Soumis: ${submittedDate.toLocaleDateString()}
                                    </div>
                                    <div style="color: #e5e7eb; margin-bottom: 1rem; padding: 0.75rem; background: rgba(0,0,0,0.3); border-radius: 6px;">
                                        ${claim.description}
                                    </div>
                                    ${claim.fileHashes && claim.fileHashes.length > 0 ? `
                                        <div style="margin-bottom: 1rem;">
                                            <div style="color: #8b5cf6; font-weight: 600; margin-bottom: 0.5rem;">📎 Documents joints (${claim.fileHashes.length}):</div>
                                            <div style="display: flex; flex-wrap: wrap; gap: 0.5rem;">
                                                ${claim.fileHashes.map(hash => `
                                                    <div style="background: rgba(139, 92, 246, 0.1); border: 1px solid #8b5cf6; padding: 0.5rem; border-radius: 6px; font-size: 0.8rem;">
                                                        📄 ${hash.substring(0, 20)}...
                                                    </div>
                                                `).join('')}
                                            </div>
                                        </div>
                                    ` : ''}
                                </div>
                                <div style="display: flex; flex-direction: column; gap: 0.5rem; min-width: 200px;">
                                    <button class="btn" onclick="approveClaim('${claim.claimId}')" style="background: #10b981; width: 100%;">
                                        ✅ Approuver (${amountEth} ETH)
                                    </button>
                                    <button class="btn" onclick="showRejectModal('${claim.claimId}')" style="background: #ef4444; width: 100%;">
                                        ❌ Rejeter
                                    </button>
                                    <button class="btn" onclick="viewClaimDetails('${claim.claimId}')" style="background: #6b7280; width: 100%;">
                                        👁️ Détails
                                    </button>
                                </div>
                            </div>
                        </div>
                    `;
                }).join('');

                console.log('✅ Loaded', claimDetails.length, 'pending claims for insurer');

            } catch (error) {
                console.error('Failed to load insurer claims:', error);
            }
        }

        async function loadRealContractStats() {
            if (!HimayaApp.connected) return;

            try {
                const stats = await HimayaApp.contract.methods.getContractStats().call();

                const totalPremiumsEth = HimayaApp.web3.utils.fromWei(stats.totalPremiums, 'ether');
                const totalClaimsEth = HimayaApp.web3.utils.fromWei(stats.totalClaims, 'ether');
                const contractBalanceEth = HimayaApp.web3.utils.fromWei(stats.balance, 'ether');

                // Update insurer dashboard with real data
                if (document.getElementById('totalPolicies')) {
                    document.getElementById('totalPolicies').textContent = stats.totalVehicles;
                }
                if (document.getElementById('totalPremiums')) {
                    document.getElementById('totalPremiums').textContent = `${parseFloat(totalPremiumsEth).toFixed(4)} ETH`;
                }
                if (document.getElementById('pendingClaims')) {
                    document.getElementById('pendingClaims').textContent = stats.totalClaimsCount;
                }

                console.log('✅ Real contract stats loaded:', {
                    totalPremiums: totalPremiumsEth + ' ETH',
                    totalClaims: totalClaimsEth + ' ETH',
                    activeUsers: stats.activeUsers,
                    contractBalance: contractBalanceEth + ' ETH',
                    totalVehicles: stats.totalVehicles,
                    totalClaimsCount: stats.totalClaimsCount
                });

            } catch (error) {
                console.error('Failed to load contract stats:', error);
            }
        }

        async function restartBlockchain() {
            showNotification('🔄 Redémarrage de la blockchain...');
            // Simulate blockchain restart
            setTimeout(() => {
                showNotification('✅ Blockchain redémarrée avec succès');
            }, 3000);
        }

        // Event Listeners and Initialization
        function initEventListeners() {
            // Language buttons
            document.querySelectorAll('.lang-btn').forEach(btn => {
                btn.addEventListener('click', () => {
                    const lang = btn.getAttribute('data-lang');
                    updateLanguage(lang);
                });
            });

            // Session cards
            document.querySelectorAll('.session-card').forEach(card => {
                card.addEventListener('click', () => {
                    const sessionType = card.getAttribute('data-session');
                    if (sessionType === 'new-client') {
                        showIdentityVerification();
                    } else {
                        switchToSession(sessionType);
                    }
                });
            });

            // Return buttons
            document.getElementById('clientReturnBtn').addEventListener('click', () => {
                showScreen('loginScreen');
                HimayaApp.currentSession = null;
            });

            document.getElementById('insurerReturnBtn').addEventListener('click', () => {
                showScreen('loginScreen');
                HimayaApp.currentSession = null;
            });

            document.getElementById('adminReturnBtn').addEventListener('click', () => {
                showScreen('loginScreen');
                HimayaApp.currentSession = null;
            });

            // Wallet connection buttons
            document.getElementById('clientWalletBtn').addEventListener('click', connectWallet);

            // File upload handling
            const fileInput = document.getElementById('claimFiles');
            if (fileInput) {
                fileInput.addEventListener('change', handleFileUpload);
            }

            // MetaMask event listeners
            if (window.ethereum) {
                window.ethereum.on('accountsChanged', (accounts) => {
                    if (accounts.length === 0) {
                        HimayaApp.connected = false;
                        HimayaApp.account = null;
                        showNotification('⚠️ Wallet déconnecté');

                        // Reset UI
                        if (HimayaApp.currentSession === 'client') {
                            document.getElementById('clientWalletAddress').textContent = 'Non connecté';
                            document.getElementById('clientBalance').textContent = '0.0000 ETH';
                            document.getElementById('clientNetwork').textContent = 'Non connecté';
                            document.getElementById('clientStatus').textContent = 'Déconnecté';
                        }
                    } else {
                        HimayaApp.account = accounts[0];
                        showNotification('🔄 Compte changé');
                        if (HimayaApp.connected) {
                            connectWallet(); // Refresh connection
                        }
                    }
                });

                window.ethereum.on('chainChanged', (chainId) => {
                    showNotification('🔄 Réseau changé, rechargement...');
                    setTimeout(() => window.location.reload(), 2000);
                });
            }
        }

        // Application Initialization
        function initApp() {
            console.log('🚀 Initializing Himaya Blockchain Insurance DApp...');

            // Show splash screen for 3 seconds
            setTimeout(() => {
                showScreen('loginScreen');
                showNotification('🛡️ Bienvenue sur Himaya Blockchain Insurance');

                // Check for existing verification
                checkExistingVerification();
            }, 3000);

            // Initialize event listeners
            initEventListeners();

            // Set default language
            updateLanguage('fr');

            console.log('✅ Himaya Blockchain Insurance DApp initialized successfully');
        }

        // Start the application when DOM is loaded
        document.addEventListener('DOMContentLoaded', initApp);

        // Global functions for console testing
        window.HimayaBlockchain = {
            connectWallet,
            subscribePlan,
            registerVehicle,
            submitClaim,
            approveClaim,
            rejectClaim,
            switchToSession,
            updateLanguage,
            HimayaApp
        };

        // FILE UPLOAD FUNCTIONS
        function handleFileUpload() {
            const fileInput = document.getElementById('claimFiles');
            const files = fileInput.files;

            if (files.length === 0) return;

            HimayaApp.uploadedFiles = [];
            const filesList = document.getElementById('filesList');
            filesList.innerHTML = '';

            for (let i = 0; i < files.length; i++) {
                const file = files[i];

                // Validate file size (5MB max)
                if (file.size > 5 * 1024 * 1024) {
                    showNotification(`❌ Fichier ${file.name} trop volumineux (max 5MB)`);
                    continue;
                }

                // Create file hash (simple hash for demo)
                const fileHash = `file_${Date.now()}_${i}_${file.name.replace(/[^a-zA-Z0-9]/g, '')}`;

                const fileData = {
                    file: file,
                    hash: fileHash,
                    name: file.name,
                    type: file.type,
                    size: file.size
                };

                HimayaApp.uploadedFiles.push(fileData);

                // Add to preview
                const fileItem = document.createElement('div');
                fileItem.style.cssText = 'background: #374151; padding: 1rem; border-radius: 8px; margin-bottom: 0.5rem; display: flex; justify-content: space-between; align-items: center;';
                fileItem.innerHTML = `
                    <div>
                        <strong style="color: #8b5cf6;">${file.name}</strong><br>
                        <small style="color: #9ca3af;">Type: ${file.type} | Taille: ${(file.size / 1024).toFixed(1)} KB</small>
                    </div>
                    <div style="color: #10b981;">✅ Prêt</div>
                `;
                filesList.appendChild(fileItem);
            }

            if (HimayaApp.uploadedFiles.length > 0) {
                document.getElementById('uploadedFilesPreview').style.display = 'block';
                showNotification(`📎 ${HimayaApp.uploadedFiles.length} fichier(s) préparé(s) pour l'upload`);
            }
        }

        async function uploadFilesToBlockchain(claimId) {
            if (HimayaApp.uploadedFiles.length === 0) return;

            try {
                showNotification('📤 Upload des fichiers sur la blockchain...');

                for (const fileData of HimayaApp.uploadedFiles) {
                    // In a real implementation, you would upload to IPFS or similar
                    // For demo, we'll just store metadata on blockchain
                    const tx = await HimayaApp.contract.methods.uploadClaimFile(
                        claimId,
                        fileData.hash,
                        fileData.name,
                        fileData.type,
                        fileData.size
                    ).send({
                        from: HimayaApp.account,
                        gas: 300000
                    });

                    console.log(`File ${fileData.name} uploaded to blockchain:`, tx.transactionHash);
                }

                showNotification(`✅ ${HimayaApp.uploadedFiles.length} fichier(s) uploadé(s) sur la blockchain`);

            } catch (error) {
                console.error('File upload failed:', error);
                showNotification('❌ Échec de l\'upload des fichiers');
            }
        }

        // WALLET ASSIGNMENT FUNCTIONS
        async function assignClientWallet() {
            if (!HimayaApp.connected) {
                showNotification('⚠️ Veuillez d\'abord connecter votre wallet');
                return;
            }

            const walletAddress = document.getElementById('clientWalletAddress').value;
            const clientName = document.getElementById('clientName').value;
            const clientId = document.getElementById('clientId').value;
            const clientPhone = document.getElementById('clientPhone').value;
            const clientEmail = document.getElementById('clientEmail').value;

            if (!walletAddress || !clientName || !clientId) {
                showNotification('⚠️ Veuillez remplir tous les champs obligatoires');
                return;
            }

            // Validate wallet address format
            if (!walletAddress.startsWith('0x') || walletAddress.length !== 42) {
                showNotification('⚠️ Format d\'adresse wallet invalide');
                return;
            }

            try {
                showNotification('🔄 Assignation du wallet client...');

                const tx = await HimayaApp.contract.methods.assignClientWallet(
                    walletAddress,
                    clientName,
                    clientId,
                    clientPhone,
                    clientEmail
                ).send({
                    from: HimayaApp.account,
                    gas: 500000
                });

                // Clear form
                document.getElementById('clientWalletAddress').value = '';
                document.getElementById('clientName').value = '';
                document.getElementById('clientId').value = '';
                document.getElementById('clientPhone').value = '';
                document.getElementById('clientEmail').value = '';

                showNotification(`🎉 Wallet assigné avec succès à ${clientName}!`);
                console.log('Wallet assignment transaction:', tx.transactionHash);

                // Reload assigned wallets list
                await loadAssignedWallets();

            } catch (error) {
                console.error('Wallet assignment failed:', error);
                if (error.message.includes('Wallet already assigned')) {
                    showNotification('❌ Ce wallet est déjà assigné à un autre client');
                } else if (error.message.includes('Only authorized insurers')) {
                    showNotification('❌ Seuls les assureurs autorisés peuvent assigner des wallets');
                } else {
                    showNotification('❌ Échec de l\'assignation du wallet');
                }
            }
        }

        async function loadAssignedWallets() {
            if (!HimayaApp.connected) return;

            try {
                const allWallets = await HimayaApp.contract.methods.getAllClientWallets().call();

                const walletsList = document.getElementById('assignedWalletsList');
                if (allWallets.length === 0) {
                    walletsList.innerHTML = '<p style="text-align: center; color: #9ca3af; padding: 1rem;">Aucun wallet assigné</p>';
                    return;
                }

                // Load detailed wallet data
                const walletDetails = await Promise.all(
                    allWallets.map(address => HimayaApp.contract.methods.getClientWallet(address).call())
                );

                walletsList.innerHTML = walletDetails.map(wallet => `
                    <div style="background: #374151; padding: 1rem; border-radius: 8px; margin-bottom: 1rem; border: 1px solid #8b5cf6;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.5rem;">
                            <strong style="color: #8b5cf6;">${wallet.clientName}</strong>
                            <span style="background: #10b981; color: white; padding: 0.25rem 0.75rem; border-radius: 12px; font-size: 0.8rem;">
                                ${wallet.isVerified ? 'Vérifié' : 'En attente'}
                            </span>
                        </div>
                        <div style="grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); display: grid; gap: 0.5rem; color: #e5e7eb; font-size: 0.9rem;">
                            <div><strong>Wallet:</strong> ${wallet.walletAddress.slice(0, 6)}...${wallet.walletAddress.slice(-4)}</div>
                            <div><strong>CIN:</strong> ${wallet.clientId}</div>
                            <div><strong>Téléphone:</strong> ${wallet.phoneNumber}</div>
                            <div><strong>Email:</strong> ${wallet.email}</div>
                        </div>
                        <div style="margin-top: 0.5rem; font-size: 0.8rem; color: #9ca3af;">
                            Assigné le: ${new Date(wallet.assignedAt * 1000).toLocaleDateString()}
                        </div>
                    </div>
                `).join('');

                console.log('✅ Loaded', walletDetails.length, 'assigned wallets');

            } catch (error) {
                console.error('Failed to load assigned wallets:', error);
            }
        }

        // IDENTITY VERIFICATION FUNCTIONS
        function showIdentityVerification() {
            showScreen('identityVerificationScreen');
            initializeDocumentUploads();
        }

        function initializeDocumentUploads() {
            // Initialize file upload handlers for identity documents
            const fileInputs = ['cinFrontFile', 'cinBackFile', 'selfieFile'];

            fileInputs.forEach(inputId => {
                const input = document.getElementById(inputId);
                if (input) {
                    input.addEventListener('change', (e) => handleDocumentUpload(e, inputId));
                }
            });
        }

        function handleDocumentUpload(event, inputId) {
            const file = event.target.files[0];
            if (!file) return;

            // Validate file
            if (file.size > 10 * 1024 * 1024) { // 10MB max
                showNotification('❌ Fichier trop volumineux (max 10MB)');
                return;
            }

            if (!file.type.startsWith('image/')) {
                showNotification('❌ Seules les images sont acceptées');
                return;
            }

            // Preview the image
            const reader = new FileReader();
            reader.onload = (e) => {
                const previewId = inputId.replace('File', 'Preview');
                const preview = document.getElementById(previewId);
                const uploadBtn = preview.nextElementSibling;

                preview.innerHTML = `<img src="${e.target.result}" style="width: 100%; height: 100%; object-fit: cover; border-radius: 10px;">`;
                preview.classList.add('has-image');
                uploadBtn.textContent = '✅ ' + uploadBtn.textContent.replace('📤 ', '');
                uploadBtn.classList.add('uploaded');

                // Store file data
                const docType = inputId.replace('File', '');
                HimayaApp.verificationData.documents[docType] = {
                    file: file,
                    dataUrl: e.target.result,
                    uploaded: true
                };

                showNotification(`✅ ${getDocumentName(docType)} téléchargé avec succès`);
            };
            reader.readAsDataURL(file);
        }

        function getDocumentName(docType) {
            const names = {
                cinFront: 'CIN Recto',
                cinBack: 'CIN Verso',
                selfie: 'Photo Selfie'
            };
            return names[docType] || docType;
        }

        async function submitIdentityVerification() {
            // Validate personal information
            const personalInfo = {
                fullName: document.getElementById('verifyFullName').value.trim(),
                cinNumber: document.getElementById('verifyCinNumber').value.trim(),
                birthDate: document.getElementById('verifyBirthDate').value,
                phoneNumber: document.getElementById('verifyPhoneNumber').value.trim(),
                emailAddress: document.getElementById('verifyEmailAddress').value.trim(),
                address: document.getElementById('verifyAddress').value.trim()
            };

            // Validate required fields
            const requiredFields = ['fullName', 'cinNumber', 'birthDate', 'phoneNumber', 'emailAddress', 'address'];
            for (const field of requiredFields) {
                if (!personalInfo[field]) {
                    showNotification(`⚠️ Veuillez remplir le champ: ${getFieldName(field)}`);
                    return;
                }
            }

            // Validate documents
            const requiredDocs = ['cinFront', 'cinBack', 'selfie'];
            for (const doc of requiredDocs) {
                if (!HimayaApp.verificationData.documents[doc]?.uploaded) {
                    showNotification(`⚠️ Veuillez télécharger: ${getDocumentName(doc)}`);
                    return;
                }
            }

            // Collect contact preferences
            const contactPrefs = {
                email: document.getElementById('contactEmail').checked,
                phone: document.getElementById('contactPhone').checked,
                whatsapp: document.getElementById('contactWhatsapp').checked,
                availabilityTime: document.getElementById('availabilityTime').value
            };

            try {
                showNotification('🔄 Soumission de votre demande de vérification...');

                // Generate submission ID
                const submissionId = `VER-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

                // Store verification data
                HimayaApp.verificationData.personalInfo = personalInfo;
                HimayaApp.verificationData.contactPrefs = contactPrefs;
                HimayaApp.verificationData.submissionId = submissionId;
                HimayaApp.verificationData.status = 'pending';
                HimayaApp.verificationData.submittedAt = new Date().toISOString();

                // In a real implementation, this would be sent to a backend server
                // For demo purposes, we'll store in localStorage
                localStorage.setItem('himayaVerification', JSON.stringify(HimayaApp.verificationData));

                showNotification('🎉 Demande de vérification soumise avec succès!');

                // Show verification status screen
                showVerificationStatus();

            } catch (error) {
                console.error('Verification submission failed:', error);
                showNotification('❌ Échec de la soumission de la demande');
            }
        }

        function getFieldName(field) {
            const names = {
                fullName: 'Nom complet',
                cinNumber: 'Numéro CIN',
                birthDate: 'Date de naissance',
                phoneNumber: 'Numéro de téléphone',
                emailAddress: 'Adresse email',
                address: 'Adresse complète'
            };
            return names[field] || field;
        }

        function showVerificationStatus() {
            showScreen('verificationStatusScreen');
            updateVerificationStatusDisplay();
        }

        function updateVerificationStatusDisplay() {
            const statusContent = document.getElementById('verificationStatusContent');
            const verification = HimayaApp.verificationData;

            if (verification.status === 'pending') {
                statusContent.innerHTML = `
                    <div class="verification-status">
                        <div class="status-icon status-pending">⏳</div>
                        <div class="status-title status-pending">Vérification en Cours</div>
                        <div class="status-description">
                            Votre demande de vérification a été soumise avec succès.<br>
                            Notre équipe examine actuellement vos documents.
                        </div>
                        <div style="background: rgba(245, 158, 11, 0.1); border: 1px solid #f59e0b; border-radius: 12px; padding: 1.5rem;">
                            <h4 style="color: #f59e0b; margin-bottom: 1rem;">📋 Détails de votre demande</h4>
                            <div style="text-align: left; color: #e5e7eb;">
                                <div><strong>ID de demande:</strong> ${verification.submissionId}</div>
                                <div><strong>Nom:</strong> ${verification.personalInfo.fullName}</div>
                                <div><strong>CIN:</strong> ${verification.personalInfo.cinNumber}</div>
                                <div><strong>Email:</strong> ${verification.personalInfo.emailAddress}</div>
                                <div><strong>Soumis le:</strong> ${new Date(verification.submittedAt).toLocaleString()}</div>
                            </div>
                        </div>
                        <div style="margin-top: 2rem; color: #9ca3af;">
                            ⏱️ Temps de traitement estimé: 24-48 heures<br>
                            📧 Vous recevrez une notification par email une fois la vérification terminée
                        </div>
                    </div>
                `;
            } else if (verification.status === 'approved') {
                statusContent.innerHTML = `
                    <div class="verification-status">
                        <div class="status-icon status-approved">✅</div>
                        <div class="status-title status-approved">Vérification Approuvée!</div>
                        <div class="status-description">
                            Félicitations! Votre identité a été vérifiée avec succès.<br>
                            Voici vos identifiants de wallet sécurisé.
                        </div>
                        <div class="wallet-credentials">
                            <h4 style="color: #10b981; margin-bottom: 1rem;">🔐 Vos Identifiants MetaMask</h4>
                            <div class="credential-item">
                                <div class="credential-label">Adresse Wallet:</div>
                                <div>
                                    <span class="credential-value" onclick="copyToClipboard('${verification.walletAddress}')">${verification.walletAddress}</span>
                                    <button class="copy-btn" onclick="copyToClipboard('${verification.walletAddress}')">Copier</button>
                                </div>
                            </div>
                            <div class="credential-item">
                                <div class="credential-label">Clé Privée:</div>
                                <div>
                                    <span class="credential-value" onclick="copyToClipboard('${verification.privateKey}')">${verification.privateKey}</span>
                                    <button class="copy-btn" onclick="copyToClipboard('${verification.privateKey}')">Copier</button>
                                </div>
                            </div>
                            <div style="background: rgba(239, 68, 68, 0.1); border: 1px solid #ef4444; border-radius: 8px; padding: 1rem; margin-top: 1rem;">
                                <strong style="color: #ef4444;">⚠️ IMPORTANT:</strong>
                                <ul style="color: #e5e7eb; margin: 0.5rem 0 0 1rem;">
                                    <li>Gardez ces informations secrètes et sécurisées</li>
                                    <li>Ne partagez jamais votre clé privée</li>
                                    <li>Sauvegardez ces informations dans un endroit sûr</li>
                                </ul>
                            </div>
                        </div>
                        <button class="btn" onclick="proceedToClientSession()" style="margin-top: 2rem;">
                            🚀 Accéder à Mon Compte Himaya
                        </button>
                    </div>
                `;
            } else if (verification.status === 'rejected') {
                statusContent.innerHTML = `
                    <div class="verification-status">
                        <div class="status-icon status-rejected">❌</div>
                        <div class="status-title status-rejected">Vérification Rejetée</div>
                        <div class="status-description">
                            Malheureusement, votre demande de vérification n'a pas pu être approuvée.
                        </div>
                        <div style="background: rgba(239, 68, 68, 0.1); border: 1px solid #ef4444; border-radius: 12px; padding: 1.5rem;">
                            <h4 style="color: #ef4444; margin-bottom: 1rem;">📋 Raison du rejet</h4>
                            <div style="color: #e5e7eb;">
                                ${verification.rejectionReason || 'Documents non conformes ou illisibles'}
                            </div>
                        </div>
                        <button class="btn" onclick="showIdentityVerification()" style="margin-top: 2rem;">
                            🔄 Soumettre une Nouvelle Demande
                        </button>
                    </div>
                `;
            }
        }

        function checkVerificationStatus() {
            // In a real implementation, this would check with the backend
            // For demo purposes, we'll simulate different statuses
            const stored = localStorage.getItem('himayaVerification');
            if (stored) {
                HimayaApp.verificationData = JSON.parse(stored);

                // Simulate status progression for demo
                const submittedTime = new Date(HimayaApp.verificationData.submittedAt).getTime();
                const now = Date.now();
                const timeDiff = now - submittedTime;

                // After 30 seconds, simulate approval for demo
                if (timeDiff > 30000 && HimayaApp.verificationData.status === 'pending') {
                    HimayaApp.verificationData.status = 'approved';
                    HimayaApp.verificationData.walletAddress = '0x' + Math.random().toString(16).substr(2, 40);
                    HimayaApp.verificationData.privateKey = '0x' + Math.random().toString(16).substr(2, 64);
                    localStorage.setItem('himayaVerification', JSON.stringify(HimayaApp.verificationData));
                    showNotification('🎉 Vérification approuvée! Vos identifiants sont prêts.');
                }

                updateVerificationStatusDisplay();
            } else {
                showNotification('⚠️ Aucune demande de vérification trouvée');
            }
        }

        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                showNotification('📋 Copié dans le presse-papiers');
            }).catch(() => {
                showNotification('❌ Impossible de copier');
            });
        }

        function proceedToClientSession() {
            // Set the wallet credentials and proceed to client session
            if (HimayaApp.verificationData.walletAddress) {
                showNotification('🎉 Redirection vers votre compte client...');
                setTimeout(() => {
                    switchToSession('client');
                }, 1000);
            }
        }

        // Check for existing verification on load
        function checkExistingVerification() {
            const stored = localStorage.getItem('himayaVerification');
            if (stored) {
                HimayaApp.verificationData = JSON.parse(stored);

                if (HimayaApp.verificationData.status === 'pending') {
                    // Auto-check status if pending
                    setTimeout(checkVerificationStatus, 2000);
                }
            }
        }

        // ADMIN VERIFICATION MANAGEMENT FUNCTIONS
        function loadVerificationRequests() {
            // In a real implementation, this would load from backend
            // For demo, we'll simulate some verification requests
            const requests = [
                {
                    id: 'VER-1234567890',
                    fullName: 'Ahmed Ben Ali',
                    cinNumber: 'AB123456',
                    email: '<EMAIL>',
                    phone: '+212 6XX-XXXXXX',
                    submittedAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
                    status: 'pending',
                    documents: {
                        cinFront: true,
                        cinBack: true,
                        selfie: true
                    }
                },
                {
                    id: 'VER-0987654321',
                    fullName: 'Fatima Zahra',
                    cinNumber: 'FZ789012',
                    email: '<EMAIL>',
                    phone: '+212 6XX-YYYYYY',
                    submittedAt: new Date(Date.now() - 5 * 60 * 60 * 1000).toISOString(),
                    status: 'pending',
                    documents: {
                        cinFront: true,
                        cinBack: true,
                        selfie: true
                    }
                }
            ];

            // Add current user's request if exists
            const userVerification = localStorage.getItem('himayaVerification');
            if (userVerification) {
                const userData = JSON.parse(userVerification);
                if (userData.status === 'pending') {
                    requests.unshift({
                        id: userData.submissionId,
                        fullName: userData.personalInfo.fullName,
                        cinNumber: userData.personalInfo.cinNumber,
                        email: userData.personalInfo.emailAddress,
                        phone: userData.personalInfo.phoneNumber,
                        submittedAt: userData.submittedAt,
                        status: userData.status,
                        documents: {
                            cinFront: userData.documents.cinFront?.uploaded || false,
                            cinBack: userData.documents.cinBack?.uploaded || false,
                            selfie: userData.documents.selfie?.uploaded || false
                        }
                    });
                }
            }

            const requestsList = document.getElementById('verificationRequestsList');
            if (requests.length === 0) {
                requestsList.innerHTML = '<p style="text-align: center; color: #9ca3af; padding: 2rem;">Aucune demande de vérification en attente</p>';
                return;
            }

            requestsList.innerHTML = requests.map(request => `
                <div style="background: #374151; padding: 1.5rem; border-radius: 12px; margin-bottom: 1rem; border: 1px solid #4a5568;">
                    <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 1rem;">
                        <div style="flex: 1;">
                            <div style="display: flex; align-items: center; gap: 1rem; margin-bottom: 0.5rem;">
                                <strong style="color: #8b5cf6; font-size: 1.1rem;">${request.fullName}</strong>
                                <span style="background: rgba(245, 158, 11, 0.2); color: #f59e0b; padding: 0.25rem 0.75rem; border-radius: 12px; font-size: 0.8rem;">
                                    En attente
                                </span>
                            </div>
                            <div style="color: #9ca3af; font-size: 0.9rem; margin-bottom: 0.5rem;">
                                🆔 CIN: ${request.cinNumber} | 📧 ${request.email} | 📱 ${request.phone}
                            </div>
                            <div style="color: #9ca3af; font-size: 0.9rem; margin-bottom: 1rem;">
                                📅 Soumis: ${new Date(request.submittedAt).toLocaleString()} | 🆔 ID: ${request.id}
                            </div>
                            <div style="margin-bottom: 1rem;">
                                <div style="color: #8b5cf6; font-weight: 600; margin-bottom: 0.5rem;">📄 Documents soumis:</div>
                                <div style="display: flex; gap: 1rem;">
                                    <span style="color: ${request.documents.cinFront ? '#10b981' : '#ef4444'};">
                                        ${request.documents.cinFront ? '✅' : '❌'} CIN Recto
                                    </span>
                                    <span style="color: ${request.documents.cinBack ? '#10b981' : '#ef4444'};">
                                        ${request.documents.cinBack ? '✅' : '❌'} CIN Verso
                                    </span>
                                    <span style="color: ${request.documents.selfie ? '#10b981' : '#ef4444'};">
                                        ${request.documents.selfie ? '✅' : '❌'} Selfie
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div style="display: flex; flex-direction: column; gap: 0.5rem; min-width: 200px;">
                            <button class="btn" onclick="approveVerification('${request.id}')" style="background: #10b981; width: 100%;">
                                ✅ Approuver & Créer Wallet
                            </button>
                            <button class="btn" onclick="showRejectVerificationModal('${request.id}')" style="background: #ef4444; width: 100%;">
                                ❌ Rejeter
                            </button>
                            <button class="btn" onclick="viewVerificationDetails('${request.id}')" style="background: #6b7280; width: 100%;">
                                👁️ Voir Documents
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        async function approveVerification(requestId) {
            try {
                showNotification('🔄 Approbation de la vérification et création du wallet...');

                // Generate wallet credentials
                const walletAddress = '0x' + Math.random().toString(16).substr(2, 40);
                const privateKey = '0x' + Math.random().toString(16).substr(2, 64);

                // Update verification status
                if (requestId.startsWith('VER-') && requestId.includes(Date.now().toString().substr(0, 10))) {
                    // This is the current user's request
                    const stored = localStorage.getItem('himayaVerification');
                    if (stored) {
                        const verification = JSON.parse(stored);
                        verification.status = 'approved';
                        verification.walletAddress = walletAddress;
                        verification.privateKey = privateKey;
                        verification.approvedAt = new Date().toISOString();
                        localStorage.setItem('himayaVerification', JSON.stringify(verification));
                    }
                }

                showNotification(`🎉 Vérification approuvée! Wallet créé: ${walletAddress.slice(0, 6)}...${walletAddress.slice(-4)}`);
                console.log('Verification approved:', {requestId, walletAddress, privateKey});

                // Reload verification requests
                loadVerificationRequests();

            } catch (error) {
                console.error('Verification approval failed:', error);
                showNotification('❌ Échec de l\'approbation de la vérification');
            }
        }

        function showRejectVerificationModal(requestId) {
            const reason = prompt('Raison du rejet de la vérification:');
            if (reason) {
                rejectVerification(requestId, reason);
            }
        }

        function rejectVerification(requestId, reason) {
            try {
                showNotification('🔄 Rejet de la vérification...');

                // Update verification status
                if (requestId.startsWith('VER-') && requestId.includes(Date.now().toString().substr(0, 10))) {
                    // This is the current user's request
                    const stored = localStorage.getItem('himayaVerification');
                    if (stored) {
                        const verification = JSON.parse(stored);
                        verification.status = 'rejected';
                        verification.rejectionReason = reason;
                        verification.rejectedAt = new Date().toISOString();
                        localStorage.setItem('himayaVerification', JSON.stringify(verification));
                    }
                }

                showNotification(`❌ Vérification rejetée: ${reason}`);
                console.log('Verification rejected:', {requestId, reason});

                // Reload verification requests
                loadVerificationRequests();

            } catch (error) {
                console.error('Verification rejection failed:', error);
                showNotification('❌ Échec du rejet de la vérification');
            }
        }

        function viewVerificationDetails(requestId) {
            // In a real implementation, this would show the actual uploaded documents
            alert(`Affichage des documents pour la demande: ${requestId}\n\nDans une implémentation réelle, ceci afficherait:\n- Photo CIN Recto\n- Photo CIN Verso\n- Photo Selfie\n\nAvec possibilité de zoomer et vérifier la qualité.`);
        }

        console.log('🛡️ Himaya Blockchain Insurance DApp loaded successfully');
        console.log('🇲🇦 Multi-language support: Français, العربية, English');
        console.log('⛓️ Blockchain integration ready');
        console.log('📎 File upload functionality enabled');
        console.log('👥 Wallet assignment system active');
        console.log('🆔 Identity verification system enabled');
    </script>
</body>
</html>
