# Vehicle Insurance Claims DApp

A comprehensive decentralized application for managing vehicle insurance claims using Hyperledger Besu blockchain. This student project demonstrates a full-stack blockchain implementation with smart contracts, backend API, and React frontend.

## 🏗️ Architecture

- **Blockchain**: Hyperledger Besu (Ethereum-compatible private network)
- **Smart Contracts**: Solidity with OpenZeppelin libraries
- **Backend**: Node.js/Express with Web3.js integration
- **Frontend**: React with Material-UI and Web3 integration
- **Database**: MongoDB for off-chain data storage
- **Caching**: Redis for session management

## ✨ Features

### Core Functionality
- **Vehicle Registration**: Register vehicles on blockchain with immutable records
- **Insurance Policy Management**: Create and manage insurance policies
- **Decentralized Claims**: Submit and process insurance claims transparently
- **Multi-step Approval Workflow**: Structured claim review process
- **Document Management**: Upload and store claim-related documents
- **Real-time Updates**: Live status updates for claims and policies

### User Roles
- **Policyholder**: Register vehicles, purchase policies, submit claims
- **Insurance Agent**: Review claims, approve/reject claims, manage policies
- **Admin**: System administration, user management, oversight

### Security Features
- **Wallet Authentication**: MetaMask integration for secure login
- **Role-based Access Control**: Different permissions for different user types
- **JWT Authentication**: Secure API access
- **Input Validation**: Comprehensive data validation
- **Rate Limiting**: API protection against abuse

## 📁 Project Structure

```
vehicle-insurance-dapp/
├── blockchain/                 # Blockchain layer
│   ├── contracts/             # Solidity smart contracts
│   ├── scripts/               # Deployment scripts
│   ├── config/                # Network configuration
│   └── abi/                   # Contract ABIs
├── backend/                   # Backend API
│   ├── config/                # Database and blockchain config
│   ├── models/                # MongoDB models
│   ├── routes/                # API routes
│   ├── middleware/            # Authentication middleware
│   └── server.js              # Main server file
├── frontend/                  # React frontend
│   ├── src/
│   │   ├── components/        # Reusable components
│   │   ├── pages/             # Page components
│   │   ├── contexts/          # React contexts
│   │   └── utils/             # Utility functions
│   └── public/                # Static assets
├── docker-compose.yml         # Docker services
├── setup.sh                   # Automated setup script
└── README.md                  # This file
```

## 🚀 Quick Start

### Prerequisites
- Docker and Docker Compose
- Node.js (v16 or higher)
- npm or yarn
- Git

### Automated Setup
```bash
# Clone the repository
git clone <repository-url>
cd vehicle-insurance-dapp

# Make setup script executable
chmod +x setup.sh

# Run automated setup
./setup.sh
```

### Manual Setup

1. **Start Infrastructure Services**
   ```bash
   docker-compose up -d
   ```

2. **Deploy Smart Contracts**
   ```bash
   cd blockchain
   npm install
   npm run deploy
   cd ..
   ```

3. **Start Backend Server**
   ```bash
   cd backend
   npm install
   cp .env.example .env
   # Update .env with contract addresses from deployment
   npm start
   cd ..
   ```

4. **Start Frontend Application**
   ```bash
   cd frontend
   npm install
   npm start
   ```

5. **Access the Application**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:3001
   - Besu RPC: http://localhost:8545

## 🔧 Configuration

### Environment Variables

#### Backend (.env)
```env
# Server
PORT=3001
NODE_ENV=development

# Blockchain
BESU_RPC_URL=http://localhost:8545
CHAIN_ID=1337

# Database
MONGODB_URI=**************************************************************************

# Security
JWT_SECRET=your-secret-key
```

### Test Accounts
The setup includes pre-funded test accounts:
- **Deployer**: `0xfe3b557e8fb62b89f4916b721be55ceb828dbd73`
- **Insurer 1**: `0x627306090abaB3A6e1400e9345bC60c78a8BEf57`
- **Insurer 2**: `0xf17f52151EbEF6C7334FAD080c5704D77216b732`

## 📋 Smart Contracts

### VehicleRegistry.sol
- Vehicle registration and ownership tracking
- Transfer ownership functionality
- Immutable vehicle records

### InsurancePolicy.sol
- Policy creation and management
- Coverage type definitions
- Policy status tracking
- Authorized insurer management

### ClaimManager.sol
- Claim submission and processing
- Multi-step approval workflow
- Document attachment support
- Fraud detection scoring

## 🔌 API Endpoints

### Authentication
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - Email/password login
- `POST /api/auth/wallet-login` - MetaMask login
- `GET /api/auth/profile` - Get user profile

### Vehicles
- `GET /api/vehicles/my-vehicles` - Get user's vehicles
- `POST /api/vehicles/register` - Register new vehicle
- `PUT /api/vehicles/:id` - Update vehicle details
- `POST /api/vehicles/:id/transfer` - Transfer ownership

### Policies
- `GET /api/policies/my-policies` - Get user's policies
- `POST /api/policies/create` - Create new policy
- `GET /api/policies/:id` - Get policy details

### Claims
- `GET /api/claims/my-claims` - Get user's claims
- `POST /api/claims/submit` - Submit new claim
- `PUT /api/claims/:id/status` - Update claim status
- `POST /api/claims/:id/documents` - Add documents

## 🧪 Testing

### Smart Contract Tests
```bash
cd blockchain
npm test
```

### Backend Tests
```bash
cd backend
npm test
```

### Frontend Tests
```bash
cd frontend
npm test
```

## 🔒 Security Considerations

- Private keys are for development only
- Use environment variables for sensitive data
- Implement proper access controls in production
- Regular security audits recommended
- Use HTTPS in production

## 🚀 Deployment

### Production Deployment
1. Set up production Besu network
2. Deploy contracts to production network
3. Configure production environment variables
4. Use proper SSL certificates
5. Set up monitoring and logging

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🎓 Educational Purpose

This project is designed for educational purposes to demonstrate:
- Blockchain development with Hyperledger Besu
- Smart contract development with Solidity
- Full-stack DApp development
- Integration between blockchain and traditional web technologies
- Real-world application of blockchain in insurance industry

## 📞 Support

For questions or issues:
1. Check the documentation
2. Review existing issues
3. Create a new issue with detailed description

## 🙏 Acknowledgments

- Hyperledger Besu team
- OpenZeppelin for smart contract libraries
- React and Material-UI communities
- Web3.js developers
