const express = require('express');
const { body, validationResult } = require('express-validator');
const Vehicle = require('../models/Vehicle');
const { auth, authorize } = require('../middleware/auth');
const { contracts, accounts, sendTransaction } = require('../config/blockchain');

const router = express.Router();

// Get all vehicles (admin only)
router.get('/', auth, authorize('admin'), async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const vehicles = await Vehicle.find({ isActive: true })
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const total = await Vehicle.countDocuments({ isActive: true });

    res.json({
      vehicles,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Get vehicles error:', error);
    res.status(500).json({ error: 'Failed to fetch vehicles' });
  }
});

// Get user's vehicles
router.get('/my-vehicles', auth, async (req, res) => {
  try {
    const vehicles = await Vehicle.find({
      owner: req.user.walletAddress,
      isActive: true
    }).sort({ createdAt: -1 });

    res.json({ vehicles });
  } catch (error) {
    console.error('Get user vehicles error:', error);
    res.status(500).json({ error: 'Failed to fetch your vehicles' });
  }
});

// Search vehicles (must be before /:id route)
router.get('/search', auth, authorize('admin'), async (req, res) => {
  try {
    const { vin, make, model, year, owner } = req.query;

    const query = { isActive: true };

    if (vin) query.vin = new RegExp(vin, 'i');
    if (make) query.make = new RegExp(make, 'i');
    if (model) query.model = new RegExp(model, 'i');
    if (year) query.year = parseInt(year);
    if (owner) query.owner = owner.toLowerCase();

    const vehicles = await Vehicle.find(query).sort({ createdAt: -1 });

    res.json(vehicles);
  } catch (error) {
    res.status(500).json({ error: 'Failed to search vehicles' });
  }
});

// Get vehicle by ID
router.get('/:id', auth, async (req, res) => {
  try {
    const vehicle = await Vehicle.findOne({
      blockchainId: req.params.id,
      isActive: true
    });

    if (!vehicle) {
      return res.status(404).json({ error: 'Vehicle not found' });
    }

    // Check if user owns the vehicle or is admin
    if (vehicle.owner !== req.user.walletAddress && req.user.role !== 'admin') {
      return res.status(403).json({ error: 'Access denied' });
    }

    res.json({ vehicle });
  } catch (error) {
    console.error('Get vehicle error:', error);
    res.status(500).json({ error: 'Failed to fetch vehicle' });
  }
});

// Register new vehicle
router.post('/register', auth, [
  body('vin').isLength({ min: 17, max: 17 }).withMessage('VIN must be 17 characters'),
  body('make').trim().isLength({ min: 1 }).withMessage('Make is required'),
  body('model').trim().isLength({ min: 1 }).withMessage('Model is required'),
  body('year').isInt({ min: 1900, max: 2030 }).withMessage('Invalid year'),
  body('color').trim().isLength({ min: 1 }).withMessage('Color is required'),
  body('engineNumber').optional().trim(),
  body('fuelType').optional().isIn(['gasoline', 'diesel', 'electric', 'hybrid', 'other']),
  body('transmission').optional().isIn(['manual', 'automatic', 'cvt']),
  body('mileage').optional().isInt({ min: 0 }),
  body('purchaseDate').optional().isISO8601(),
  body('purchasePrice').optional().isFloat({ min: 0 }),
  body('currentValue').optional().isFloat({ min: 0 })
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const {
      vin,
      make,
      model,
      year,
      color,
      engineNumber,
      fuelType,
      transmission,
      mileage,
      purchaseDate,
      purchasePrice,
      currentValue,
      notes
    } = req.body;

    // Check if VIN already exists
    const existingVehicle = await Vehicle.findOne({ vin: vin.toUpperCase() });
    if (existingVehicle) {
      return res.status(400).json({ error: 'Vehicle with this VIN already registered' });
    }

    // Register vehicle on blockchain
    const vehicleRegistry = contracts.vehicleRegistry;
    const transaction = vehicleRegistry.methods.registerVehicle(
      vin.toUpperCase(),
      make,
      model,
      year,
      color
    );

    const result = await sendTransaction(transaction, {
      address: req.user.walletAddress
    });

    // Get the vehicle ID from the event
    const vehicleRegisteredEvent = result.events.VehicleRegistered;
    const blockchainId = parseInt(vehicleRegisteredEvent.returnValues.vehicleId);

    // Save to database
    const vehicle = new Vehicle({
      blockchainId,
      vin: vin.toUpperCase(),
      make,
      model,
      year,
      color,
      owner: req.user.walletAddress,
      registrationDate: new Date(),
      engineNumber,
      fuelType,
      transmission,
      mileage,
      purchaseDate,
      purchasePrice,
      currentValue,
      notes
    });

    await vehicle.save();

    res.status(201).json({
      message: 'Vehicle registered successfully',
      vehicle,
      transactionHash: result.transactionHash
    });

  } catch (error) {
    console.error('Vehicle registration error:', error);
    
    if (error.message.includes('Vehicle already registered')) {
      return res.status(400).json({ error: 'Vehicle already registered on blockchain' });
    }
    
    res.status(500).json({ error: 'Failed to register vehicle' });
  }
});

// Update vehicle (off-chain data only)
router.put('/:id', auth, [
  body('engineNumber').optional().trim(),
  body('fuelType').optional().isIn(['gasoline', 'diesel', 'electric', 'hybrid', 'other']),
  body('transmission').optional().isIn(['manual', 'automatic', 'cvt']),
  body('mileage').optional().isInt({ min: 0 }),
  body('purchaseDate').optional().isISO8601(),
  body('purchasePrice').optional().isFloat({ min: 0 }),
  body('currentValue').optional().isFloat({ min: 0 }),
  body('notes').optional().trim()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const vehicle = await Vehicle.findOne({
      blockchainId: req.params.id,
      isActive: true
    });

    if (!vehicle) {
      return res.status(404).json({ error: 'Vehicle not found' });
    }

    // Check ownership
    if (vehicle.owner !== req.user.walletAddress && req.user.role !== 'admin') {
      return res.status(403).json({ error: 'Access denied' });
    }

    const allowedUpdates = [
      'engineNumber', 'fuelType', 'transmission', 'mileage',
      'purchaseDate', 'purchasePrice', 'currentValue', 'notes'
    ];

    const updates = {};
    Object.keys(req.body).forEach(key => {
      if (allowedUpdates.includes(key)) {
        updates[key] = req.body[key];
      }
    });

    const updatedVehicle = await Vehicle.findByIdAndUpdate(
      vehicle._id,
      updates,
      { new: true, runValidators: true }
    );

    res.json({
      message: 'Vehicle updated successfully',
      vehicle: updatedVehicle
    });

  } catch (error) {
    console.error('Vehicle update error:', error);
    res.status(500).json({ error: 'Failed to update vehicle' });
  }
});

// Transfer vehicle ownership
router.post('/:id/transfer', auth, [
  body('newOwner').isEthereumAddress().withMessage('Invalid wallet address')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { newOwner } = req.body;

    const vehicle = await Vehicle.findOne({
      blockchainId: req.params.id,
      isActive: true
    });

    if (!vehicle) {
      return res.status(404).json({ error: 'Vehicle not found' });
    }

    // Check ownership
    if (vehicle.owner !== req.user.walletAddress) {
      return res.status(403).json({ error: 'Only vehicle owner can transfer ownership' });
    }

    // Transfer on blockchain
    const vehicleRegistry = contracts.vehicleRegistry;
    const transaction = vehicleRegistry.methods.transferVehicle(
      parseInt(req.params.id),
      newOwner
    );

    const result = await sendTransaction(transaction, {
      address: req.user.walletAddress
    });

    // Update database
    vehicle.owner = newOwner.toLowerCase();
    await vehicle.save();

    res.json({
      message: 'Vehicle ownership transferred successfully',
      vehicle,
      transactionHash: result.transactionHash
    });

  } catch (error) {
    console.error('Vehicle transfer error:', error);
    res.status(500).json({ error: 'Failed to transfer vehicle ownership' });
  }
});

// Add vehicle images
router.post('/:id/images', auth, async (req, res) => {
  try {
    const { images } = req.body; // Array of { url, description }

    const vehicle = await Vehicle.findOne({
      blockchainId: req.params.id,
      isActive: true
    });

    if (!vehicle) {
      return res.status(404).json({ error: 'Vehicle not found' });
    }

    // Check ownership
    if (vehicle.owner !== req.user.walletAddress && req.user.role !== 'admin') {
      return res.status(403).json({ error: 'Access denied' });
    }

    vehicle.images.push(...images);
    await vehicle.save();

    res.json({
      message: 'Images added successfully',
      vehicle
    });

  } catch (error) {
    console.error('Add images error:', error);
    res.status(500).json({ error: 'Failed to add images' });
  }
});

module.exports = router;
