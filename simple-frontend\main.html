<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🛡️ Himaya Blockchain Insurance</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 50%, #1a1a1a 100%);
            color: #e5e5e5;
            min-height: 100vh;
            overflow-x: hidden;
        }
        
        .splash-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #8b5cf6 0%, #3b82f6 50%, #06b6d4 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            animation: fadeOut 1s ease 3s forwards;
        }
        
        .splash-content {
            text-align: center;
            animation: pulse 2s ease-in-out infinite;
        }
        
        .splash-logo {
            font-size: 6rem;
            margin-bottom: 1rem;
            animation: bounce 2s ease-in-out infinite;
        }
        
        .splash-title {
            font-size: 3rem;
            font-weight: 700;
            color: white;
            margin-bottom: 1rem;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }
        
        .splash-subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            font-weight: 300;
        }
        
        @keyframes fadeOut {
            to { opacity: 0; visibility: hidden; }
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-20px); }
            60% { transform: translateY(-10px); }
        }
        
        .main-content {
            opacity: 0;
            animation: fadeIn 1s ease 4s forwards;
            padding: 2rem;
        }
        
        @keyframes fadeIn {
            to { opacity: 1; }
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 4rem;
            background: rgba(139, 92, 246, 0.1);
            border: 1px solid #8b5cf6;
            border-radius: 20px;
            padding: 3rem;
        }
        
        .logo {
            font-size: 5rem;
            margin-bottom: 1rem;
            animation: float 3s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        
        .title {
            font-size: 3rem;
            font-weight: 700;
            background: linear-gradient(135deg, #8b5cf6, #3b82f6, #06b6d4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1rem;
        }
        
        .subtitle {
            font-size: 1.3rem;
            color: #9ca3af;
            margin-bottom: 2rem;
        }
        
        .description {
            font-size: 1.1rem;
            color: #e5e7eb;
            max-width: 800px;
            margin: 0 auto;
            line-height: 1.6;
        }
        
        .apps-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }
        
        .app-card {
            background: #2d3748;
            border: 1px solid #4a5568;
            border-radius: 20px;
            padding: 2rem;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }
        
        .app-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            transition: left 0.5s ease;
        }
        
        .app-card:hover::before {
            left: 100%;
        }
        
        .app-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(139, 92, 246, 0.3);
            border-color: #8b5cf6;
        }
        
        .app-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
            display: block;
        }
        
        .app-title {
            font-size: 1.8rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #f7fafc;
        }
        
        .app-description {
            color: #9ca3af;
            margin-bottom: 2rem;
            line-height: 1.5;
        }
        
        .app-btn {
            background: linear-gradient(135deg, #8b5cf6, #3b82f6);
            border: none;
            border-radius: 12px;
            padding: 1rem 2rem;
            color: white;
            font-weight: 600;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .app-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(139, 92, 246, 0.4);
        }
        
        .app-card.new-client {
            border-color: #10b981;
            background: linear-gradient(135deg, #2d3748 0%, rgba(16, 185, 129, 0.1) 100%);
        }
        
        .app-card.new-client:hover {
            border-color: #10b981;
            box-shadow: 0 20px 40px rgba(16, 185, 129, 0.3);
        }
        
        .app-card.new-client .app-btn {
            background: linear-gradient(135deg, #10b981, #059669);
        }
        
        .app-card.verified-client {
            border-color: #3b82f6;
            background: linear-gradient(135deg, #2d3748 0%, rgba(59, 130, 246, 0.1) 100%);
        }
        
        .app-card.verified-client:hover {
            border-color: #3b82f6;
            box-shadow: 0 20px 40px rgba(59, 130, 246, 0.3);
        }
        
        .app-card.insurer {
            border-color: #f59e0b;
            background: linear-gradient(135deg, #2d3748 0%, rgba(245, 158, 11, 0.1) 100%);
        }
        
        .app-card.insurer:hover {
            border-color: #f59e0b;
            box-shadow: 0 20px 40px rgba(245, 158, 11, 0.3);
        }
        
        .app-card.insurer .app-btn {
            background: linear-gradient(135deg, #f59e0b, #d97706);
        }
        
        .app-card.admin {
            border-color: #ef4444;
            background: linear-gradient(135deg, #2d3748 0%, rgba(239, 68, 68, 0.1) 100%);
        }
        
        .app-card.admin:hover {
            border-color: #ef4444;
            box-shadow: 0 20px 40px rgba(239, 68, 68, 0.3);
        }
        
        .app-card.admin .app-btn {
            background: linear-gradient(135deg, #ef4444, #dc2626);
        }
        
        .security-info {
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid #10b981;
            border-radius: 16px;
            padding: 2rem;
            margin-bottom: 3rem;
        }
        
        .security-title {
            color: #10b981;
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            text-align: center;
        }
        
        .security-features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-top: 1.5rem;
        }
        
        .security-feature {
            text-align: center;
            padding: 1rem;
        }
        
        .security-feature-icon {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }
        
        .security-feature-title {
            color: #8b5cf6;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .security-feature-desc {
            color: #9ca3af;
            font-size: 0.9rem;
        }
        
        .footer {
            text-align: center;
            padding: 2rem;
            color: #6b7280;
            border-top: 1px solid #374151;
            margin-top: 3rem;
        }
        
        @media (max-width: 768px) {
            .apps-grid { grid-template-columns: 1fr; }
            .title { font-size: 2.5rem; }
            .splash-title { font-size: 2rem; }
            .security-features { grid-template-columns: 1fr; }
        }
    </style>
</head>
<body>
    <!-- Splash Screen -->
    <div class="splash-screen">
        <div class="splash-content">
            <div class="splash-logo">🛡️</div>
            <h1 class="splash-title">HIMAYA</h1>
            <p class="splash-subtitle">Blockchain Insurance Platform</p>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <div class="container">
            <!-- Header -->
            <div class="header">
                <div class="logo">🛡️</div>
                <h1 class="title">HIMAYA BLOCKCHAIN</h1>
                <p class="subtitle">Plateforme d'Assurance Décentralisée</p>
                <p class="description">
                    Révolutionnez votre expérience d'assurance avec la technologie blockchain. 
                    Transparence totale, transactions sécurisées, et vérification d'identité avancée 
                    pour une protection complète de vos véhicules.
                </p>
            </div>

            <!-- Security Information -->
            <div class="security-info">
                <h2 class="security-title">🔐 Système de Sécurité Multi-Niveaux</h2>
                <p style="text-align: center; color: #e5e7eb; margin-bottom: 1rem;">
                    Chaque utilisateur est vérifié par notre équipe avant d'accéder à la plateforme
                </p>
                <div class="security-features">
                    <div class="security-feature">
                        <div class="security-feature-icon">🆔</div>
                        <div class="security-feature-title">Vérification d'Identité</div>
                        <div class="security-feature-desc">Documents officiels + Biométrie</div>
                    </div>
                    <div class="security-feature">
                        <div class="security-feature-icon">👨‍💼</div>
                        <div class="security-feature-title">Approbation Manuelle</div>
                        <div class="security-feature-desc">Équipe d'experts vérifie chaque demande</div>
                    </div>
                    <div class="security-feature">
                        <div class="security-feature-icon">🔐</div>
                        <div class="security-feature-title">Wallets Sécurisés</div>
                        <div class="security-feature-desc">Fournis par l'assureur après approbation</div>
                    </div>
                    <div class="security-feature">
                        <div class="security-feature-icon">⛓️</div>
                        <div class="security-feature-title">Blockchain Immutable</div>
                        <div class="security-feature-desc">Toutes les transactions sont traçables</div>
                    </div>
                </div>
            </div>

            <!-- Applications Grid -->
            <div class="apps-grid">
                <!-- New Client Registration -->
                <div class="app-card new-client" onclick="window.location.href='client-registration.html'">
                    <div class="app-icon">👤</div>
                    <h2 class="app-title">Nouveau Client</h2>
                    <p class="app-description">
                        Créez votre compte en soumettant vos documents d'identité pour vérification. 
                        Notre équipe vérifiera votre identité sous 24-48h.
                    </p>
                    <a href="client-registration.html" class="app-btn">📝 Commencer l'Inscription</a>
                </div>

                <!-- Verified Client App -->
                <div class="app-card verified-client" onclick="window.location.href='client-app.html'">
                    <div class="app-icon">🔐</div>
                    <h2 class="app-title">Client Vérifié</h2>
                    <p class="app-description">
                        Accédez à votre compte avec votre wallet vérifié. Souscrivez aux plans, 
                        enregistrez vos véhicules et gérez vos réclamations.
                    </p>
                    <a href="client-app.html" class="app-btn">🚀 Accéder à Mon Compte</a>
                </div>

                <!-- Insurer App -->
                <div class="app-card insurer" onclick="window.location.href='insurer-app.html'">
                    <div class="app-icon">🏢</div>
                    <h2 class="app-title">Assureur</h2>
                    <p class="app-description">
                        Gérez les polices d'assurance, approuvez les réclamations avec transferts de fonds réels, 
                        et assignez des wallets aux clients vérifiés.
                    </p>
                    <a href="insurer-app.html" class="app-btn">🏢 Dashboard Assureur</a>
                </div>

                <!-- Admin App -->
                <div class="app-card admin" onclick="window.location.href='admin-app.html'">
                    <div class="app-icon">⚙️</div>
                    <h2 class="app-title">Administration</h2>
                    <p class="app-description">
                        Administration complète du système. Gérez les vérifications d'identité, 
                        surveillez la blockchain et contrôlez les opérations critiques.
                    </p>
                    <a href="admin-app.html" class="app-btn">⚙️ Panneau Admin</a>
                </div>
            </div>

            <!-- Additional Info -->
            <div style="background: rgba(139, 92, 246, 0.1); border: 1px solid #8b5cf6; border-radius: 16px; padding: 2rem; text-align: center;">
                <h3 style="color: #8b5cf6; margin-bottom: 1rem;">🌟 Pourquoi Choisir Himaya ?</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; color: #e5e7eb;">
                    <div>✅ <strong>Transparence Totale</strong><br>Toutes les transactions sur blockchain</div>
                    <div>✅ <strong>Sécurité Maximale</strong><br>Vérification d'identité obligatoire</div>
                    <div>✅ <strong>Paiements Instantanés</strong><br>Transferts ETH automatiques</div>
                    <div>✅ <strong>Zéro Fraude</strong><br>Seuls les vrais clients accèdent</div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p>&copy; 2024 Himaya Blockchain Insurance. Tous droits réservés.</p>
            <p>Plateforme d'assurance décentralisée sécurisée par la blockchain Ethereum</p>
        </div>
    </div>

    <script>
        console.log('🛡️ Himaya Blockchain Insurance Platform');
        console.log('🔐 Multi-level security system active');
        console.log('⛓️ Real blockchain integration enabled');
        console.log('🇲🇦 Localized for Morocco market');
    </script>
</body>
</html>
