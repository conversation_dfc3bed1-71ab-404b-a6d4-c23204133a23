#!/bin/bash

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker services are running
check_docker_services() {
    print_status "Checking Docker services..."
    
    if ! docker ps | grep -q "geth-private-enterprise"; then
        print_error "Blockchain container is not running."
        return 1
    fi
    
    if ! docker ps | grep -q "insurance-mongodb"; then
        print_error "MongoDB container is not running."
        return 1
    fi
    
    if ! docker ps | grep -q "insurance-redis"; then
        print_error "Redis container is not running."
        return 1
    }
    
    print_status "All required Docker services are running."
    return 0
}

# Start Docker services if not running
start_docker_services() {
    print_status "Starting Docker services..."
    docker-compose up -d
    
    # Wait for services to be ready
    print_status "Waiting for services to start..."
    sleep 10
    
    if check_docker_services; then
        print_status "Docker services started successfully."
    else
        print_error "Some Docker services failed to start. Check the logs."
        return 1
    fi
    
    return 0
}

# Start the backend server
start_backend() {
    print_status "Starting backend server..."
    cd backend
    
    # Check if node_modules exists
    if [ ! -d "node_modules" ]; then
        print_warning "node_modules not found. Installing dependencies..."
        npm install
    fi
    
    # Start the server in the background
    npm start &
    BACKEND_PID=$!
    
    # Wait for backend to start
    print_status "Waiting for backend to start..."
    sleep 5
    
    # Check if backend is running
    if kill -0 $BACKEND_PID 2>/dev/null; then
        print_status "Backend server started successfully. PID: $BACKEND_PID"
        cd ..
        return 0
    else
        print_error "Failed to start backend server."
        cd ..
        return 1
    fi
}

# Start the frontend server
start_frontend() {
    print_status "Starting frontend server..."
    cd frontend
    
    # Check if node_modules exists
    if [ ! -d "node_modules" ]; then
        print_warning "node_modules not found. Installing dependencies..."
        npm install
    fi
    
    # Start the server in the background
    npm start &
    FRONTEND_PID=$!
    
    # Wait for frontend to start
    print_status "Waiting for frontend to start..."
    sleep 5
    
    # Check if frontend is running
    if kill -0 $FRONTEND_PID 2>/dev/null; then
        print_status "Frontend server started successfully. PID: $FRONTEND_PID"
        cd ..
        return 0
    else
        print_error "Failed to start frontend server."
        cd ..
        return 1
    fi
}

# Check for deployment
check_deployment() {
    if [ ! -f "deployments.json" ]; then
        print_warning "Smart contracts are not deployed. Attempting to deploy..."
        cd blockchain
        npm run deploy
        DEPLOY_STATUS=$?
        cd ..
        
        if [ $DEPLOY_STATUS -ne 0 ]; then
            print_error "Smart contract deployment failed."
            return 1
        fi
        
        print_status "Smart contracts deployed successfully!"
        
        # Update backend environment with contract addresses
        VEHICLE_REGISTRY_ADDRESS=$(node -p "JSON.parse(require('fs').readFileSync('deployments.json', 'utf8')).contracts.VehicleRegistry.address")
        INSURANCE_POLICY_ADDRESS=$(node -p "JSON.parse(require('fs').readFileSync('deployments.json', 'utf8')).contracts.InsurancePolicy.address")
        CLAIM_MANAGER_ADDRESS=$(node -p "JSON.parse(require('fs').readFileSync('deployments.json', 'utf8')).contracts.ClaimManager.address")
        
        # Update .env file
        sed -i "s/VEHICLE_REGISTRY_ADDRESS=.*/VEHICLE_REGISTRY_ADDRESS=$VEHICLE_REGISTRY_ADDRESS/" backend/.env
        sed -i "s/INSURANCE_POLICY_ADDRESS=.*/INSURANCE_POLICY_ADDRESS=$INSURANCE_POLICY_ADDRESS/" backend/.env
        sed -i "s/CLAIM_MANAGER_ADDRESS=.*/CLAIM_MANAGER_ADDRESS=$CLAIM_MANAGER_ADDRESS/" backend/.env
        
        print_status "Contract addresses updated in backend/.env"
    fi
    
    return 0
}

# Clean up function to kill processes on exit
cleanup() {
    print_status "Cleaning up..."
    
    if [ -n "$BACKEND_PID" ]; then
        print_status "Stopping backend server (PID: $BACKEND_PID)..."
        kill $BACKEND_PID 2>/dev/null
    fi
    
    if [ -n "$FRONTEND_PID" ]; then
        print_status "Stopping frontend server (PID: $FRONTEND_PID)..."
        kill $FRONTEND_PID 2>/dev/null
    fi
    
    print_status "Cleanup complete."
}

# Register the cleanup function for when the script exits
trap cleanup EXIT

# Main function
main() {
    print_status "Starting Insurance DApp..."
    
    # Check if Docker services are running
    if ! check_docker_services; then
        # Start Docker services if not running
        if ! start_docker_services; then
            print_error "Failed to start Docker services. Exiting."
            exit 1
        fi
    fi
    
    # Check and deploy contracts if necessary
    if ! check_deployment; then
        print_warning "Deployment check failed, but continuing..."
    fi
    
    # Start backend server
    if ! start_backend; then
        print_error "Failed to start backend server. Exiting."
        exit 1
    fi
    
    # Start frontend server
    if ! start_frontend; then
        print_error "Failed to start frontend server. Exiting."
        exit 1
    fi
    
    print_status "Insurance DApp is now running! 🚀"
    print_status "- Backend: http://localhost:3001"
    print_status "- Frontend: http://localhost:3000"
    print_status "Press Ctrl+C to stop all services."
    
    # Keep the script running
    while true; do
        sleep 1
    done
}

# Run the main function
main