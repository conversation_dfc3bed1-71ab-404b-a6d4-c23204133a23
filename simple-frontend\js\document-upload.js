/**
 * Document Upload Component for SecureShield Insurance
 * Handles file uploads, validation, and preview for claims
 */

class DocumentUpload {
    constructor() {
        this.maxFileSize = 10 * 1024 * 1024; // 10MB
        this.allowedTypes = [
            'image/jpeg',
            'image/png',
            'image/gif',
            'image/webp',
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        ];
        this.uploadedFiles = new Map();
        this.init();
    }

    init() {
        this.setupDragAndDrop();
        this.setupFileInputs();
    }

    createUploadArea(container, options = {}) {
        const {
            multiple = true,
            accept = this.allowedTypes.join(','),
            maxFiles = 5,
            title = 'Upload Documents',
            description = 'Drag and drop files here or click to browse',
            hint = 'Supported formats: JPG, PNG, PDF, DOC (Max 10MB each)'
        } = options;

        const uploadId = 'upload-' + Date.now();
        
        const uploadHTML = `
            <div id="${uploadId}" class="document-upload-container">
                <div class="upload-header">
                    <h3 class="upload-title">${title}</h3>
                    <span class="upload-counter">0 / ${maxFiles} files</span>
                </div>
                
                <div class="file-upload" data-upload-id="${uploadId}">
                    <input type="file" 
                           id="${uploadId}-input" 
                           class="file-upload-input" 
                           ${multiple ? 'multiple' : ''}
                           accept="${accept}"
                           aria-label="${title}">
                    
                    <div class="file-upload-area" 
                         role="button" 
                         tabindex="0"
                         aria-label="Click to upload files or drag and drop">
                        <div class="file-upload-icon">📁</div>
                        <div class="file-upload-text">${description}</div>
                        <div class="file-upload-hint">${hint}</div>
                    </div>
                </div>
                
                <div class="upload-progress" style="display: none;">
                    <div class="progress-bar-container">
                        <div class="progress-bar progress-primary" style="width: 0%"></div>
                    </div>
                    <div class="upload-status">Uploading...</div>
                </div>
                
                <div class="uploaded-files" id="${uploadId}-files"></div>
            </div>
        `;

        container.innerHTML = uploadHTML;
        this.setupUploadArea(uploadId, options);
        return uploadId;
    }

    setupUploadArea(uploadId, options) {
        const container = document.getElementById(uploadId);
        const input = document.getElementById(`${uploadId}-input`);
        const uploadArea = container.querySelector('.file-upload-area');
        const filesContainer = document.getElementById(`${uploadId}-files`);

        // File input change handler
        input.addEventListener('change', (e) => {
            this.handleFiles(e.target.files, uploadId, options);
        });

        // Click to upload
        uploadArea.addEventListener('click', () => {
            input.click();
        });

        // Keyboard accessibility
        uploadArea.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                input.click();
            }
        });

        // Drag and drop
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            this.handleFiles(e.dataTransfer.files, uploadId, options);
        });
    }

    async handleFiles(files, uploadId, options) {
        const { maxFiles = 5, onUpload = null } = options;
        const container = document.getElementById(uploadId);
        const counter = container.querySelector('.upload-counter');
        const filesContainer = document.getElementById(`${uploadId}-files`);
        
        const currentFiles = this.uploadedFiles.get(uploadId) || [];
        
        // Check file count limit
        if (currentFiles.length + files.length > maxFiles) {
            UI.showNotification(
                `Maximum ${maxFiles} files allowed. You can upload ${maxFiles - currentFiles.length} more files.`,
                'warning'
            );
            return;
        }

        const validFiles = [];
        
        // Validate each file
        for (const file of files) {
            const validation = this.validateFile(file);
            if (validation.valid) {
                validFiles.push(file);
            } else {
                UI.showNotification(validation.error, 'error');
            }
        }

        if (validFiles.length === 0) return;

        // Show upload progress
        this.showUploadProgress(container, true);

        try {
            const uploadedFiles = [];
            
            for (let i = 0; i < validFiles.length; i++) {
                const file = validFiles[i];
                const progress = ((i + 1) / validFiles.length) * 100;
                
                // Update progress
                this.updateUploadProgress(container, progress, `Uploading ${file.name}...`);
                
                // Process file
                const fileData = await this.processFile(file);
                uploadedFiles.push(fileData);
                
                // Add to UI
                this.addFileToUI(filesContainer, fileData, uploadId);
            }

            // Update stored files
            const allFiles = [...currentFiles, ...uploadedFiles];
            this.uploadedFiles.set(uploadId, allFiles);
            
            // Update counter
            counter.textContent = `${allFiles.length} / ${maxFiles} files`;
            
            // Hide progress
            this.showUploadProgress(container, false);
            
            // Callback
            if (onUpload) {
                onUpload(uploadedFiles, allFiles);
            }
            
            UI.showNotification(
                `Successfully uploaded ${uploadedFiles.length} file(s)`,
                'success'
            );
            
        } catch (error) {
            console.error('Upload failed:', error);
            this.showUploadProgress(container, false);
            UI.showNotification(`Upload failed: ${error.message}`, 'error');
        }
    }

    validateFile(file) {
        // Check file type
        if (!this.allowedTypes.includes(file.type)) {
            return {
                valid: false,
                error: `File type not supported: ${file.name}. Please use JPG, PNG, PDF, or DOC files.`
            };
        }

        // Check file size
        if (file.size > this.maxFileSize) {
            return {
                valid: false,
                error: `File too large: ${file.name}. Maximum size is 10MB.`
            };
        }

        return { valid: true };
    }

    async processFile(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            
            reader.onload = (e) => {
                const fileData = {
                    id: 'file-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9),
                    name: file.name,
                    size: file.size,
                    type: file.type,
                    lastModified: file.lastModified,
                    data: e.target.result,
                    preview: this.generatePreview(file, e.target.result),
                    uploadDate: new Date().toISOString()
                };
                
                resolve(fileData);
            };
            
            reader.onerror = () => {
                reject(new Error(`Failed to read file: ${file.name}`));
            };
            
            reader.readAsDataURL(file);
        });
    }

    generatePreview(file, data) {
        if (file.type.startsWith('image/')) {
            return {
                type: 'image',
                url: data
            };
        } else if (file.type === 'application/pdf') {
            return {
                type: 'pdf',
                icon: '📄'
            };
        } else {
            return {
                type: 'document',
                icon: '📄'
            };
        }
    }

    addFileToUI(container, fileData, uploadId) {
        const fileHTML = `
            <div class="uploaded-file" data-file-id="${fileData.id}">
                <div class="file-preview">
                    ${fileData.preview.type === 'image' 
                        ? `<img src="${fileData.preview.url}" alt="${fileData.name}" class="file-thumbnail">`
                        : `<div class="file-icon">${fileData.preview.icon}</div>`
                    }
                </div>
                
                <div class="file-info">
                    <div class="file-name" title="${fileData.name}">${fileData.name}</div>
                    <div class="file-details">
                        <span class="file-size">${this.formatFileSize(fileData.size)}</span>
                        <span class="file-date">${new Date(fileData.uploadDate).toLocaleDateString()}</span>
                    </div>
                </div>
                
                <div class="file-actions">
                    <button class="btn-icon" 
                            onclick="documentUpload.previewFile('${fileData.id}', '${uploadId}')"
                            aria-label="Preview ${fileData.name}">
                        👁️
                    </button>
                    <button class="btn-icon" 
                            onclick="documentUpload.downloadFile('${fileData.id}', '${uploadId}')"
                            aria-label="Download ${fileData.name}">
                        💾
                    </button>
                    <button class="btn-icon btn-danger" 
                            onclick="documentUpload.removeFile('${fileData.id}', '${uploadId}')"
                            aria-label="Remove ${fileData.name}">
                        🗑️
                    </button>
                </div>
            </div>
        `;
        
        container.insertAdjacentHTML('beforeend', fileHTML);
    }

    showUploadProgress(container, show) {
        const progress = container.querySelector('.upload-progress');
        progress.style.display = show ? 'block' : 'none';
    }

    updateUploadProgress(container, percent, status) {
        const progressBar = container.querySelector('.progress-bar');
        const statusText = container.querySelector('.upload-status');
        
        progressBar.style.width = `${percent}%`;
        statusText.textContent = status;
    }

    previewFile(fileId, uploadId) {
        const files = this.uploadedFiles.get(uploadId) || [];
        const file = files.find(f => f.id === fileId);
        
        if (!file) return;

        if (file.preview.type === 'image') {
            UI.showModal(
                `Preview: ${file.name}`,
                `<img src="${file.preview.url}" alt="${file.name}" style="max-width: 100%; height: auto;">`,
                { size: 'large' }
            );
        } else {
            UI.showNotification('Preview not available for this file type', 'info');
        }
    }

    downloadFile(fileId, uploadId) {
        const files = this.uploadedFiles.get(uploadId) || [];
        const file = files.find(f => f.id === fileId);
        
        if (!file) return;

        const link = document.createElement('a');
        link.href = file.data;
        link.download = file.name;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    removeFile(fileId, uploadId) {
        const files = this.uploadedFiles.get(uploadId) || [];
        const updatedFiles = files.filter(f => f.id !== fileId);
        
        this.uploadedFiles.set(uploadId, updatedFiles);
        
        // Remove from UI
        const fileElement = document.querySelector(`[data-file-id="${fileId}"]`);
        if (fileElement) {
            fileElement.remove();
        }
        
        // Update counter
        const container = document.getElementById(uploadId);
        const counter = container.querySelector('.upload-counter');
        const maxFiles = parseInt(counter.textContent.split('/')[1].trim().split(' ')[0]);
        counter.textContent = `${updatedFiles.length} / ${maxFiles} files`;
        
        UI.showNotification('File removed successfully', 'success');
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    getUploadedFiles(uploadId) {
        return this.uploadedFiles.get(uploadId) || [];
    }

    clearFiles(uploadId) {
        this.uploadedFiles.delete(uploadId);
        const filesContainer = document.getElementById(`${uploadId}-files`);
        if (filesContainer) {
            filesContainer.innerHTML = '';
        }
        
        const container = document.getElementById(uploadId);
        const counter = container.querySelector('.upload-counter');
        const maxFiles = parseInt(counter.textContent.split('/')[1].trim().split(' ')[0]);
        counter.textContent = `0 / ${maxFiles} files`;
    }

    setupDragAndDrop() {
        // Prevent default drag behaviors on document
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            document.addEventListener(eventName, (e) => {
                e.preventDefault();
                e.stopPropagation();
            }, false);
        });
    }

    setupFileInputs() {
        // Setup existing file inputs
        document.querySelectorAll('input[type="file"]').forEach(input => {
            if (!input.dataset.enhanced) {
                this.enhanceFileInput(input);
                input.dataset.enhanced = 'true';
            }
        });
    }

    enhanceFileInput(input) {
        const wrapper = document.createElement('div');
        wrapper.className = 'enhanced-file-input';
        
        input.parentNode.insertBefore(wrapper, input);
        wrapper.appendChild(input);
        
        const label = document.createElement('label');
        label.className = 'file-input-label';
        label.textContent = 'Choose File';
        label.setAttribute('for', input.id);
        wrapper.appendChild(label);
        
        input.addEventListener('change', () => {
            const fileName = input.files[0]?.name || 'No file chosen';
            label.textContent = fileName;
        });
    }
}

// Initialize document upload
window.documentUpload = new DocumentUpload();
