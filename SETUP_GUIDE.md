# HIMAYA VEHICLE INSURANCE DAPP - COMPLETE SETUP GUIDE

## 🚀 QUICK START (Demo Mode)

### Option 1: Run Demo Application (Recommended for Video)
```bash
# Simply run this command:
run_app.bat

# Or manually:
npx http-server -p 3000
# Then open: http://localhost:3000/main.html
```

**✅ This gives you a fully functional demo with:**
- All pages working
- Realistic demo data
- Complete user journey simulation
- Professional French interface
- Perfect for video demonstration

---

## ⛓️ FULL BLOCKCHAIN SETUP (Optional)

### Step 1: Install Ganache Desktop (Easier)
1. Download from: https://trufflesuite.com/ganache/
2. Install and run Ganache Desktop
3. Create new workspace:
   - **Workspace Name:** Himaya
   - **Port:** 8545
   - **Chain ID:** 1337
   - **Accounts:** 10
   - **Mnemonic:** Use deterministic

### Step 2: Deploy Smart Contracts
```bash
# Compile contracts
npx truffle compile

# Deploy to Ganache
npx truffle migrate --reset --network development
```

### Step 3: Configure MetaMask
1. Install MetaMask browser extension
2. Add custom network:
   - **Network Name:** Himaya Local
   - **RPC URL:** http://127.0.0.1:8545
   - **Chain ID:** 1337
   - **Currency Symbol:** ETH
3. Import accounts from Ganache:
   - Copy private keys from Ganache
   - Import into MetaMask

---

## 🎬 VIDEO DEMO SCRIPT

### Scene 1: Landing Page (30 seconds)
- Open http://localhost:3000/main.html
- Show professional interface
- Explain role-based navigation
- Highlight blockchain features

### Scene 2: Client Registration (1 minute)
- Click "Nouveau Client"
- Fill Ahmed Ben Ali's information:
  - Nom: Ahmed Ben Ali
  - CIN: AB123456
  - Date: 15/03/1992
  - Téléphone: +212 661-234567
  - Email: <EMAIL>
- Upload documents (simulated)
- Get verification ID

### Scene 3: Status Tracking (30 seconds)
- Click "Vérifier le Statut"
- Enter verification ID
- Show pending status with timeline

### Scene 4: Admin Verification (1 minute)
- Click "Administration"
- Review Ahmed's documents
- Complete verification checklist
- Generate wallet credentials

### Scene 5: Client Dashboard (1.5 minutes)
- Click "Espace Client Vérifié"
- Connect MetaMask (or simulate)
- Register vehicle:
  - Plaque: 123456-A-07
  - Marque: Toyota
  - Modèle: Camry
  - Année: 2022
- Purchase Standard insurance plan
- Show policy confirmation

### Scene 6: Claim Process (1.5 minutes)
- Submit new claim:
  - Type: Accident
  - Description: Minor collision
  - Amount: 1.5 ETH
  - Location: Avenue Mohammed V, Casablanca
- Show claim submission confirmation

### Scene 7: Insurer Processing (1 minute)
- Click "Espace Assureur"
- Review Ahmed's claim
- Examine documents
- Approve and process payment
- Show instant settlement

### Scene 8: Conclusion (30 seconds)
- Highlight benefits:
  - 90% faster processing
  - Complete transparency
  - Blockchain security
  - Cost reduction

---

## 📱 APPLICATION FEATURES

### ✅ Working Features (Demo Mode):
- **Multi-role Interface:** Client, Insurer, Admin
- **Document Upload:** Simulated file handling
- **Status Tracking:** Real-time verification status
- **Vehicle Registration:** Blockchain simulation
- **Insurance Plans:** Basic, Standard, Premium
- **Claims Processing:** End-to-end workflow
- **French Localization:** Moroccan market focus
- **Responsive Design:** Mobile-friendly

### ⛓️ Blockchain Features (With Ganache):
- **Smart Contracts:** VehicleRegistry, InsurancePolicy, ClaimManager
- **Web3 Integration:** Real MetaMask connection
- **ETH Transactions:** Actual blockchain payments
- **Immutable Records:** True blockchain storage
- **Event Logging:** Real-time blockchain events

---

## 🛠️ TROUBLESHOOTING

### Web Server Issues:
```bash
# If port 3000 is busy, try:
npx http-server -p 8080
# Then use: http://localhost:8080/main.html
```

### MetaMask Issues:
- Ensure MetaMask is installed
- Add Himaya network manually
- Import accounts from Ganache
- Check network connection

### Contract Deployment Issues:
```bash
# Reset everything:
npx truffle migrate --reset --network development

# Check Ganache is running:
# Should see accounts and transactions in Ganache UI
```

---

## 📋 DEMO DATA

### Test Users:
- **Ahmed Ben Ali** (CIN: AB123456) - New registration
- **Fatima El Mansouri** (CIN: FM789012) - Approved user
- **Youssef Alami** (CIN: YA345678) - Pending verification

### Test Verification IDs:
- **VER-********** - Pending status
- **VER-********** - Approved status

### Test Claims:
- **CLM-********** - Ahmed's accident claim
- **CLM-********** - Fatima's theft claim

---

## 🎯 SUCCESS CRITERIA

### For Video Demo:
- ✅ Show complete user journey
- ✅ Demonstrate all role interfaces
- ✅ Highlight blockchain benefits
- ✅ Professional presentation
- ✅ French language interface
- ✅ Realistic scenarios

### For Technical Evaluation:
- ✅ Smart contracts deployed
- ✅ Web3 integration working
- ✅ MetaMask connectivity
- ✅ Real blockchain transactions
- ✅ Event handling
- ✅ Error management

---

## 📞 SUPPORT

If you encounter issues:
1. Check that Node.js is installed
2. Ensure ports 3000 and 8545 are available
3. Verify Ganache is running (if using blockchain)
4. Check MetaMask configuration
5. Review browser console for errors

The demo mode works perfectly for video demonstration without requiring blockchain setup!
