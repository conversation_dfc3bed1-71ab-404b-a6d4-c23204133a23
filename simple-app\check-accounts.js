const { ethers } = require('ethers');

async function checkAllAccounts() {
    try {
        console.log('🔍 Checking all available accounts on local blockchain...');
        console.log('');

        const provider = new ethers.JsonRpcProvider('http://localhost:8545');
        
        // Test connection
        const blockNumber = await provider.getBlockNumber();
        console.log(`✅ Connected to blockchain (Block: ${blockNumber})`);
        console.log('');

        // Get all accounts from the node
        try {
            const accounts = await provider.send('eth_accounts', []);
            console.log(`📋 Found ${accounts.length} accounts on the node:`);
            console.log('');

            for (let i = 0; i < accounts.length; i++) {
                const account = accounts[i];
                const balance = await provider.getBalance(account);
                const balanceETH = ethers.formatEther(balance);
                
                console.log(`Account ${i + 1}: ${account}`);
                console.log(`Balance: ${balanceETH} ETH`);
                console.log('');
            }

            // Find accounts with enough balance
            const richAccounts = [];
            for (const account of accounts) {
                const balance = await provider.getBalance(account);
                const balanceETH = parseFloat(ethers.formatEther(balance));
                if (balanceETH >= 200) {
                    richAccounts.push({ address: account, balance: balanceETH });
                }
            }

            if (richAccounts.length > 0) {
                console.log('💰 Accounts with sufficient balance (200+ ETH):');
                richAccounts.forEach((acc, i) => {
                    console.log(`  ${i + 1}. ${acc.address} (${acc.balance} ETH)`);
                });
                console.log('');
                console.log('💡 You can use these accounts to send ETH!');
            } else {
                console.log('❌ No accounts found with 200+ ETH');
                console.log('');
                console.log('💡 Solutions:');
                console.log('  1. Use a different amount (less than available balance)');
                console.log('  2. Fund the accounts using your blockchain setup');
                console.log('  3. Check if your local blockchain has pre-funded accounts');
            }

        } catch (error) {
            console.log('⚠️  Could not get accounts from node, trying manual unlock...');
            console.log('');
            
            // Try to get coinbase (mining account)
            try {
                const coinbase = await provider.send('eth_coinbase', []);
                console.log(`🏦 Coinbase account: ${coinbase}`);
                const balance = await provider.getBalance(coinbase);
                console.log(`Balance: ${ethers.formatEther(balance)} ETH`);
                console.log('');
            } catch (coinbaseError) {
                console.log('❌ Could not get coinbase account');
            }
        }

        // Check the target recipient
        const recipient = '******************************************';
        const recipientBalance = await provider.getBalance(recipient);
        console.log(`🎯 Target recipient: ${recipient}`);
        console.log(`Current balance: ${ethers.formatEther(recipientBalance)} ETH`);

    } catch (error) {
        console.error('❌ Error:', error.message);
        
        if (error.code === 'NETWORK_ERROR') {
            console.log('');
            console.log('💡 Make sure your local blockchain is running on http://localhost:8545');
        }
    }
}

checkAllAccounts();
