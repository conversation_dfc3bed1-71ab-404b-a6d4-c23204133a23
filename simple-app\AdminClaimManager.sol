// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

contract AdminClaimManager {
    address public admin;
    uint256 public totalFunds;
    
    struct Claim {
        uint256 id;
        address claimant;
        uint256 amount;
        string description;
        bool processed;
        bool approved;
        uint256 timestamp;
    }
    
    mapping(uint256 => Claim) public claims;
    mapping(address => uint256[]) public userClaims;
    uint256 public nextClaimId = 1;
    
    event ClaimSubmitted(uint256 indexed claimId, address indexed claimant, uint256 amount);
    event ClaimApproved(uint256 indexed claimId, address indexed claimant, uint256 amount);
    event ClaimRejected(uint256 indexed claimId, address indexed claimant);
    event FundsDeposited(address indexed depositor, uint256 amount);
    event FundsWithdrawn(address indexed admin, uint256 amount);
    
    modifier onlyAdmin() {
        require(msg.sender == admin, "Only admin can perform this action");
        _;
    }
    
    modifier claimExists(uint256 claimId) {
        require(claimId > 0 && claimId < nextClaimId, "Claim does not exist");
        _;
    }
    
    constructor() {
        admin = msg.sender;
    }
    
    // Allow contract to receive ETH
    receive() external payable {
        totalFunds += msg.value;
        emit FundsDeposited(msg.sender, msg.value);
    }
    
    // Deposit funds to the contract
    function depositFunds() external payable {
        require(msg.value > 0, "Must send ETH to deposit");
        totalFunds += msg.value;
        emit FundsDeposited(msg.sender, msg.value);
    }
    
    // Submit a claim
    function submitClaim(uint256 amount, string memory description) external returns (uint256) {
        require(amount > 0, "Claim amount must be greater than 0");
        require(bytes(description).length > 0, "Description cannot be empty");
        
        uint256 claimId = nextClaimId++;
        
        claims[claimId] = Claim({
            id: claimId,
            claimant: msg.sender,
            amount: amount,
            description: description,
            processed: false,
            approved: false,
            timestamp: block.timestamp
        });
        
        userClaims[msg.sender].push(claimId);
        
        emit ClaimSubmitted(claimId, msg.sender, amount);
        return claimId;
    }
    
    // Approve a claim and send payment
    function approveClaim(uint256 claimId) external onlyAdmin claimExists(claimId) {
        Claim storage claim = claims[claimId];
        require(!claim.processed, "Claim already processed");
        require(address(this).balance >= claim.amount, "Insufficient contract funds");
        
        claim.processed = true;
        claim.approved = true;
        
        // Send payment to claimant
        (bool success, ) = payable(claim.claimant).call{value: claim.amount}("");
        require(success, "Payment failed");
        
        totalFunds -= claim.amount;
        
        emit ClaimApproved(claimId, claim.claimant, claim.amount);
    }
    
    // Reject a claim
    function rejectClaim(uint256 claimId) external onlyAdmin claimExists(claimId) {
        Claim storage claim = claims[claimId];
        require(!claim.processed, "Claim already processed");
        
        claim.processed = true;
        claim.approved = false;
        
        emit ClaimRejected(claimId, claim.claimant);
    }
    
    // Get claim details
    function getClaim(uint256 claimId) external view claimExists(claimId) returns (
        uint256 id,
        address claimant,
        uint256 amount,
        string memory description,
        bool processed,
        bool approved,
        uint256 timestamp
    ) {
        Claim memory claim = claims[claimId];
        return (
            claim.id,
            claim.claimant,
            claim.amount,
            claim.description,
            claim.processed,
            claim.approved,
            claim.timestamp
        );
    }
    
    // Get all claims by user
    function getUserClaims(address user) external view returns (uint256[] memory) {
        return userClaims[user];
    }
    
    // Get pending claims (for admin)
    function getPendingClaims() external view returns (uint256[] memory) {
        uint256 pendingCount = 0;
        
        // Count pending claims
        for (uint256 i = 1; i < nextClaimId; i++) {
            if (!claims[i].processed) {
                pendingCount++;
            }
        }
        
        // Create array of pending claim IDs
        uint256[] memory pendingClaims = new uint256[](pendingCount);
        uint256 index = 0;
        
        for (uint256 i = 1; i < nextClaimId; i++) {
            if (!claims[i].processed) {
                pendingClaims[index] = i;
                index++;
            }
        }
        
        return pendingClaims;
    }
    
    // Get contract balance
    function getContractBalance() external view returns (uint256) {
        return address(this).balance;
    }
    
    // Withdraw funds (admin only)
    function withdrawFunds(uint256 amount) external onlyAdmin {
        require(amount <= address(this).balance, "Insufficient balance");
        
        (bool success, ) = payable(admin).call{value: amount}("");
        require(success, "Withdrawal failed");
        
        totalFunds -= amount;
        emit FundsWithdrawn(admin, amount);
    }
    
    // Emergency withdraw all funds
    function emergencyWithdraw() external onlyAdmin {
        uint256 balance = address(this).balance;
        require(balance > 0, "No funds to withdraw");
        
        (bool success, ) = payable(admin).call{value: balance}("");
        require(success, "Emergency withdrawal failed");
        
        totalFunds = 0;
        emit FundsWithdrawn(admin, balance);
    }
    
    // Transfer admin rights
    function transferAdmin(address newAdmin) external onlyAdmin {
        require(newAdmin != address(0), "Invalid admin address");
        admin = newAdmin;
    }
    
    // Get total number of claims
    function getTotalClaims() external view returns (uint256) {
        return nextClaimId - 1;
    }
}
