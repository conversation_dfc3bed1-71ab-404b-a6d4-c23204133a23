// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

contract VehicleRegistry {
    struct Vehicle {
        uint256 id;
        string licensePlate;
        string make;
        string model;
        uint256 year;
        address owner;
        string city;
        bool isActive;
        uint256 registrationDate;
    }
    
    mapping(uint256 => Vehicle) public vehicles;
    mapping(address => uint256[]) public ownerVehicles;
    mapping(string => uint256) public plateToVehicleId;
    
    uint256 public nextVehicleId = 1;
    
    event VehicleRegistered(
        uint256 indexed vehicleId,
        address indexed owner,
        string licensePlate,
        string make,
        string model
    );
    
    event VehicleTransferred(
        uint256 indexed vehicleId,
        address indexed fromOwner,
        address indexed toOwner
    );
    
    modifier onlyVehicleOwner(uint256 _vehicleId) {
        require(vehicles[_vehicleId].owner == msg.sender, "Not vehicle owner");
        _;
    }
    
    function registerVehicle(
        string memory _licensePlate,
        string memory _make,
        string memory _model,
        uint256 _year,
        string memory _city
    ) external returns (uint256) {
        require(bytes(_licensePlate).length > 0, "License plate required");
        require(plateToVehicleId[_licensePlate] == 0, "Vehicle already registered");
        require(_year > 1900 && _year <= 2024, "Invalid year");
        
        uint256 vehicleId = nextVehicleId++;
        
        vehicles[vehicleId] = Vehicle({
            id: vehicleId,
            licensePlate: _licensePlate,
            make: _make,
            model: _model,
            year: _year,
            owner: msg.sender,
            city: _city,
            isActive: true,
            registrationDate: block.timestamp
        });
        
        ownerVehicles[msg.sender].push(vehicleId);
        plateToVehicleId[_licensePlate] = vehicleId;
        
        emit VehicleRegistered(vehicleId, msg.sender, _licensePlate, _make, _model);
        return vehicleId;
    }
    
    function transferOwnership(uint256 _vehicleId, address _newOwner) 
        external 
        onlyVehicleOwner(_vehicleId) 
    {
        require(_newOwner != address(0), "Invalid new owner");
        require(_newOwner != msg.sender, "Cannot transfer to self");
        
        address oldOwner = vehicles[_vehicleId].owner;
        vehicles[_vehicleId].owner = _newOwner;
        
        // Remove from old owner's list
        _removeVehicleFromOwner(oldOwner, _vehicleId);
        
        // Add to new owner's list
        ownerVehicles[_newOwner].push(_vehicleId);
        
        emit VehicleTransferred(_vehicleId, oldOwner, _newOwner);
    }
    
    function getVehicle(uint256 _vehicleId) 
        external 
        view 
        returns (
            uint256 id,
            string memory licensePlate,
            string memory make,
            string memory model,
            uint256 year,
            address owner,
            string memory city,
            bool isActive,
            uint256 registrationDate
        ) 
    {
        Vehicle memory vehicle = vehicles[_vehicleId];
        return (
            vehicle.id,
            vehicle.licensePlate,
            vehicle.make,
            vehicle.model,
            vehicle.year,
            vehicle.owner,
            vehicle.city,
            vehicle.isActive,
            vehicle.registrationDate
        );
    }
    
    function getOwnerVehicles(address _owner) 
        external 
        view 
        returns (uint256[] memory) 
    {
        return ownerVehicles[_owner];
    }
    
    function validateOwnership(uint256 _vehicleId, address _owner) 
        external 
        view 
        returns (bool) 
    {
        return vehicles[_vehicleId].owner == _owner && vehicles[_vehicleId].isActive;
    }
    
    function _removeVehicleFromOwner(address _owner, uint256 _vehicleId) internal {
        uint256[] storage vehicleList = ownerVehicles[_owner];
        for (uint256 i = 0; i < vehicleList.length; i++) {
            if (vehicleList[i] == _vehicleId) {
                vehicleList[i] = vehicleList[vehicleList.length - 1];
                vehicleList.pop();
                break;
            }
        }
    }
    
    function getTotalVehicles() external view returns (uint256) {
        return nextVehicleId - 1;
    }
}
