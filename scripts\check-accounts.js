const hre = require("hardhat");

async function main() {
  console.log("Checking accounts...");

  const signers = await hre.ethers.getSigners();
  console.log("Available signers:", signers.length);

  for (let i = 0; i < signers.length; i++) {
    const address = await signers[i].getAddress();
    const balance = await hre.ethers.provider.getBalance(address);
    console.log(`Account ${i}: ${address} - Balance: ${hre.ethers.formatEther(balance)} ETH`);
  }

  // Also check the dev account from Geth logs
  const devAccount = "0x71562b71999873DB5b2866dF957af199Ec94617F7";
  try {
    const devBalance = await hre.ethers.provider.getBalance(devAccount);
    console.log(`Dev Account: ${devAccount} - Balance: ${hre.ethers.formatEther(devBalance)} ETH`);
  } catch (error) {
    console.log("Could not check dev account balance");
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
