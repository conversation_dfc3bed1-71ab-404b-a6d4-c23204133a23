{"name": "SecureShield Insurance", "short_name": "SecureShield", "description": "Advanced blockchain-powered vehicle insurance platform with comprehensive coverage for all vehicle types", "start_url": "/", "display": "standalone", "background_color": "#0f172a", "theme_color": "#1e40af", "orientation": "portrait-primary", "scope": "/", "lang": "en-US", "categories": ["finance", "insurance", "business"], "icons": [{"src": "data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 192 192'><rect width='192' height='192' fill='%231e40af' rx='24'/><text x='96' y='120' font-size='80' text-anchor='middle' fill='white'>🛡️</text></svg>", "sizes": "192x192", "type": "image/svg+xml", "purpose": "any maskable"}, {"src": "data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512'><rect width='512' height='512' fill='%231e40af' rx='64'/><text x='256' y='320' font-size='200' text-anchor='middle' fill='white'>🛡️</text></svg>", "sizes": "512x512", "type": "image/svg+xml", "purpose": "any maskable"}], "screenshots": [{"src": "data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1280 720'><rect width='1280' height='720' fill='%230f172a'/><rect x='40' y='40' width='1200' height='640' fill='%231e293b' rx='16'/><text x='640' y='360' font-size='48' text-anchor='middle' fill='%23f1f5f9'>SecureShield Insurance Dashboard</text></svg>", "sizes": "1280x720", "type": "image/svg+xml", "form_factor": "wide", "label": "Dashboard view of SecureShield Insurance"}, {"src": "data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 390 844'><rect width='390' height='844' fill='%230f172a'/><rect x='20' y='60' width='350' height='724' fill='%231e293b' rx='16'/><text x='195' y='422' font-size='24' text-anchor='middle' fill='%23f1f5f9'>Mobile Insurance App</text></svg>", "sizes": "390x844", "type": "image/svg+xml", "form_factor": "narrow", "label": "Mobile view of SecureShield Insurance"}], "shortcuts": [{"name": "Submit <PERSON><PERSON><PERSON>", "short_name": "<PERSON><PERSON><PERSON>", "description": "Quickly submit an insurance claim", "url": "/?action=claim", "icons": [{"src": "data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 96 96'><rect width='96' height='96' fill='%23ef4444' rx='12'/><text x='48' y='60' font-size='40' text-anchor='middle' fill='white'>📋</text></svg>", "sizes": "96x96", "type": "image/svg+xml"}]}, {"name": "View Policies", "short_name": "Policies", "description": "View your insurance policies", "url": "/?action=policies", "icons": [{"src": "data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 96 96'><rect width='96' height='96' fill='%2310b981' rx='12'/><text x='48' y='60' font-size='40' text-anchor='middle' fill='white'>📄</text></svg>", "sizes": "96x96", "type": "image/svg+xml"}]}, {"name": "Register Vehicle", "short_name": "Register", "description": "Register a new vehicle", "url": "/?action=register", "icons": [{"src": "data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 96 96'><rect width='96' height='96' fill='%233b82f6' rx='12'/><text x='48' y='60' font-size='40' text-anchor='middle' fill='white'>🚗</text></svg>", "sizes": "96x96", "type": "image/svg+xml"}]}], "related_applications": [], "prefer_related_applications": false, "edge_side_panel": {"preferred_width": 400}, "launch_handler": {"client_mode": "navigate-existing"}, "handle_links": "preferred", "capture_links": "existing-client-navigate"}