<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Himaya - Inscription Nouveau Client</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }

        .header h1 {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .header p {
            opacity: 0.9;
        }

        .form-container {
            padding: 2rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #4a5568;
        }

        input, select, textarea {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #667eea;
        }

        .file-upload {
            border: 2px dashed #cbd5e0;
            border-radius: 8px;
            padding: 2rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .file-upload:hover {
            border-color: #667eea;
            background-color: #f7fafc;
        }

        .file-upload.dragover {
            border-color: #667eea;
            background-color: #ebf8ff;
        }

        .upload-icon {
            font-size: 3rem;
            color: #a0aec0;
            margin-bottom: 1rem;
        }

        .btn {
            padding: 0.75rem 2rem;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-secondary {
            background: #e2e8f0;
            color: #4a5568;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .button-group {
            display: flex;
            gap: 1rem;
            justify-content: center;
            margin-top: 2rem;
        }

        .progress-bar {
            width: 100%;
            height: 4px;
            background: #e2e8f0;
            border-radius: 2px;
            margin-bottom: 2rem;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            width: 33%;
            transition: width 0.3s ease;
        }

        .success-message {
            background: #c6f6d5;
            color: #22543d;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            display: none;
        }

        .error-message {
            background: #fed7d7;
            color: #c53030;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            display: none;
        }

        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .button-group {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚗 Inscription Nouveau Client</h1>
            <p>Rejoignez la révolution de l'assurance véhicule blockchain</p>
        </div>

        <div class="form-container">
            <div class="progress-bar">
                <div class="progress-fill"></div>
            </div>

            <div class="success-message" id="successMessage">
                ✅ Inscription réussie! Votre ID de vérification: <strong id="verificationId"></strong>
            </div>

            <div class="error-message" id="errorMessage">
                ❌ Erreur lors de l'inscription. Veuillez réessayer.
            </div>

            <form id="registrationForm">
                <div class="form-group">
                    <h3>📋 Informations Personnelles</h3>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="firstName">Prénom *</label>
                        <input type="text" id="firstName" name="firstName" required>
                    </div>
                    <div class="form-group">
                        <label for="lastName">Nom *</label>
                        <input type="text" id="lastName" name="lastName" required>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="cin">CIN *</label>
                        <input type="text" id="cin" name="cin" placeholder="AB123456" required>
                    </div>
                    <div class="form-group">
                        <label for="birthDate">Date de Naissance *</label>
                        <input type="date" id="birthDate" name="birthDate" required>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="phone">Téléphone *</label>
                        <input type="tel" id="phone" name="phone" placeholder="+212 6XX-XXXXXX" required>
                    </div>
                    <div class="form-group">
                        <label for="email">Email *</label>
                        <input type="email" id="email" name="email" required>
                    </div>
                </div>

                <div class="form-group">
                    <label for="address">Adresse Complète *</label>
                    <textarea id="address" name="address" rows="3" placeholder="Rue, Quartier, Ville" required></textarea>
                </div>

                <div class="form-group">
                    <h3>📄 Documents Requis</h3>
                    <p style="color: #718096; margin-bottom: 1rem;">Veuillez télécharger des images claires et lisibles</p>
                </div>

                <div class="form-group">
                    <label>CIN Recto *</label>
                    <div class="file-upload" onclick="document.getElementById('cinFront').click()">
                        <div class="upload-icon">📄</div>
                        <p>Cliquez pour télécharger la face avant de votre CIN</p>
                        <input type="file" id="cinFront" name="cinFront" accept="image/*" style="display: none;" required>
                    </div>
                </div>

                <div class="form-group">
                    <label>CIN Verso *</label>
                    <div class="file-upload" onclick="document.getElementById('cinBack').click()">
                        <div class="upload-icon">📄</div>
                        <p>Cliquez pour télécharger la face arrière de votre CIN</p>
                        <input type="file" id="cinBack" name="cinBack" accept="image/*" style="display: none;" required>
                    </div>
                </div>

                <div class="form-group">
                    <label>Photo Selfie *</label>
                    <div class="file-upload" onclick="document.getElementById('selfie').click()">
                        <div class="upload-icon">🤳</div>
                        <p>Cliquez pour télécharger une photo selfie claire</p>
                        <input type="file" id="selfie" name="selfie" accept="image/*" style="display: none;" required>
                    </div>
                </div>

                <div class="button-group">
                    <a href="main.html" class="btn btn-secondary">← Retour</a>
                    <button type="submit" class="btn btn-primary">Soumettre l'Inscription</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // File upload handling
        document.querySelectorAll('input[type="file"]').forEach(input => {
            input.addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    const uploadDiv = e.target.parentElement;
                    uploadDiv.style.borderColor = '#48bb78';
                    uploadDiv.style.backgroundColor = '#c6f6d5';
                    uploadDiv.querySelector('p').textContent = `✅ ${file.name}`;
                }
            });
        });

        // Form submission
        document.getElementById('registrationForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Simulate registration process
            const formData = new FormData(this);
            const data = Object.fromEntries(formData.entries());
            
            // Validate required fields
            const requiredFields = ['firstName', 'lastName', 'cin', 'birthDate', 'phone', 'email', 'address'];
            const missingFields = requiredFields.filter(field => !data[field]);
            
            if (missingFields.length > 0) {
                showError('Veuillez remplir tous les champs obligatoires.');
                return;
            }

            // Check if files are uploaded
            const requiredFiles = ['cinFront', 'cinBack', 'selfie'];
            const missingFiles = requiredFiles.filter(field => !document.getElementById(field).files[0]);
            
            if (missingFiles.length > 0) {
                showError('Veuillez télécharger tous les documents requis.');
                return;
            }

            // Simulate processing
            const submitBtn = document.querySelector('button[type="submit"]');
            submitBtn.textContent = 'Traitement en cours...';
            submitBtn.disabled = true;

            setTimeout(() => {
                // Generate verification ID
                const verificationId = 'VER-' + Date.now().toString().slice(-8);
                
                // Store registration data in localStorage for demo
                localStorage.setItem('registrationData', JSON.stringify({
                    ...data,
                    verificationId: verificationId,
                    status: 'pending',
                    submissionDate: new Date().toISOString()
                }));

                // Show success message
                document.getElementById('verificationId').textContent = verificationId;
                showSuccess();
                
                // Reset form
                this.reset();
                document.querySelectorAll('.file-upload').forEach(div => {
                    div.style.borderColor = '#cbd5e0';
                    div.style.backgroundColor = 'transparent';
                    div.querySelector('p').textContent = div.querySelector('p').textContent.replace('✅ ', '').split(' ')[0] === 'Cliquez' ? div.querySelector('p').textContent : 'Cliquez pour télécharger';
                });

                submitBtn.textContent = 'Soumettre l\'Inscription';
                submitBtn.disabled = false;

                // Update progress
                document.querySelector('.progress-fill').style.width = '100%';
                
                setTimeout(() => {
                    document.querySelector('.progress-fill').style.width = '33%';
                }, 2000);

            }, 2000);
        });

        function showSuccess() {
            document.getElementById('successMessage').style.display = 'block';
            document.getElementById('errorMessage').style.display = 'none';
            setTimeout(() => {
                document.getElementById('successMessage').style.display = 'none';
            }, 10000);
        }

        function showError(message) {
            document.getElementById('errorMessage').textContent = '❌ ' + message;
            document.getElementById('errorMessage').style.display = 'block';
            document.getElementById('successMessage').style.display = 'none';
            setTimeout(() => {
                document.getElementById('errorMessage').style.display = 'none';
            }, 5000);
        }

        // Drag and drop functionality
        document.querySelectorAll('.file-upload').forEach(uploadDiv => {
            uploadDiv.addEventListener('dragover', function(e) {
                e.preventDefault();
                this.classList.add('dragover');
            });

            uploadDiv.addEventListener('dragleave', function(e) {
                e.preventDefault();
                this.classList.remove('dragover');
            });

            uploadDiv.addEventListener('drop', function(e) {
                e.preventDefault();
                this.classList.remove('dragover');
                
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    const input = this.querySelector('input[type="file"]');
                    input.files = files;
                    input.dispatchEvent(new Event('change'));
                }
            });
        });
    </script>
</body>
</html>
