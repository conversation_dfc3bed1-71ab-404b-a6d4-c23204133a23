// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/Counters.sol";

contract VehicleRegistry is Ownable {
    using Counters for Counters.Counter;
    
    Counters.Counter private _vehicleIds;
    
    struct Vehicle {
        uint256 id;
        string vin; // Vehicle Identification Number
        string make;
        string model;
        uint256 year;
        string color;
        address owner;
        bool isRegistered;
        uint256 registrationDate;
    }
    
    mapping(uint256 => Vehicle) public vehicles;
    mapping(string => uint256) public vinToVehicleId;
    mapping(address => uint256[]) public ownerVehicles;
    
    event VehicleRegistered(
        uint256 indexed vehicleId,
        string indexed vin,
        address indexed owner,
        uint256 registrationDate
    );
    
    event VehicleTransferred(
        uint256 indexed vehicleId,
        address indexed previousOwner,
        address indexed newOwner
    );
    
    modifier onlyVehicleOwner(uint256 _vehicleId) {
        require(vehicles[_vehicleId].owner == msg.sender, "Not vehicle owner");
        _;
    }
    
    modifier vehicleExists(uint256 _vehicleId) {
        require(vehicles[_vehicleId].isRegistered, "Vehicle not registered");
        _;
    }
    
    constructor() Ownable(msg.sender) {}
    
    function registerVehicle(
        string memory _vin,
        string memory _make,
        string memory _model,
        uint256 _year,
        string memory _color
    ) external returns (uint256) {
        require(bytes(_vin).length > 0, "VIN cannot be empty");
        require(vinToVehicleId[_vin] == 0, "Vehicle already registered");
        require(_year > 1900 && _year <= 2030, "Invalid year");
        
        _vehicleIds.increment();
        uint256 newVehicleId = _vehicleIds.current();
        
        vehicles[newVehicleId] = Vehicle({
            id: newVehicleId,
            vin: _vin,
            make: _make,
            model: _model,
            year: _year,
            color: _color,
            owner: msg.sender,
            isRegistered: true,
            registrationDate: block.timestamp
        });
        
        vinToVehicleId[_vin] = newVehicleId;
        ownerVehicles[msg.sender].push(newVehicleId);
        
        emit VehicleRegistered(newVehicleId, _vin, msg.sender, block.timestamp);
        
        return newVehicleId;
    }
    
    function transferVehicle(uint256 _vehicleId, address _newOwner) 
        external 
        vehicleExists(_vehicleId) 
        onlyVehicleOwner(_vehicleId) 
    {
        require(_newOwner != address(0), "Invalid new owner address");
        require(_newOwner != vehicles[_vehicleId].owner, "Already owner");
        
        address previousOwner = vehicles[_vehicleId].owner;
        vehicles[_vehicleId].owner = _newOwner;
        
        // Remove from previous owner's list
        _removeVehicleFromOwner(previousOwner, _vehicleId);
        
        // Add to new owner's list
        ownerVehicles[_newOwner].push(_vehicleId);
        
        emit VehicleTransferred(_vehicleId, previousOwner, _newOwner);
    }
    
    function getVehicle(uint256 _vehicleId) 
        external 
        view 
        vehicleExists(_vehicleId) 
        returns (Vehicle memory) 
    {
        return vehicles[_vehicleId];
    }
    
    function getVehicleByVin(string memory _vin) 
        external 
        view 
        returns (Vehicle memory) 
    {
        uint256 vehicleId = vinToVehicleId[_vin];
        require(vehicleId != 0, "Vehicle not found");
        return vehicles[vehicleId];
    }
    
    function getOwnerVehicles(address _owner) 
        external 
        view 
        returns (uint256[] memory) 
    {
        return ownerVehicles[_owner];
    }
    
    function getTotalVehicles() external view returns (uint256) {
        return _vehicleIds.current();
    }
    
    function _removeVehicleFromOwner(address _owner, uint256 _vehicleId) private {
        uint256[] storage vehicles_list = ownerVehicles[_owner];
        for (uint256 i = 0; i < vehicles_list.length; i++) {
            if (vehicles_list[i] == _vehicleId) {
                vehicles_list[i] = vehicles_list[vehicles_list.length - 1];
                vehicles_list.pop();
                break;
            }
        }
    }
}
