/**
 * UI Components Library for SecureShield Insurance
 * Enhanced UX components with accessibility and error handling
 */

class UIComponents {
    constructor() {
        this.notifications = [];
        this.modals = new Map();
        this.tooltips = new Map();
        this.init();
    }

    init() {
        this.createNotificationContainer();
        this.createModalContainer();
        this.createTooltipContainer();
        this.setupKeyboardNavigation();
        this.setupAccessibility();
    }

    // Enhanced Loading States
    showLoading(message = 'Loading...', target = null) {
        const loadingId = 'loading-' + Date.now();
        const loadingHTML = `
            <div id="${loadingId}" class="loading-overlay" role="status" aria-live="polite">
                <div class="loading-content">
                    <div class="loading-spinner">
                        <div class="spinner-ring"></div>
                        <div class="spinner-ring"></div>
                        <div class="spinner-ring"></div>
                    </div>
                    <p class="loading-message" aria-label="${message}">${message}</p>
                    <div class="loading-progress">
                        <div class="progress-bar" id="${loadingId}-progress"></div>
                    </div>
                </div>
            </div>
        `;

        if (target) {
            target.style.position = 'relative';
            target.insertAdjacentHTML('beforeend', loadingHTML);
        } else {
            document.body.insertAdjacentHTML('beforeend', loadingHTML);
        }

        return {
            id: loadingId,
            updateProgress: (percent) => {
                const progressBar = document.getElementById(`${loadingId}-progress`);
                if (progressBar) {
                    progressBar.style.width = `${percent}%`;
                }
            },
            updateMessage: (newMessage) => {
                const messageEl = document.querySelector(`#${loadingId} .loading-message`);
                if (messageEl) {
                    messageEl.textContent = newMessage;
                    messageEl.setAttribute('aria-label', newMessage);
                }
            },
            hide: () => this.hideLoading(loadingId)
        };
    }

    hideLoading(loadingId) {
        const loadingEl = document.getElementById(loadingId);
        if (loadingEl) {
            loadingEl.style.opacity = '0';
            setTimeout(() => loadingEl.remove(), 300);
        }
    }

    // Enhanced Notifications with Actions
    showNotification(message, type = 'info', options = {}) {
        const {
            duration = 5000,
            actions = [],
            persistent = false,
            icon = null,
            position = 'top-right'
        } = options;

        const notificationId = 'notification-' + Date.now();
        const iconHTML = icon ? `<i class="notification-icon ${icon}" aria-hidden="true"></i>` : '';
        const actionsHTML = actions.length > 0 ? `
            <div class="notification-actions">
                ${actions.map(action => `
                    <button class="notification-action" data-action="${action.id}" aria-label="${action.label}">
                        ${action.label}
                    </button>
                `).join('')}
            </div>
        ` : '';

        const notificationHTML = `
            <div id="${notificationId}" class="notification notification-${type} notification-${position}" 
                 role="alert" aria-live="assertive" tabindex="0">
                <div class="notification-content">
                    ${iconHTML}
                    <span class="notification-message">${message}</span>
                    ${!persistent ? '<button class="notification-close" aria-label="Close notification">&times;</button>' : ''}
                </div>
                ${actionsHTML}
            </div>
        `;

        const container = document.getElementById('notification-container');
        container.insertAdjacentHTML('beforeend', notificationHTML);

        const notificationEl = document.getElementById(notificationId);
        
        // Add event listeners
        if (!persistent) {
            const closeBtn = notificationEl.querySelector('.notification-close');
            closeBtn.addEventListener('click', () => this.hideNotification(notificationId));
        }

        // Handle action buttons
        actions.forEach(action => {
            const actionBtn = notificationEl.querySelector(`[data-action="${action.id}"]`);
            if (actionBtn) {
                actionBtn.addEventListener('click', () => {
                    action.callback();
                    if (action.closeOnClick !== false) {
                        this.hideNotification(notificationId);
                    }
                });
            }
        });

        // Auto-hide if not persistent
        if (!persistent && duration > 0) {
            setTimeout(() => this.hideNotification(notificationId), duration);
        }

        // Focus for accessibility
        notificationEl.focus();

        this.notifications.push({ id: notificationId, type, message });
        return notificationId;
    }

    hideNotification(notificationId) {
        const notification = document.getElementById(notificationId);
        if (notification) {
            notification.style.transform = 'translateX(100%)';
            notification.style.opacity = '0';
            setTimeout(() => {
                notification.remove();
                this.notifications = this.notifications.filter(n => n.id !== notificationId);
            }, 300);
        }
    }

    // Enhanced Modal System
    showModal(title, content, options = {}) {
        const {
            size = 'medium',
            closable = true,
            actions = [],
            className = '',
            onClose = null
        } = options;

        const modalId = 'modal-' + Date.now();
        const actionsHTML = actions.length > 0 ? `
            <div class="modal-actions">
                ${actions.map(action => `
                    <button class="btn ${action.className || 'btn-secondary'}" 
                            data-action="${action.id}"
                            ${action.disabled ? 'disabled' : ''}
                            aria-label="${action.label}">
                        ${action.label}
                    </button>
                `).join('')}
            </div>
        ` : '';

        const modalHTML = `
            <div id="${modalId}" class="modal-overlay" role="dialog" aria-modal="true" aria-labelledby="${modalId}-title">
                <div class="modal modal-${size} ${className}">
                    <div class="modal-header">
                        <h2 id="${modalId}-title" class="modal-title">${title}</h2>
                        ${closable ? '<button class="modal-close" aria-label="Close modal">&times;</button>' : ''}
                    </div>
                    <div class="modal-body">
                        ${content}
                    </div>
                    ${actionsHTML}
                </div>
            </div>
        `;

        document.getElementById('modal-container').insertAdjacentHTML('beforeend', modalHTML);
        const modalEl = document.getElementById(modalId);

        // Event listeners
        if (closable) {
            const closeBtn = modalEl.querySelector('.modal-close');
            const overlay = modalEl.querySelector('.modal-overlay');
            
            closeBtn.addEventListener('click', () => this.hideModal(modalId));
            overlay.addEventListener('click', (e) => {
                if (e.target === overlay) {
                    this.hideModal(modalId);
                }
            });
        }

        // Handle action buttons
        actions.forEach(action => {
            const actionBtn = modalEl.querySelector(`[data-action="${action.id}"]`);
            if (actionBtn) {
                actionBtn.addEventListener('click', () => {
                    const result = action.callback();
                    if (result !== false && action.closeOnClick !== false) {
                        this.hideModal(modalId);
                    }
                });
            }
        });

        // Keyboard navigation
        this.setupModalKeyboard(modalId);

        // Focus management
        const firstFocusable = modalEl.querySelector('button, input, select, textarea, [tabindex]:not([tabindex="-1"])');
        if (firstFocusable) {
            firstFocusable.focus();
        }

        this.modals.set(modalId, { onClose });
        return modalId;
    }

    hideModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            const modalData = this.modals.get(modalId);
            if (modalData && modalData.onClose) {
                modalData.onClose();
            }
            
            modal.style.opacity = '0';
            setTimeout(() => {
                modal.remove();
                this.modals.delete(modalId);
            }, 300);
        }
    }

    // Progress Indicators
    createProgressBar(container, options = {}) {
        const {
            value = 0,
            max = 100,
            label = '',
            showPercentage = true,
            animated = true,
            color = 'primary'
        } = options;

        const progressId = 'progress-' + Date.now();
        const percentage = Math.round((value / max) * 100);
        
        const progressHTML = `
            <div id="${progressId}" class="progress-container">
                ${label ? `<label class="progress-label" for="${progressId}-bar">${label}</label>` : ''}
                <div class="progress-bar-container">
                    <div id="${progressId}-bar" class="progress-bar progress-${color} ${animated ? 'animated' : ''}" 
                         role="progressbar" 
                         aria-valuenow="${value}" 
                         aria-valuemin="0" 
                         aria-valuemax="${max}"
                         aria-label="${label || 'Progress'}"
                         style="width: ${percentage}%">
                    </div>
                    ${showPercentage ? `<span class="progress-percentage">${percentage}%</span>` : ''}
                </div>
            </div>
        `;

        container.insertAdjacentHTML('beforeend', progressHTML);

        return {
            id: progressId,
            update: (newValue) => {
                const bar = document.getElementById(`${progressId}-bar`);
                const percentageEl = document.querySelector(`#${progressId} .progress-percentage`);
                const newPercentage = Math.round((newValue / max) * 100);
                
                if (bar) {
                    bar.style.width = `${newPercentage}%`;
                    bar.setAttribute('aria-valuenow', newValue);
                }
                if (percentageEl) {
                    percentageEl.textContent = `${newPercentage}%`;
                }
            },
            complete: () => {
                const container = document.getElementById(progressId);
                if (container) {
                    container.classList.add('progress-complete');
                }
            }
        };
    }

    // Enhanced Tooltips
    addTooltip(element, content, options = {}) {
        const {
            position = 'top',
            trigger = 'hover',
            delay = 500,
            className = ''
        } = options;

        const tooltipId = 'tooltip-' + Date.now();
        element.setAttribute('aria-describedby', tooltipId);
        element.setAttribute('data-tooltip', tooltipId);

        let showTimeout, hideTimeout;

        const showTooltip = (e) => {
            clearTimeout(hideTimeout);
            showTimeout = setTimeout(() => {
                const existingTooltip = document.getElementById(tooltipId);
                if (existingTooltip) return;

                const tooltipHTML = `
                    <div id="${tooltipId}" class="tooltip tooltip-${position} ${className}" role="tooltip">
                        <div class="tooltip-content">${content}</div>
                        <div class="tooltip-arrow"></div>
                    </div>
                `;

                document.getElementById('tooltip-container').insertAdjacentHTML('beforeend', tooltipHTML);
                const tooltip = document.getElementById(tooltipId);
                
                // Position tooltip
                this.positionTooltip(tooltip, element, position);
                
                // Show with animation
                requestAnimationFrame(() => {
                    tooltip.classList.add('tooltip-visible');
                });

            }, delay);
        };

        const hideTooltip = () => {
            clearTimeout(showTimeout);
            hideTimeout = setTimeout(() => {
                const tooltip = document.getElementById(tooltipId);
                if (tooltip) {
                    tooltip.classList.remove('tooltip-visible');
                    setTimeout(() => tooltip.remove(), 200);
                }
            }, 100);
        };

        if (trigger === 'hover') {
            element.addEventListener('mouseenter', showTooltip);
            element.addEventListener('mouseleave', hideTooltip);
            element.addEventListener('focus', showTooltip);
            element.addEventListener('blur', hideTooltip);
        } else if (trigger === 'click') {
            element.addEventListener('click', (e) => {
                e.preventDefault();
                const tooltip = document.getElementById(tooltipId);
                if (tooltip) {
                    hideTooltip();
                } else {
                    showTooltip();
                }
            });
        }

        this.tooltips.set(tooltipId, { element, content, options });
        return tooltipId;
    }

    positionTooltip(tooltip, element, position) {
        const elementRect = element.getBoundingClientRect();
        const tooltipRect = tooltip.getBoundingClientRect();
        
        let top, left;

        switch (position) {
            case 'top':
                top = elementRect.top - tooltipRect.height - 10;
                left = elementRect.left + (elementRect.width - tooltipRect.width) / 2;
                break;
            case 'bottom':
                top = elementRect.bottom + 10;
                left = elementRect.left + (elementRect.width - tooltipRect.width) / 2;
                break;
            case 'left':
                top = elementRect.top + (elementRect.height - tooltipRect.height) / 2;
                left = elementRect.left - tooltipRect.width - 10;
                break;
            case 'right':
                top = elementRect.top + (elementRect.height - tooltipRect.height) / 2;
                left = elementRect.right + 10;
                break;
        }

        // Ensure tooltip stays within viewport
        const viewport = {
            width: window.innerWidth,
            height: window.innerHeight
        };

        if (left < 10) left = 10;
        if (left + tooltipRect.width > viewport.width - 10) {
            left = viewport.width - tooltipRect.width - 10;
        }
        if (top < 10) top = 10;
        if (top + tooltipRect.height > viewport.height - 10) {
            top = viewport.height - tooltipRect.height - 10;
        }

        tooltip.style.top = `${top + window.scrollY}px`;
        tooltip.style.left = `${left + window.scrollX}px`;
    }

    // Create container elements
    createNotificationContainer() {
        if (!document.getElementById('notification-container')) {
            const container = document.createElement('div');
            container.id = 'notification-container';
            container.className = 'notification-container';
            container.setAttribute('aria-live', 'polite');
            document.body.appendChild(container);
        }
    }

    createModalContainer() {
        if (!document.getElementById('modal-container')) {
            const container = document.createElement('div');
            container.id = 'modal-container';
            container.className = 'modal-container';
            document.body.appendChild(container);
        }
    }

    createTooltipContainer() {
        if (!document.getElementById('tooltip-container')) {
            const container = document.createElement('div');
            container.id = 'tooltip-container';
            container.className = 'tooltip-container';
            document.body.appendChild(container);
        }
    }

    // Keyboard navigation setup
    setupKeyboardNavigation() {
        document.addEventListener('keydown', (e) => {
            // ESC key closes modals and notifications
            if (e.key === 'Escape') {
                // Close topmost modal
                const modals = document.querySelectorAll('.modal-overlay');
                if (modals.length > 0) {
                    const topModal = modals[modals.length - 1];
                    const modalId = topModal.id;
                    this.hideModal(modalId);
                    return;
                }

                // Close notifications
                const notifications = document.querySelectorAll('.notification');
                notifications.forEach(notification => {
                    this.hideNotification(notification.id);
                });
            }
        });
    }

    setupModalKeyboard(modalId) {
        const modal = document.getElementById(modalId);
        const focusableElements = modal.querySelectorAll(
            'button, input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );
        
        if (focusableElements.length === 0) return;

        const firstElement = focusableElements[0];
        const lastElement = focusableElements[focusableElements.length - 1];

        modal.addEventListener('keydown', (e) => {
            if (e.key === 'Tab') {
                if (e.shiftKey) {
                    if (document.activeElement === firstElement) {
                        e.preventDefault();
                        lastElement.focus();
                    }
                } else {
                    if (document.activeElement === lastElement) {
                        e.preventDefault();
                        firstElement.focus();
                    }
                }
            }
        });
    }

    // Accessibility enhancements
    setupAccessibility() {
        // Add skip links
        this.addSkipLinks();
        
        // Enhance focus indicators
        this.enhanceFocusIndicators();
        
        // Add ARIA labels where missing
        this.addMissingAriaLabels();
    }

    addSkipLinks() {
        const skipLinks = `
            <div class="skip-links">
                <a href="#main-content" class="skip-link">Skip to main content</a>
                <a href="#navigation" class="skip-link">Skip to navigation</a>
            </div>
        `;
        document.body.insertAdjacentHTML('afterbegin', skipLinks);
    }

    enhanceFocusIndicators() {
        const style = document.createElement('style');
        style.textContent = `
            .focus-visible {
                outline: 2px solid var(--primary-light);
                outline-offset: 2px;
            }
        `;
        document.head.appendChild(style);
    }

    addMissingAriaLabels() {
        // Add ARIA labels to buttons without text
        document.querySelectorAll('button:not([aria-label]):not([aria-labelledby])').forEach(button => {
            if (!button.textContent.trim()) {
                button.setAttribute('aria-label', 'Button');
            }
        });

        // Add ARIA labels to form inputs without labels
        document.querySelectorAll('input:not([aria-label]):not([aria-labelledby])').forEach(input => {
            if (!input.previousElementSibling || input.previousElementSibling.tagName !== 'LABEL') {
                const placeholder = input.getAttribute('placeholder');
                if (placeholder) {
                    input.setAttribute('aria-label', placeholder);
                }
            }
        });
    }
}

}

/**
 * Charts and Data Visualization Library
 */
class ChartsLibrary {
    constructor() {
        this.charts = new Map();
        this.colors = {
            primary: '#3b82f6',
            secondary: '#14b8a6',
            success: '#10b981',
            warning: '#f59e0b',
            error: '#ef4444',
            info: '#6366f1'
        };
    }

    // Create a simple bar chart
    createBarChart(container, data, options = {}) {
        const {
            title = '',
            width = 400,
            height = 300,
            colors = [this.colors.primary],
            animated = true
        } = options;

        const chartId = 'chart-' + Date.now();
        const maxValue = Math.max(...data.map(d => d.value));

        const chartHTML = `
            <div id="${chartId}" class="chart-container">
                ${title ? `<h3 class="chart-title">${title}</h3>` : ''}
                <div class="chart-content" style="width: ${width}px; height: ${height}px;">
                    <svg class="chart-svg" width="${width}" height="${height}" role="img" aria-label="${title}">
                        ${data.map((item, index) => {
                            const barHeight = (item.value / maxValue) * (height - 60);
                            const barWidth = (width - 80) / data.length - 10;
                            const x = 40 + index * (barWidth + 10);
                            const y = height - 40 - barHeight;
                            const color = colors[index % colors.length];

                            return `
                                <rect class="chart-bar ${animated ? 'animated' : ''}"
                                      x="${x}"
                                      y="${y}"
                                      width="${barWidth}"
                                      height="${barHeight}"
                                      fill="${color}"
                                      data-value="${item.value}"
                                      data-label="${item.label}">
                                </rect>
                                <text x="${x + barWidth/2}"
                                      y="${height - 20}"
                                      text-anchor="middle"
                                      class="chart-label">
                                    ${item.label}
                                </text>
                                <text x="${x + barWidth/2}"
                                      y="${y - 5}"
                                      text-anchor="middle"
                                      class="chart-value">
                                    ${item.value}
                                </text>
                            `;
                        }).join('')}
                    </svg>
                </div>
            </div>
        `;

        container.innerHTML = chartHTML;

        if (animated) {
            this.animateChart(chartId);
        }

        this.charts.set(chartId, { type: 'bar', data, options });
        return chartId;
    }

    // Create a donut chart
    createDonutChart(container, data, options = {}) {
        const {
            title = '',
            size = 200,
            innerRadius = 60,
            colors = [this.colors.primary, this.colors.secondary, this.colors.success, this.colors.warning]
        } = options;

        const chartId = 'chart-' + Date.now();
        const radius = size / 2 - 20;
        const centerX = size / 2;
        const centerY = size / 2;
        const total = data.reduce((sum, item) => sum + item.value, 0);

        let currentAngle = 0;
        const paths = data.map((item, index) => {
            const angle = (item.value / total) * 360;
            const startAngle = currentAngle;
            const endAngle = currentAngle + angle;
            currentAngle += angle;

            const startAngleRad = (startAngle - 90) * Math.PI / 180;
            const endAngleRad = (endAngle - 90) * Math.PI / 180;

            const x1 = centerX + radius * Math.cos(startAngleRad);
            const y1 = centerY + radius * Math.sin(startAngleRad);
            const x2 = centerX + radius * Math.cos(endAngleRad);
            const y2 = centerY + radius * Math.sin(endAngleRad);

            const largeArcFlag = angle > 180 ? 1 : 0;

            const pathData = [
                `M ${centerX} ${centerY}`,
                `L ${x1} ${y1}`,
                `A ${radius} ${radius} 0 ${largeArcFlag} 1 ${x2} ${y2}`,
                'Z'
            ].join(' ');

            const color = colors[index % colors.length];
            const percentage = Math.round((item.value / total) * 100);

            return `
                <path d="${pathData}"
                      fill="${color}"
                      class="chart-segment"
                      data-value="${item.value}"
                      data-label="${item.label}"
                      data-percentage="${percentage}">
                </path>
            `;
        }).join('');

        const chartHTML = `
            <div id="${chartId}" class="chart-container">
                ${title ? `<h3 class="chart-title">${title}</h3>` : ''}
                <div class="chart-content">
                    <svg class="chart-svg" width="${size}" height="${size}" role="img" aria-label="${title}">
                        ${paths}
                        <circle cx="${centerX}" cy="${centerY}" r="${innerRadius}" fill="var(--gray-50)"></circle>
                    </svg>
                    <div class="chart-legend">
                        ${data.map((item, index) => `
                            <div class="legend-item">
                                <span class="legend-color" style="background-color: ${colors[index % colors.length]}"></span>
                                <span class="legend-label">${item.label}: ${item.value}</span>
                            </div>
                        `).join('')}
                    </div>
                </div>
            </div>
        `;

        container.innerHTML = chartHTML;
        this.charts.set(chartId, { type: 'donut', data, options });
        return chartId;
    }

    // Create a line chart
    createLineChart(container, data, options = {}) {
        const {
            title = '',
            width = 400,
            height = 300,
            color = this.colors.primary,
            showPoints = true,
            animated = true
        } = options;

        const chartId = 'chart-' + Date.now();
        const maxValue = Math.max(...data.map(d => d.value));
        const minValue = Math.min(...data.map(d => d.value));
        const range = maxValue - minValue;

        const points = data.map((item, index) => {
            const x = 40 + (index / (data.length - 1)) * (width - 80);
            const y = height - 40 - ((item.value - minValue) / range) * (height - 80);
            return { x, y, value: item.value, label: item.label };
        });

        const pathData = points.map((point, index) =>
            `${index === 0 ? 'M' : 'L'} ${point.x} ${point.y}`
        ).join(' ');

        const chartHTML = `
            <div id="${chartId}" class="chart-container">
                ${title ? `<h3 class="chart-title">${title}</h3>` : ''}
                <div class="chart-content" style="width: ${width}px; height: ${height}px;">
                    <svg class="chart-svg" width="${width}" height="${height}" role="img" aria-label="${title}">
                        <path d="${pathData}"
                              stroke="${color}"
                              stroke-width="3"
                              fill="none"
                              class="chart-line ${animated ? 'animated' : ''}">
                        </path>
                        ${showPoints ? points.map(point => `
                            <circle cx="${point.x}"
                                    cy="${point.y}"
                                    r="4"
                                    fill="${color}"
                                    class="chart-point"
                                    data-value="${point.value}"
                                    data-label="${point.label}">
                            </circle>
                        `).join('') : ''}
                        ${data.map((item, index) => `
                            <text x="${points[index].x}"
                                  y="${height - 20}"
                                  text-anchor="middle"
                                  class="chart-label">
                                ${item.label}
                            </text>
                        `).join('')}
                    </svg>
                </div>
            </div>
        `;

        container.innerHTML = chartHTML;

        if (animated) {
            this.animateChart(chartId);
        }

        this.charts.set(chartId, { type: 'line', data, options });
        return chartId;
    }

    animateChart(chartId) {
        const chart = document.getElementById(chartId);
        const bars = chart.querySelectorAll('.chart-bar');
        const lines = chart.querySelectorAll('.chart-line');

        bars.forEach((bar, index) => {
            bar.style.animationDelay = `${index * 100}ms`;
        });

        lines.forEach(line => {
            const pathLength = line.getTotalLength();
            line.style.strokeDasharray = pathLength;
            line.style.strokeDashoffset = pathLength;
            line.style.animation = 'drawLine 2s ease-in-out forwards';
        });
    }
}

// Initialize components
window.UI = new UIComponents();
window.Charts = new ChartsLibrary();
