# PowerShell script to send 20 ETH to specified wallet
param(
    [string]$RecipientAddress = "******************************************",
    [string]$Amount = "20",
    [string]$GanacheUrl = "http://localhost:8545"
)

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "HIMAYA ETH TRANSFER SCRIPT" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "Target Wallet: $RecipientAddress" -ForegroundColor Yellow
Write-Host "Amount: $Amount ETH" -ForegroundColor Yellow
Write-Host ""

# Check if Node.js is installed
try {
    $nodeVersion = node --version 2>$null
    Write-Host "[✓] Node.js found: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "[ERROR] Node.js is not installed!" -ForegroundColor Red
    Write-Host "Please install Node.js from https://nodejs.org/" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

# Check if Ganache is running
Write-Host "[INFO] Checking if Ganache is running..." -ForegroundColor Blue
try {
    $response = Invoke-RestMethod -Uri $GanacheUrl -Method Post -ContentType "application/json" -Body '{"jsonrpc":"2.0","method":"eth_accounts","params":[],"id":1}' -TimeoutSec 5
    Write-Host "[✓] Ganache is running" -ForegroundColor Green
} catch {
    Write-Host "[ERROR] Ganache is not running!" -ForegroundColor Red
    Write-Host ""
    Write-Host "Please start Ganache first:" -ForegroundColor Yellow
    Write-Host "1. Download Ganache Desktop from: https://trufflesuite.com/ganache/" -ForegroundColor White
    Write-Host "2. Create new workspace with:" -ForegroundColor White
    Write-Host "   - Port: 8545" -ForegroundColor White
    Write-Host "   - Chain ID: 1337" -ForegroundColor White
    Write-Host "   - Accounts: 10" -ForegroundColor White
    Write-Host ""
    Write-Host "OR run: npx ganache --deterministic --accounts 10 --host 0.0.0.0 --port 8545 --chain.chainId 1337" -ForegroundColor Cyan
    Write-Host ""
    Read-Host "Press Enter to exit"
    exit 1
}

# Install web3 if not present
if (-not (Test-Path "node_modules\web3")) {
    Write-Host "[INFO] Installing Web3..." -ForegroundColor Blue
    npm install web3
    if ($LASTEXITCODE -ne 0) {
        Write-Host "[ERROR] Failed to install Web3" -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }
}

Write-Host "[✓] Dependencies ready" -ForegroundColor Green
Write-Host ""
Write-Host "[INFO] Sending $Amount ETH to wallet..." -ForegroundColor Blue
Write-Host ""

# Run the transfer script
node send_eth.js

if ($LASTEXITCODE -eq 0) {
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Green
    Write-Host "TRANSFER COMPLETE" -ForegroundColor Green
    Write-Host "========================================" -ForegroundColor Green
    Write-Host ""
    Write-Host "Next steps:" -ForegroundColor Yellow
    Write-Host "1. Import wallet in MetaMask" -ForegroundColor White
    Write-Host "2. Add Ganache network:" -ForegroundColor White
    Write-Host "   - Network Name: Himaya Local" -ForegroundColor Cyan
    Write-Host "   - RPC URL: http://localhost:8545" -ForegroundColor Cyan
    Write-Host "   - Chain ID: 1337" -ForegroundColor Cyan
    Write-Host "   - Currency Symbol: ETH" -ForegroundColor Cyan
    Write-Host "3. Test Himaya DApp with real balance" -ForegroundColor White
    Write-Host ""
    Write-Host "🎉 Wallet now has 20 ETH for testing!" -ForegroundColor Green
} else {
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Red
    Write-Host "TRANSFER FAILED" -ForegroundColor Red
    Write-Host "========================================" -ForegroundColor Red
    Write-Host ""
    Write-Host "Please check the error messages above and try again." -ForegroundColor Yellow
}

Write-Host ""
Read-Host "Press Enter to exit"
