/**
 * Multi-language Translation System for Himaya Blockchain Insurance
 * Supports English, French, Arabic with RTL support
 */

class LanguageSystem {
    constructor() {
        this.currentLanguage = 'fr'; // Default to French for Morocco
        this.translations = {};
        this.rtlLanguages = ['ar'];
        this.init();
    }

    async init() {
        await this.loadTranslations();
        this.setupLanguageSelector();
        this.detectUserLanguage();
        this.applyLanguage(this.currentLanguage);
    }

    async loadTranslations() {
        this.translations = {
            en: {
                // Header
                appName: "Himaya Blockchain",
                subtitle: "Advanced Blockchain-Powered Vehicle Insurance Platform",
                tagline: "Blockchain-Based Insurance for Morocco",
                
                // Navigation
                dashboard: "Dashboard",
                vehicles: "Vehicles",
                policies: "Policies",
                claims: "Claims",
                analytics: "Analytics",
                settings: "Settings",
                
                // Connection
                connectWallet: "Connect MetaMask Wallet",
                addNetwork: "Add Private Network",
                refreshInfo: "Refresh Info",
                walletConnected: "Wallet Connected",
                walletNotConnected: "Wallet Not Connected",
                networkConnected: "Network Connected",
                networkNotConnected: "Network Not Connected",
                
                // Vehicle Registration
                registerVehicle: "Register Vehicle",
                vehicleType: "Vehicle Type",
                vehicleVin: "VIN/Registration Number",
                vehicleMake: "Make",
                vehicleModel: "Model",
                vehicleYear: "Year",
                vehicleValue: "Value",
                city: "City",
                selectCity: "Select your city",
                
                // Vehicle Types
                car: "Car",
                motorcycle: "Motorcycle",
                truck: "Truck",
                bus: "Bus",
                
                // Insurance
                createPolicy: "Create Policy",
                policyType: "Policy Type",
                coverage: "Coverage",
                premium: "Premium",
                deductible: "Deductible",
                duration: "Duration",
                
                // Coverage Types
                liability: "Liability",
                comprehensive: "Comprehensive",
                collision: "Collision",
                fullCoverage: "Full Coverage",
                
                // Claims
                submitClaim: "Submit Claim",
                claimType: "Claim Type",
                claimDescription: "Description",
                claimAmount: "Claim Amount",
                claimLocation: "Location",
                
                // Claim Types
                accident: "Accident",
                theft: "Theft",
                vandalism: "Vandalism",
                naturalDisaster: "Natural Disaster",
                fire: "Fire",
                other: "Other",
                
                // Currency
                ethToMad: "ETH to MAD Converter",
                exchangeRate: "Exchange Rate",
                lastUpdated: "Last Updated",
                
                // Status
                active: "Active",
                inactive: "Inactive",
                pending: "Pending",
                approved: "Approved",
                rejected: "Rejected",
                expired: "Expired",
                
                // Actions
                save: "Save",
                cancel: "Cancel",
                submit: "Submit",
                edit: "Edit",
                delete: "Delete",
                view: "View",
                download: "Download",
                upload: "Upload",
                refresh: "Refresh",
                
                // Messages
                success: "Success",
                error: "Error",
                warning: "Warning",
                info: "Information",
                loading: "Loading...",
                
                // Analytics
                totalPolicies: "Total Policies",
                totalClaims: "Total Claims",
                totalVehicles: "Total Vehicles",
                totalPremiums: "Total Premiums",
                
                // Moroccan Features
                acapsCompliance: "ACAPS Compliance",
                carteGrise: "Electronic Registration Card",
                assistance24h: "24/7 Assistance",
                medicalNetwork: "Medical Network",
                approvedGarages: "Approved Garages"
            },
            
            fr: {
                // Header
                appName: "Himaya Blockchain",
                subtitle: "Plateforme d'Assurance Véhicules Basée sur la Blockchain",
                tagline: "Assurance Basée sur la Blockchain pour le Maroc",
                
                // Navigation
                dashboard: "Tableau de Bord",
                vehicles: "Véhicules",
                policies: "Polices",
                claims: "Réclamations",
                analytics: "Analyses",
                settings: "Paramètres",
                
                // Connection
                connectWallet: "Connecter Portefeuille MetaMask",
                addNetwork: "Ajouter Réseau Privé",
                refreshInfo: "Actualiser Infos",
                walletConnected: "Portefeuille Connecté",
                walletNotConnected: "Portefeuille Non Connecté",
                networkConnected: "Réseau Connecté",
                networkNotConnected: "Réseau Non Connecté",
                
                // Vehicle Registration
                registerVehicle: "Enregistrer Véhicule",
                vehicleType: "Type de Véhicule",
                vehicleVin: "Numéro VIN/Immatriculation",
                vehicleMake: "Marque",
                vehicleModel: "Modèle",
                vehicleYear: "Année",
                vehicleValue: "Valeur",
                city: "Ville",
                selectCity: "Sélectionnez votre ville",
                
                // Vehicle Types
                car: "Voiture",
                motorcycle: "Moto",
                truck: "Camion",
                bus: "Autobus",
                
                // Insurance
                createPolicy: "Créer Police",
                policyType: "Type de Police",
                coverage: "Couverture",
                premium: "Prime",
                deductible: "Franchise",
                duration: "Durée",
                
                // Coverage Types
                liability: "Responsabilité Civile",
                comprehensive: "Tous Risques",
                collision: "Collision",
                fullCoverage: "Couverture Complète",
                
                // Claims
                submitClaim: "Soumettre Réclamation",
                claimType: "Type de Réclamation",
                claimDescription: "Description",
                claimAmount: "Montant Réclamé",
                claimLocation: "Lieu",
                
                // Claim Types
                accident: "Accident",
                theft: "Vol",
                vandalism: "Vandalisme",
                naturalDisaster: "Catastrophe Naturelle",
                fire: "Incendie",
                other: "Autre",
                
                // Currency
                ethToMad: "Convertisseur ETH vers MAD",
                exchangeRate: "Taux de Change",
                lastUpdated: "Dernière Mise à Jour",
                
                // Status
                active: "Actif",
                inactive: "Inactif",
                pending: "En Attente",
                approved: "Approuvé",
                rejected: "Rejeté",
                expired: "Expiré",
                
                // Actions
                save: "Enregistrer",
                cancel: "Annuler",
                submit: "Soumettre",
                edit: "Modifier",
                delete: "Supprimer",
                view: "Voir",
                download: "Télécharger",
                upload: "Téléverser",
                refresh: "Actualiser",
                
                // Messages
                success: "Succès",
                error: "Erreur",
                warning: "Avertissement",
                info: "Information",
                loading: "Chargement...",
                
                // Analytics
                totalPolicies: "Total Polices",
                totalClaims: "Total Réclamations",
                totalVehicles: "Total Véhicules",
                totalPremiums: "Total Primes",
                
                // Moroccan Features
                acapsCompliance: "Conformité ACAPS",
                carteGrise: "Carte Grise Électronique",
                assistance24h: "Assistance 24h/7j",
                medicalNetwork: "Réseau Médical",
                approvedGarages: "Garages Agréés"
            },
            
            ar: {
                // Header
                appName: "حماية بلوك تشين",
                subtitle: "منصة تأمين المركبات المتقدمة القائمة على البلوك تشين",
                tagline: "تأمين قائم على البلوك تشين للمغرب",
                
                // Navigation
                dashboard: "لوحة التحكم",
                vehicles: "المركبات",
                policies: "البوالص",
                claims: "المطالبات",
                analytics: "التحليلات",
                settings: "الإعدادات",
                
                // Connection
                connectWallet: "ربط محفظة MetaMask",
                addNetwork: "إضافة شبكة خاصة",
                refreshInfo: "تحديث المعلومات",
                walletConnected: "المحفظة متصلة",
                walletNotConnected: "المحفظة غير متصلة",
                networkConnected: "الشبكة متصلة",
                networkNotConnected: "الشبكة غير متصلة",
                
                // Vehicle Registration
                registerVehicle: "تسجيل مركبة",
                vehicleType: "نوع المركبة",
                vehicleVin: "رقم الهوية/التسجيل",
                vehicleMake: "الماركة",
                vehicleModel: "الطراز",
                vehicleYear: "السنة",
                vehicleValue: "القيمة",
                city: "المدينة",
                selectCity: "اختر مدينتك",
                
                // Vehicle Types
                car: "سيارة",
                motorcycle: "دراجة نارية",
                truck: "شاحنة",
                bus: "حافلة",
                
                // Insurance
                createPolicy: "إنشاء بوليصة",
                policyType: "نوع البوليصة",
                coverage: "التغطية",
                premium: "القسط",
                deductible: "التحمل",
                duration: "المدة",
                
                // Coverage Types
                liability: "المسؤولية المدنية",
                comprehensive: "شامل",
                collision: "تصادم",
                fullCoverage: "تغطية كاملة",
                
                // Claims
                submitClaim: "تقديم مطالبة",
                claimType: "نوع المطالبة",
                claimDescription: "الوصف",
                claimAmount: "مبلغ المطالبة",
                claimLocation: "الموقع",
                
                // Claim Types
                accident: "حادث",
                theft: "سرقة",
                vandalism: "تخريب",
                naturalDisaster: "كارثة طبيعية",
                fire: "حريق",
                other: "أخرى",
                
                // Currency
                ethToMad: "محول ETH إلى MAD",
                exchangeRate: "سعر الصرف",
                lastUpdated: "آخر تحديث",
                
                // Status
                active: "نشط",
                inactive: "غير نشط",
                pending: "في الانتظار",
                approved: "موافق عليه",
                rejected: "مرفوض",
                expired: "منتهي الصلاحية",
                
                // Actions
                save: "حفظ",
                cancel: "إلغاء",
                submit: "إرسال",
                edit: "تعديل",
                delete: "حذف",
                view: "عرض",
                download: "تحميل",
                upload: "رفع",
                refresh: "تحديث",
                
                // Messages
                success: "نجح",
                error: "خطأ",
                warning: "تحذير",
                info: "معلومات",
                loading: "جاري التحميل...",
                
                // Analytics
                totalPolicies: "إجمالي البوالص",
                totalClaims: "إجمالي المطالبات",
                totalVehicles: "إجمالي المركبات",
                totalPremiums: "إجمالي الأقساط",
                
                // Moroccan Features
                acapsCompliance: "امتثال ACAPS",
                carteGrise: "الرخصة الإلكترونية",
                assistance24h: "مساعدة 24/7",
                medicalNetwork: "الشبكة الطبية",
                approvedGarages: "الكراجات المعتمدة"
            }
        };
    }

    setupLanguageSelector() {
        const selectorHTML = `
            <div class="language-selector">
                <select id="languageSelect" class="language-dropdown" onchange="languageSystem.changeLanguage(this.value)">
                    <option value="fr">🇫🇷 Français</option>
                    <option value="en">🇺🇸 English</option>
                    <option value="ar">🇲🇦 العربية</option>
                </select>
            </div>
        `;

        const header = document.querySelector('.header');
        if (header) {
            header.insertAdjacentHTML('afterbegin', selectorHTML);
        }
    }

    detectUserLanguage() {
        // Check localStorage first
        const savedLanguage = localStorage.getItem('himaya-language');
        if (savedLanguage && this.translations[savedLanguage]) {
            this.currentLanguage = savedLanguage;
            return;
        }

        // Detect from browser
        const browserLang = navigator.language.split('-')[0];
        if (this.translations[browserLang]) {
            this.currentLanguage = browserLang;
        }
    }

    changeLanguage(language) {
        if (!this.translations[language]) return;
        
        this.currentLanguage = language;
        localStorage.setItem('himaya-language', language);
        this.applyLanguage(language);
        
        // Update selector
        const selector = document.getElementById('languageSelect');
        if (selector) {
            selector.value = language;
        }
    }

    applyLanguage(language) {
        const translations = this.translations[language];
        if (!translations) return;

        // Apply RTL for Arabic
        document.documentElement.dir = this.rtlLanguages.includes(language) ? 'rtl' : 'ltr';
        document.documentElement.lang = language;

        // Update all translatable elements
        document.querySelectorAll('[data-translate]').forEach(element => {
            const key = element.getAttribute('data-translate');
            if (translations[key]) {
                if (element.tagName === 'INPUT' && element.type !== 'submit') {
                    element.placeholder = translations[key];
                } else {
                    element.textContent = translations[key];
                }
            }
        });

        // Update specific elements by ID
        this.updateElementById('appTitle', translations.appName);
        this.updateElementById('appSubtitle', translations.subtitle);
        this.updateElementById('appTagline', translations.tagline);

        // Trigger custom event for other components
        window.dispatchEvent(new CustomEvent('languageChanged', {
            detail: { language, translations }
        }));
    }

    updateElementById(id, text) {
        const element = document.getElementById(id);
        if (element && text) {
            element.textContent = text;
        }
    }

    translate(key) {
        const translations = this.translations[this.currentLanguage];
        return translations && translations[key] ? translations[key] : key;
    }

    getCurrentLanguage() {
        return this.currentLanguage;
    }

    isRTL() {
        return this.rtlLanguages.includes(this.currentLanguage);
    }
}

}

/**
 * Insurer Dashboard System
 * Advanced analytics and management for insurance companies
 */
class InsurerDashboard {
    constructor() {
        this.currentUser = null;
        this.userRole = 'client'; // client, insurer, admin
        this.analytics = {};
        this.init();
    }

    init() {
        this.detectUserRole();
        this.setupRoleBasedUI();
        this.loadAnalytics();
    }

    detectUserRole() {
        // In a real app, this would be determined by authentication
        const savedRole = localStorage.getItem('himaya-user-role') || 'client';
        this.setUserRole(savedRole);
    }

    setUserRole(role) {
        this.userRole = role;
        localStorage.setItem('himaya-user-role', role);
        this.setupRoleBasedUI();

        // Update role indicator
        const roleIndicator = document.getElementById('roleIndicator');
        if (roleIndicator) {
            const roleText = {
                client: 'CLIENT',
                insurer: 'ASSUREUR',
                admin: 'ADMINISTRATEUR'
            };
            roleIndicator.textContent = roleText[role] || 'CLIENT';
            roleIndicator.className = `role-indicator role-${role}`;
        }
    }

    setupRoleBasedUI() {
        // Show/hide elements based on role
        document.querySelectorAll('[data-role]').forEach(element => {
            const allowedRoles = element.getAttribute('data-role').split(',');
            element.style.display = allowedRoles.includes(this.userRole) ? 'block' : 'none';
        });

        // Setup role-specific navigation
        this.setupRoleNavigation();

        // Load role-specific content
        if (this.userRole === 'insurer' || this.userRole === 'admin') {
            this.loadInsurerDashboard();
        }
    }

    setupRoleNavigation() {
        const navHTML = `
            <div class="role-navigation" data-role="insurer,admin">
                <div class="nav-tabs">
                    <button class="nav-tab active" onclick="insurerDashboard.showTab('overview')">
                        📊 Vue d'ensemble
                    </button>
                    <button class="nav-tab" onclick="insurerDashboard.showTab('policies')">
                        📋 Polices
                    </button>
                    <button class="nav-tab" onclick="insurerDashboard.showTab('claims')">
                        🔍 Réclamations
                    </button>
                    <button class="nav-tab" onclick="insurerDashboard.showTab('analytics')">
                        📈 Analyses
                    </button>
                    <button class="nav-tab" onclick="insurerDashboard.showTab('users')">
                        👥 Utilisateurs
                    </button>
                    <button class="nav-tab" onclick="insurerDashboard.showTab('settings')">
                        ⚙️ Paramètres
                    </button>
                </div>
            </div>
        `;

        const container = document.querySelector('.container');
        const statusBar = container.querySelector('.status-bar');
        if (statusBar) {
            statusBar.insertAdjacentHTML('afterend', navHTML);
        }
    }

    loadInsurerDashboard() {
        const dashboardHTML = `
            <div class="insurer-dashboard" data-role="insurer,admin">
                <!-- Overview Tab -->
                <div id="overview-tab" class="dashboard-tab active">
                    <div class="grid grid-cols-4 mb-8">
                        <div class="stat-card">
                            <div class="stat-icon">📋</div>
                            <div class="stat-value" id="totalActivePolicies">0</div>
                            <div class="stat-label">Polices Actives</div>
                            <div class="stat-change positive">+12% ce mois</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">💰</div>
                            <div class="stat-value" id="totalPremiumsCollected">0 MAD</div>
                            <div class="stat-label">Primes Collectées</div>
                            <div class="stat-change positive">+8% ce mois</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">🔍</div>
                            <div class="stat-value" id="pendingClaims">0</div>
                            <div class="stat-label">Réclamations en Attente</div>
                            <div class="stat-change negative">-5% ce mois</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">⚠️</div>
                            <div class="stat-value" id="riskScore">7.2/10</div>
                            <div class="stat-label">Score de Risque</div>
                            <div class="stat-change neutral">Stable</div>
                        </div>
                    </div>

                    <div class="grid grid-cols-2 mb-8">
                        <div class="card">
                            <h3 class="text-xl font-semibold mb-4">Évolution des Polices</h3>
                            <div id="policiesChart"></div>
                        </div>
                        <div class="card">
                            <h3 class="text-xl font-semibold mb-4">Répartition des Réclamations</h3>
                            <div id="claimsDistributionChart"></div>
                        </div>
                    </div>

                    <div class="grid grid-cols-3">
                        <div class="card">
                            <h3 class="text-xl font-semibold mb-4">Alertes Récentes</h3>
                            <div id="recentAlerts"></div>
                        </div>
                        <div class="card">
                            <h3 class="text-xl font-semibold mb-4">Activité Récente</h3>
                            <div id="recentActivity"></div>
                        </div>
                        <div class="card">
                            <h3 class="text-xl font-semibold mb-4">Performance</h3>
                            <div id="performanceMetrics"></div>
                        </div>
                    </div>
                </div>

                <!-- Policies Tab -->
                <div id="policies-tab" class="dashboard-tab" style="display: none;">
                    <div class="card">
                        <div class="flex justify-between items-center mb-6">
                            <h2 class="text-2xl font-bold">Gestion des Polices</h2>
                            <div class="btn-group">
                                <button class="btn btn-secondary" onclick="insurerDashboard.exportPolicies()">
                                    📊 Exporter
                                </button>
                                <button class="btn btn-primary" onclick="insurerDashboard.createPolicy()">
                                    ➕ Nouvelle Police
                                </button>
                            </div>
                        </div>
                        <div id="policiesTable"></div>
                    </div>
                </div>

                <!-- Claims Tab -->
                <div id="claims-tab" class="dashboard-tab" style="display: none;">
                    <div class="card">
                        <div class="flex justify-between items-center mb-6">
                            <h2 class="text-2xl font-bold">Gestion des Réclamations</h2>
                            <div class="btn-group">
                                <button class="btn btn-secondary" onclick="insurerDashboard.exportClaims()">
                                    📊 Exporter
                                </button>
                                <button class="btn btn-accent" onclick="insurerDashboard.processAllClaims()">
                                    ⚡ Traitement Automatique
                                </button>
                            </div>
                        </div>
                        <div id="claimsTable"></div>
                    </div>
                </div>

                <!-- Analytics Tab -->
                <div id="analytics-tab" class="dashboard-tab" style="display: none;">
                    <div class="grid grid-cols-2 mb-8">
                        <div class="card">
                            <h3 class="text-xl font-semibold mb-4">Analyse des Risques</h3>
                            <div id="riskAnalysisChart"></div>
                        </div>
                        <div class="card">
                            <h3 class="text-xl font-semibold mb-4">Prédictions IA</h3>
                            <div id="aiPredictionsChart"></div>
                        </div>
                    </div>
                    <div class="card">
                        <h3 class="text-xl font-semibold mb-4">Analyse Géographique</h3>
                        <div id="geographicAnalysis"></div>
                    </div>
                </div>

                <!-- Users Tab -->
                <div id="users-tab" class="dashboard-tab" style="display: none;">
                    <div class="card">
                        <h2 class="text-2xl font-bold mb-6">Gestion des Utilisateurs</h2>
                        <div id="usersTable"></div>
                    </div>
                </div>

                <!-- Settings Tab -->
                <div id="settings-tab" class="dashboard-tab" style="display: none;">
                    <div class="grid grid-cols-2">
                        <div class="card">
                            <h3 class="text-xl font-semibold mb-4">Paramètres de l'Assureur</h3>
                            <div id="insurerSettings"></div>
                        </div>
                        <div class="card">
                            <h3 class="text-xl font-semibold mb-4">Configuration Blockchain</h3>
                            <div id="blockchainSettings"></div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        const container = document.querySelector('.container');
        const analyticsCard = document.querySelector('#policyChart')?.closest('.card');
        if (analyticsCard) {
            analyticsCard.insertAdjacentHTML('afterend', dashboardHTML);
        }

        this.loadDashboardData();
    }

    showTab(tabName) {
        // Hide all tabs
        document.querySelectorAll('.dashboard-tab').forEach(tab => {
            tab.style.display = 'none';
        });

        // Remove active class from all nav tabs
        document.querySelectorAll('.nav-tab').forEach(tab => {
            tab.classList.remove('active');
        });

        // Show selected tab
        const selectedTab = document.getElementById(`${tabName}-tab`);
        if (selectedTab) {
            selectedTab.style.display = 'block';
        }

        // Add active class to clicked nav tab
        event.target.classList.add('active');

        // Load tab-specific data
        this.loadTabData(tabName);
    }

    loadDashboardData() {
        // Simulate loading real data
        this.analytics = {
            totalActivePolicies: 1247,
            totalPremiumsCollected: 2850000, // MAD
            pendingClaims: 23,
            riskScore: 7.2,
            monthlyGrowth: {
                policies: 12,
                premiums: 8,
                claims: -5
            }
        };

        // Update dashboard stats
        this.updateDashboardStats();
        this.loadDashboardCharts();
    }

    updateDashboardStats() {
        document.getElementById('totalActivePolicies').textContent = this.analytics.totalActivePolicies.toLocaleString();
        document.getElementById('totalPremiumsCollected').textContent = `${this.analytics.totalPremiumsCollected.toLocaleString()} MAD`;
        document.getElementById('pendingClaims').textContent = this.analytics.pendingClaims;
    }

    loadDashboardCharts() {
        // Load charts for insurer dashboard
        if (window.Charts) {
            this.loadPoliciesChart();
            this.loadClaimsDistributionChart();
        }
    }

    loadPoliciesChart() {
        const data = [
            { label: 'Jan', value: 980 },
            { label: 'Fév', value: 1050 },
            { label: 'Mar', value: 1120 },
            { label: 'Avr', value: 1180 },
            { label: 'Mai', value: 1247 }
        ];

        const container = document.getElementById('policiesChart');
        if (container && window.Charts) {
            window.Charts.createLineChart(container, data, {
                title: '',
                color: '#a855f7'
            });
        }
    }

    loadClaimsDistributionChart() {
        const data = [
            { label: 'Accidents', value: 45 },
            { label: 'Vol', value: 23 },
            { label: 'Incendie', value: 12 },
            { label: 'Autres', value: 20 }
        ];

        const container = document.getElementById('claimsDistributionChart');
        if (container && window.Charts) {
            window.Charts.createDonutChart(container, data, {
                title: '',
                colors: ['#a855f7', '#3b82f6', '#10b981', '#f59e0b']
            });
        }
    }

    loadTabData(tabName) {
        switch (tabName) {
            case 'policies':
                this.loadPoliciesData();
                break;
            case 'claims':
                this.loadClaimsData();
                break;
            case 'analytics':
                this.loadAdvancedAnalytics();
                break;
            case 'users':
                this.loadUsersData();
                break;
            case 'settings':
                this.loadSettings();
                break;
        }
    }

    loadPoliciesData() {
        // Load and display policies table
        console.log('Loading policies data...');
    }

    loadClaimsData() {
        // Load and display claims table
        console.log('Loading claims data...');
    }

    loadAdvancedAnalytics() {
        // Load advanced analytics charts
        console.log('Loading advanced analytics...');
    }

    loadUsersData() {
        // Load users management
        console.log('Loading users data...');
    }

    loadSettings() {
        // Load settings interface
        console.log('Loading settings...');
    }

    // Role switching for demo purposes
    switchRole(role) {
        this.setUserRole(role);
        window.location.reload(); // Reload to apply role changes
    }
}

// Initialize systems
window.languageSystem = new LanguageSystem();
window.insurerDashboard = new InsurerDashboard();
