const hre = require("hardhat");
const fs = require("fs");
const path = require("path");

async function main() {
  console.log("Starting deployment to Private Ethereum network...");

  const [deployer, insurer1, insurer2] = await hre.ethers.getSigners();

  console.log("Deploying contracts with the account:", deployer.address);
  console.log("Account balance:", (await deployer.provider.getBalance(deployer.address)).toString());

  // Deploy VehicleRegistry
  console.log("\nDeploying VehicleRegistry...");
  const VehicleRegistry = await hre.ethers.getContractFactory("VehicleRegistry");
  const vehicleRegistry = await VehicleRegistry.deploy();
  await vehicleRegistry.waitForDeployment();
  const vehicleRegistryAddress = await vehicleRegistry.getAddress();
  console.log("VehicleRegistry deployed to:", vehicleRegistryAddress);

  // Deploy InsurancePolicy
  console.log("\nDeploying InsurancePolicy...");
  const InsurancePolicy = await hre.ethers.getContractFactory("InsurancePolicy");
  const insurancePolicy = await InsurancePolicy.deploy(vehicleRegistryAddress);
  await insurancePolicy.waitForDeployment();
  const insurancePolicyAddress = await insurancePolicy.getAddress();
  console.log("InsurancePolicy deployed to:", insurancePolicyAddress);

  // Deploy ClaimManager
  console.log("\nDeploying ClaimManager...");
  const ClaimManager = await hre.ethers.getContractFactory("ClaimManager");
  const claimManager = await ClaimManager.deploy(insurancePolicyAddress, vehicleRegistryAddress);
  await claimManager.waitForDeployment();
  const claimManagerAddress = await claimManager.getAddress();
  console.log("ClaimManager deployed to:", claimManagerAddress);

  // Create some test accounts for insurers
  const testInsurer1 = "******************************************";
  const testInsurer2 = "******************************************";

  // Authorize initial insurers
  console.log("\nAuthorizing initial insurers...");
  await insurancePolicy.authorizeInsurer(testInsurer1);
  console.log("Authorized insurer 1:", testInsurer1);

  await insurancePolicy.authorizeInsurer(testInsurer2);
  console.log("Authorized insurer 2:", testInsurer2);

  // Authorize initial reviewers
  console.log("\nAuthorizing initial reviewers...");
  await claimManager.authorizeReviewer(testInsurer1);
  await claimManager.authorizeReviewer(testInsurer2);
  console.log("Authorized reviewers:", testInsurer1, testInsurer2);

  // Save deployment addresses
  const deploymentInfo = {
    network: "private-ethereum",
    chainId: 1337,
    contracts: {
      VehicleRegistry: {
        address: vehicleRegistryAddress,
        deployer: deployer.address
      },
      InsurancePolicy: {
        address: insurancePolicyAddress,
        deployer: deployer.address
      },
      ClaimManager: {
        address: claimManagerAddress,
        deployer: deployer.address
      }
    },
    accounts: {
      deployer: deployer.address,
      insurer1: testInsurer1,
      insurer2: testInsurer2
    },
    deploymentDate: new Date().toISOString()
  };

  // Save to JSON file
  const deploymentPath = path.join(__dirname, "../deployments.json");
  fs.writeFileSync(deploymentPath, JSON.stringify(deploymentInfo, null, 2));
  console.log("\nDeployment info saved to:", deploymentPath);

  // Save ABI files for backend integration
  const artifactsDir = path.join(__dirname, "../artifacts/contracts");
  const abiDir = path.join(__dirname, "../abi");
  
  if (!fs.existsSync(abiDir)) {
    fs.mkdirSync(abiDir, { recursive: true });
  }

  // Copy ABI files
  const contracts = ["VehicleRegistry", "InsurancePolicy", "ClaimManager"];
  contracts.forEach(contractName => {
    const artifactPath = path.join(artifactsDir, `${contractName}.sol`, `${contractName}.json`);
    const artifact = JSON.parse(fs.readFileSync(artifactPath, "utf8"));
    const abiPath = path.join(abiDir, `${contractName}.json`);
    fs.writeFileSync(abiPath, JSON.stringify(artifact.abi, null, 2));
    console.log(`ABI saved for ${contractName}`);
  });

  console.log("\n=== Deployment Summary ===");
  console.log("VehicleRegistry:", vehicleRegistryAddress);
  console.log("InsurancePolicy:", insurancePolicyAddress);
  console.log("ClaimManager:", claimManagerAddress);
  console.log("Network: Private Ethereum (Chain ID: 1337)");
  console.log("Deployer:", deployer.address);
  console.log("Authorized Insurers:", testInsurer1, testInsurer2);
  console.log("========================");
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
