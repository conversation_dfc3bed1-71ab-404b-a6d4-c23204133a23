const { ethers } = require('ethers');

// Configuration
const RPC_URL = 'http://localhost:8545';
const RECIPIENT_ADDRESS = '******************************************';
const AMOUNT_ETH = '200'; // Amount to send in ETH
const SENDER_ADDRESS = '******************************************'; // Rich account from blockchain

async function sendETHFromUnlockedAccount() {
    try {
        console.log('🚀 Starting ETH transfer from unlocked account...');
        console.log(`📍 From: ${SENDER_ADDRESS}`);
        console.log(`📍 To: ${RECIPIENT_ADDRESS}`);
        console.log(`💰 Amount: ${AMOUNT_ETH} ETH`);
        console.log('');

        // Connect to the local blockchain
        const provider = new ethers.JsonRpcProvider(RPC_URL);
        
        // Test connection
        const blockNumber = await provider.getBlockNumber();
        console.log(`✅ Connected to blockchain (Block: ${blockNumber})`);

        // Check balances before
        const senderBalanceBefore = await provider.getBalance(SENDER_ADDRESS);
        const recipientBalanceBefore = await provider.getBalance(RECIPIENT_ADDRESS);
        
        console.log(`📊 Sender balance before: ${ethers.formatEther(senderBalanceBefore)} ETH`);
        console.log(`📊 Recipient balance before: ${ethers.formatEther(recipientBalanceBefore)} ETH`);
        console.log('');

        // Convert amount to Wei
        const amountWei = ethers.parseEther(AMOUNT_ETH);

        // Prepare transaction using eth_sendTransaction (for unlocked accounts)
        const transactionRequest = {
            from: SENDER_ADDRESS,
            to: RECIPIENT_ADDRESS,
            value: '0x' + amountWei.toString(16), // Convert to hex
            gas: '0x5208', // 21000 in hex (standard ETH transfer)
        };

        console.log('📤 Sending transaction...');
        console.log(`   From: ${SENDER_ADDRESS}`);
        console.log(`   To: ${RECIPIENT_ADDRESS}`);
        console.log(`   Amount: ${AMOUNT_ETH} ETH`);
        console.log(`   Gas: 21000`);
        console.log('');

        // Send transaction using eth_sendTransaction (works with unlocked accounts)
        const txHash = await provider.send('eth_sendTransaction', [transactionRequest]);
        console.log(`🔄 Transaction sent! Hash: ${txHash}`);
        console.log('⏳ Waiting for confirmation...');

        // Wait for transaction to be mined
        let receipt = null;
        let attempts = 0;
        const maxAttempts = 30;

        while (!receipt && attempts < maxAttempts) {
            try {
                receipt = await provider.getTransactionReceipt(txHash);
                if (receipt) {
                    break;
                }
            } catch (error) {
                // Transaction not yet mined
            }
            
            attempts++;
            await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second
            process.stdout.write('.');
        }

        console.log('');

        if (receipt) {
            console.log(`✅ Transaction confirmed in block ${receipt.blockNumber}`);
            console.log(`⛽ Gas used: ${receipt.gasUsed.toString()}`);
            console.log(`📋 Status: ${receipt.status === 1 ? 'Success' : 'Failed'}`);
            console.log('');

            // Check balances after
            const senderBalanceAfter = await provider.getBalance(SENDER_ADDRESS);
            const recipientBalanceAfter = await provider.getBalance(RECIPIENT_ADDRESS);

            console.log('📊 Final Balances:');
            console.log(`   Sender: ${ethers.formatEther(senderBalanceAfter)} ETH`);
            console.log(`   Recipient: ${ethers.formatEther(recipientBalanceAfter)} ETH (+${ethers.formatEther(recipientBalanceAfter - recipientBalanceBefore)} ETH)`);
            console.log('');
            console.log('🎉 Transfer completed successfully!');
            console.log('');
            console.log('💡 You can now use this wallet in MetaMask:');
            console.log(`   Address: ${RECIPIENT_ADDRESS}`);
            console.log(`   Balance: ${ethers.formatEther(recipientBalanceAfter)} ETH`);

        } else {
            throw new Error('Transaction was not confirmed within expected time');
        }

    } catch (error) {
        console.error('❌ Transfer failed:', error.message);
        
        if (error.message.includes('account not found') || error.message.includes('unknown account')) {
            console.log('');
            console.log('💡 The account might not be unlocked. Trying alternative method...');
            await tryAlternativeMethod();
        } else if (error.code === 'NETWORK_ERROR') {
            console.log('');
            console.log('💡 Troubleshooting:');
            console.log('   1. Make sure your local blockchain is running on http://localhost:8545');
            console.log('   2. Check if Geth or Hardhat network is started');
            console.log('   3. Verify the RPC endpoint is accessible');
        }
        
        process.exit(1);
    }
}

async function tryAlternativeMethod() {
    try {
        console.log('🔄 Trying alternative method with personal_unlockAccount...');
        
        const provider = new ethers.JsonRpcProvider(RPC_URL);
        
        // Try to unlock the account
        try {
            await provider.send('personal_unlockAccount', [SENDER_ADDRESS, '', 0]);
            console.log('✅ Account unlocked successfully!');
            
            // Retry the transaction
            await sendETHFromUnlockedAccount();
            
        } catch (unlockError) {
            console.log('❌ Could not unlock account:', unlockError.message);
            console.log('');
            console.log('💡 Alternative solutions:');
            console.log('   1. Use Geth with --allow-insecure-unlock flag');
            console.log('   2. Use Hardhat network which auto-unlocks accounts');
            console.log('   3. Import the account private key if available');
        }
        
    } catch (error) {
        console.error('❌ Alternative method failed:', error.message);
    }
}

// Quick balance check function
async function quickCheck() {
    try {
        const provider = new ethers.JsonRpcProvider(RPC_URL);
        const balance = await provider.getBalance(RECIPIENT_ADDRESS);
        console.log(`Current balance of ${RECIPIENT_ADDRESS}: ${ethers.formatEther(balance)} ETH`);
    } catch (error) {
        console.error('Failed to check balance:', error.message);
    }
}

// Command line interface
const args = process.argv.slice(2);

if (args.includes('--check')) {
    quickCheck();
} else if (args.includes('--help')) {
    console.log('🛡️ Himaya Insurance - ETH Transfer Script (Unlocked Account)');
    console.log('');
    console.log('Usage:');
    console.log('  node send-eth-unlocked.js        # Send 200 ETH to recipient');
    console.log('  node send-eth-unlocked.js --check # Check recipient balance');
    console.log('  node send-eth-unlocked.js --help  # Show this help');
    console.log('');
    console.log(`Sender: ${SENDER_ADDRESS}`);
    console.log(`Recipient: ${RECIPIENT_ADDRESS}`);
    console.log(`Amount: ${AMOUNT_ETH} ETH`);
} else {
    // Default action: send ETH
    sendETHFromUnlockedAccount();
}
