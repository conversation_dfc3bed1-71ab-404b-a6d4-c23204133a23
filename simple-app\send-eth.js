const { ethers } = require('ethers');

// Configuration
const RPC_URL = 'http://localhost:8545';
const RECIPIENT_ADDRESS = '******************************************';
const AMOUNT_ETH = '200'; // Amount to send in ETH

// Pre-funded private keys from your local blockchain
const SENDER_PRIVATE_KEYS = [
    '0x8f2a55949038a9610f50fb23b5883af3b4ecb3c3bb792cbcefbd1542c692be63',
    '0xc87509a1c067bbde78beb793e6fa76530b6382a4c0241e5e4a9ec0a0f44dc0d3',
    '0xae6ae8e5ccbfb04590405997ee2d52d2b330726137b875053c36d94e974d162f'
];

async function sendETH() {
    try {
        console.log('🚀 Starting ETH transfer...');
        console.log(`📍 Recipient: ${RECIPIENT_ADDRESS}`);
        console.log(`💰 Amount: ${AMOUNT_ETH} ETH`);
        console.log('');

        // Connect to the local blockchain
        const provider = new ethers.JsonRpcProvider(RPC_URL);
        
        // Test connection
        const blockNumber = await provider.getBlockNumber();
        console.log(`✅ Connected to blockchain (Block: ${blockNumber})`);

        // Check recipient balance before
        const balanceBefore = await provider.getBalance(RECIPIENT_ADDRESS);
        console.log(`📊 Recipient balance before: ${ethers.formatEther(balanceBefore)} ETH`);
        console.log('');

        // Try each sender account until we find one with enough balance
        let transactionSent = false;
        const amountWei = ethers.parseEther(AMOUNT_ETH);

        for (let i = 0; i < SENDER_PRIVATE_KEYS.length && !transactionSent; i++) {
            try {
                console.log(`🔑 Trying sender account ${i + 1}...`);
                
                // Create wallet from private key
                const wallet = new ethers.Wallet(SENDER_PRIVATE_KEYS[i], provider);
                const senderAddress = wallet.address;
                
                console.log(`   Address: ${senderAddress}`);
                
                // Check sender balance
                const senderBalance = await provider.getBalance(senderAddress);
                const senderBalanceETH = ethers.formatEther(senderBalance);
                
                console.log(`   Balance: ${senderBalanceETH} ETH`);
                
                // Check if sender has enough balance (including gas)
                const gasLimit = 21000n; // Standard ETH transfer gas limit
                const gasPrice = await provider.getFeeData();
                const gasCost = gasLimit * gasPrice.gasPrice;
                const totalCost = amountWei + gasCost;
                
                if (senderBalance < totalCost) {
                    console.log(`   ❌ Insufficient balance (need ${ethers.formatEther(totalCost)} ETH)`);
                    continue;
                }
                
                console.log(`   ✅ Sufficient balance found!`);
                console.log('');
                
                // Prepare transaction
                const transaction = {
                    to: RECIPIENT_ADDRESS,
                    value: amountWei,
                    gasLimit: gasLimit,
                    gasPrice: gasPrice.gasPrice
                };
                
                console.log('📤 Sending transaction...');
                console.log(`   From: ${senderAddress}`);
                console.log(`   To: ${RECIPIENT_ADDRESS}`);
                console.log(`   Amount: ${AMOUNT_ETH} ETH`);
                console.log(`   Gas Price: ${ethers.formatUnits(gasPrice.gasPrice, 'gwei')} gwei`);
                console.log('');
                
                // Send transaction
                const tx = await wallet.sendTransaction(transaction);
                console.log(`🔄 Transaction sent! Hash: ${tx.hash}`);
                console.log('⏳ Waiting for confirmation...');
                
                // Wait for confirmation
                const receipt = await tx.wait();
                console.log(`✅ Transaction confirmed in block ${receipt.blockNumber}`);
                console.log(`⛽ Gas used: ${receipt.gasUsed.toString()}`);
                console.log('');
                
                transactionSent = true;
                
                // Check balances after
                const balanceAfter = await provider.getBalance(RECIPIENT_ADDRESS);
                const senderBalanceAfter = await provider.getBalance(senderAddress);
                
                console.log('📊 Final Balances:');
                console.log(`   Recipient: ${ethers.formatEther(balanceAfter)} ETH (+${ethers.formatEther(balanceAfter - balanceBefore)} ETH)`);
                console.log(`   Sender: ${ethers.formatEther(senderBalanceAfter)} ETH`);
                console.log('');
                console.log('🎉 Transfer completed successfully!');
                
            } catch (error) {
                console.log(`   ❌ Failed with account ${i + 1}: ${error.message}`);
                continue;
            }
        }
        
        if (!transactionSent) {
            throw new Error('No account with sufficient balance found');
        }
        
    } catch (error) {
        console.error('❌ Transfer failed:', error.message);
        
        if (error.code === 'NETWORK_ERROR') {
            console.log('');
            console.log('💡 Troubleshooting:');
            console.log('   1. Make sure your local blockchain is running on http://localhost:8545');
            console.log('   2. Check if Geth or Hardhat network is started');
            console.log('   3. Verify the RPC endpoint is accessible');
        } else if (error.message.includes('insufficient funds')) {
            console.log('');
            console.log('💡 Troubleshooting:');
            console.log('   1. Make sure the sender accounts are pre-funded');
            console.log('   2. Check if the local blockchain has enough ETH in genesis accounts');
            console.log('   3. Try reducing the transfer amount');
        }
        
        process.exit(1);
    }
}

// Additional utility functions
async function checkBalance(address) {
    try {
        const provider = new ethers.JsonRpcProvider(RPC_URL);
        const balance = await provider.getBalance(address);
        console.log(`Balance of ${address}: ${ethers.formatEther(balance)} ETH`);
        return balance;
    } catch (error) {
        console.error('Failed to check balance:', error.message);
    }
}

async function listAllBalances() {
    console.log('📊 Checking all pre-funded account balances:');
    console.log('');
    
    const provider = new ethers.JsonRpcProvider(RPC_URL);
    
    for (let i = 0; i < SENDER_PRIVATE_KEYS.length; i++) {
        try {
            const wallet = new ethers.Wallet(SENDER_PRIVATE_KEYS[i], provider);
            const balance = await provider.getBalance(wallet.address);
            console.log(`Account ${i + 1}: ${wallet.address}`);
            console.log(`Balance: ${ethers.formatEther(balance)} ETH`);
            console.log('');
        } catch (error) {
            console.log(`Account ${i + 1}: Error - ${error.message}`);
        }
    }
}

// Command line interface
const args = process.argv.slice(2);

if (args.includes('--check-balances')) {
    listAllBalances();
} else if (args.includes('--check-recipient')) {
    checkBalance(RECIPIENT_ADDRESS);
} else if (args.includes('--help')) {
    console.log('🛡️ Himaya Insurance - ETH Transfer Script');
    console.log('');
    console.log('Usage:');
    console.log('  node send-eth.js                 # Send 200 ETH to recipient');
    console.log('  node send-eth.js --check-balances # Check all sender balances');
    console.log('  node send-eth.js --check-recipient # Check recipient balance');
    console.log('  node send-eth.js --help          # Show this help');
    console.log('');
    console.log(`Recipient: ${RECIPIENT_ADDRESS}`);
    console.log(`Amount: ${AMOUNT_ETH} ETH`);
} else {
    // Default action: send ETH
    sendETH();
}
