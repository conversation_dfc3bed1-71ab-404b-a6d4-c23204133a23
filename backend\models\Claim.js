const mongoose = require('mongoose');

const claimSchema = new mongoose.Schema({
  blockchainId: {
    type: Number,
    required: true,
    unique: true
  },
  claimNumber: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  policyId: {
    type: Number,
    required: true
  },
  claimant: {
    type: String,
    required: true,
    lowercase: true
  },
  claimType: {
    type: String,
    enum: ['Collision', 'Theft', 'Vandalism', 'NaturalDisaster', 'Fire', 'Other'],
    required: true
  },
  description: {
    type: String,
    required: true
  },
  claimedAmount: {
    type: Number,
    required: true,
    min: 0
  },
  approvedAmount: {
    type: Number,
    default: 0,
    min: 0
  },
  status: {
    type: String,
    enum: ['Submitted', 'UnderReview', 'RequiresDocuments', 'Approved', 'Rejected', 'Paid'],
    default: 'Submitted'
  },
  incidentDate: {
    type: Date,
    required: true
  },
  submissionDate: {
    type: Date,
    required: true
  },
  location: {
    type: String,
    required: true
  },
  reviewer: {
    type: String,
    lowercase: true
  },
  reviewNotes: {
    type: String
  },
  reviewDate: {
    type: Date
  },
  // Additional off-chain data
  policeReportNumber: {
    type: String,
    trim: true
  },
  witnesses: [{
    name: String,
    phone: String,
    email: String,
    statement: String
  }],
  thirdPartyInfo: {
    name: String,
    phone: String,
    email: String,
    insuranceCompany: String,
    policyNumber: String,
    vehicleInfo: String
  },
  repairEstimates: [{
    shopName: String,
    contactInfo: String,
    estimatedAmount: Number,
    estimateDate: Date,
    documentUrl: String
  }],
  medicalInfo: {
    hospitalName: String,
    doctorName: String,
    treatmentDescription: String,
    medicalBills: Number,
    isInjured: {
      type: Boolean,
      default: false
    }
  },
  documents: [{
    type: String, // IPFS hash or file path
    description: String,
    category: {
      type: String,
      enum: ['photos', 'police_report', 'medical', 'repair_estimate', 'other']
    },
    uploadDate: {
      type: Date,
      default: Date.now
    }
  }],
  timeline: [{
    action: String,
    description: String,
    performedBy: String,
    timestamp: {
      type: Date,
      default: Date.now
    }
  }],
  paymentInfo: {
    method: {
      type: String,
      enum: ['bank_transfer', 'check', 'crypto']
    },
    transactionHash: String,
    paymentDate: Date,
    paymentReference: String
  },
  fraudScore: {
    type: Number,
    min: 0,
    max: 100,
    default: 0
  },
  priority: {
    type: String,
    enum: ['low', 'medium', 'high', 'urgent'],
    default: 'medium'
  },
  estimatedProcessingTime: {
    type: Number, // in days
    default: 14
  },
  notes: {
    type: String
  }
}, {
  timestamps: true
});

// Indexes
claimSchema.index({ blockchainId: 1 });
claimSchema.index({ claimNumber: 1 });
claimSchema.index({ claimant: 1 });
claimSchema.index({ policyId: 1 });
claimSchema.index({ status: 1 });
claimSchema.index({ reviewer: 1 });
claimSchema.index({ incidentDate: 1 });
claimSchema.index({ submissionDate: 1 });
claimSchema.index({ claimType: 1 });

// Virtual for processing time
claimSchema.virtual('processingDays').get(function() {
  if (!this.reviewDate) {
    const now = new Date();
    const diffTime = Math.abs(now - this.submissionDate);
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }
  const diffTime = Math.abs(this.reviewDate - this.submissionDate);
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
});

// Virtual for is overdue
claimSchema.virtual('isOverdue').get(function() {
  if (this.status === 'Approved' || this.status === 'Rejected' || this.status === 'Paid') {
    return false;
  }
  const now = new Date();
  const diffTime = Math.abs(now - this.submissionDate);
  const daysPassed = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return daysPassed > this.estimatedProcessingTime;
});

// Method to add timeline entry
claimSchema.methods.addTimelineEntry = function(action, description, performedBy) {
  this.timeline.push({
    action,
    description,
    performedBy,
    timestamp: new Date()
  });
};

// Method to calculate fraud score (simplified)
claimSchema.methods.calculateFraudScore = function() {
  let score = 0;
  
  // High claim amount relative to vehicle value
  if (this.claimedAmount > 50000) score += 20;
  
  // Recent policy (less than 30 days)
  // This would need policy start date from Policy model
  
  // Multiple claims in short period
  // This would need to check other claims from same claimant
  
  // Incident on weekend/holiday
  const incidentDay = this.incidentDate.getDay();
  if (incidentDay === 0 || incidentDay === 6) score += 10;
  
  // Late reporting (more than 7 days)
  const reportingDelay = Math.abs(this.submissionDate - this.incidentDate);
  const daysDelay = Math.ceil(reportingDelay / (1000 * 60 * 60 * 24));
  if (daysDelay > 7) score += 15;
  
  this.fraudScore = Math.min(score, 100);
  return this.fraudScore;
};

module.exports = mongoose.model('Claim', claimSchema);
