/* Professional Report Styling for Himaya Blockchain Insurance Platform */

/* Page Setup */
@page {
    size: A4;
    margin: 1in;
}

/* Typography */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: 11pt;
    line-height: 1.6;
    color: #2c3e50;
    max-width: 8.5in;
    margin: 0 auto;
    padding: 0;
    background: white;
}

/* Headings */
h1 {
    color: #2980b9;
    font-size: 24pt;
    font-weight: bold;
    margin-top: 30pt;
    margin-bottom: 20pt;
    border-bottom: 3px solid #2980b9;
    padding-bottom: 10pt;
    page-break-before: always;
}

h1:first-of-type {
    page-break-before: auto;
    text-align: center;
    border-bottom: none;
    margin-top: 0;
}

h2 {
    color: #34495e;
    font-size: 18pt;
    font-weight: bold;
    margin-top: 25pt;
    margin-bottom: 15pt;
    border-left: 4px solid #3498db;
    padding-left: 15pt;
}

h3 {
    color: #2c3e50;
    font-size: 14pt;
    font-weight: bold;
    margin-top: 20pt;
    margin-bottom: 10pt;
}

h4 {
    color: #34495e;
    font-size: 12pt;
    font-weight: bold;
    margin-top: 15pt;
    margin-bottom: 8pt;
}

/* Paragraphs */
p {
    margin-bottom: 12pt;
    text-align: justify;
    orphans: 2;
    widows: 2;
}

/* Lists */
ul, ol {
    margin-bottom: 12pt;
    padding-left: 20pt;
}

li {
    margin-bottom: 6pt;
}

/* Strong emphasis */
strong {
    color: #2c3e50;
    font-weight: bold;
}

/* Code blocks */
pre {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 15pt;
    margin: 15pt 0;
    font-family: 'Courier New', monospace;
    font-size: 9pt;
    overflow-x: auto;
    page-break-inside: avoid;
}

code {
    background-color: #f8f9fa;
    padding: 2pt 4pt;
    border-radius: 2px;
    font-family: 'Courier New', monospace;
    font-size: 9pt;
}

/* Tables */
table {
    width: 100%;
    border-collapse: collapse;
    margin: 15pt 0;
    page-break-inside: avoid;
}

th {
    background-color: #3498db;
    color: white;
    padding: 10pt;
    text-align: left;
    font-weight: bold;
    border: 1px solid #2980b9;
}

td {
    padding: 8pt 10pt;
    border: 1px solid #bdc3c7;
    vertical-align: top;
}

tr:nth-child(even) {
    background-color: #f8f9fa;
}

/* Blockquotes */
blockquote {
    border-left: 4px solid #3498db;
    margin: 15pt 0;
    padding: 10pt 20pt;
    background-color: #f8f9fa;
    font-style: italic;
}

/* Links */
a {
    color: #2980b9;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

/* Table of Contents */
#TOC {
    border: 2px solid #3498db;
    padding: 20pt;
    margin: 20pt 0;
    background-color: #f8f9fa;
    page-break-after: always;
}

#TOC h1 {
    color: #2980b9;
    margin-top: 0;
    border-bottom: 2px solid #2980b9;
    text-align: center;
}

#TOC ul {
    list-style-type: none;
    padding-left: 0;
}

#TOC li {
    margin-bottom: 8pt;
}

#TOC a {
    color: #2c3e50;
    font-weight: 500;
}

/* Header styling for title page */
.title-page {
    text-align: center;
    page-break-after: always;
}

.title {
    font-size: 28pt;
    font-weight: bold;
    color: #2980b9;
    margin-bottom: 10pt;
}

.subtitle {
    font-size: 18pt;
    color: #34495e;
    margin-bottom: 30pt;
}

.author {
    font-size: 14pt;
    margin-bottom: 10pt;
}

.institution {
    font-size: 12pt;
    color: #7f8c8d;
    margin-bottom: 20pt;
}

.date {
    font-size: 12pt;
    color: #7f8c8d;
}

/* Page breaks */
.page-break {
    page-break-before: always;
}

/* Figures and images */
figure {
    text-align: center;
    margin: 20pt 0;
    page-break-inside: avoid;
}

figcaption {
    font-style: italic;
    color: #7f8c8d;
    margin-top: 8pt;
}

/* Special boxes for important information */
.info-box {
    border: 2px solid #3498db;
    background-color: #ebf3fd;
    padding: 15pt;
    margin: 15pt 0;
    border-radius: 5px;
    page-break-inside: avoid;
}

.warning-box {
    border: 2px solid #e74c3c;
    background-color: #fdf2f2;
    padding: 15pt;
    margin: 15pt 0;
    border-radius: 5px;
    page-break-inside: avoid;
}

.success-box {
    border: 2px solid #27ae60;
    background-color: #f0f9f4;
    padding: 15pt;
    margin: 15pt 0;
    border-radius: 5px;
    page-break-inside: avoid;
}

/* Print optimizations */
@media print {
    body {
        font-size: 10pt;
        line-height: 1.4;
    }
    
    h1 {
        font-size: 20pt;
    }
    
    h2 {
        font-size: 16pt;
    }
    
    h3 {
        font-size: 12pt;
    }
    
    .no-print {
        display: none;
    }
    
    a {
        color: #000;
        text-decoration: none;
    }
    
    pre {
        font-size: 8pt;
    }
}

/* Responsive design for screen viewing */
@media screen and (max-width: 768px) {
    body {
        padding: 10pt;
        font-size: 12pt;
    }
    
    h1 {
        font-size: 20pt;
    }
    
    h2 {
        font-size: 16pt;
    }
    
    table {
        font-size: 9pt;
    }
}
