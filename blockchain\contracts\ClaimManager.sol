// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/Counters.sol";
import "./InsurancePolicy.sol";
import "./VehicleRegistry.sol";

contract ClaimManager is Ownable {
    using Counters for Counters.Counter;
    
    Counters.Counter private _claimIds;
    InsurancePolicy public insurancePolicy;
    VehicleRegistry public vehicleRegistry;
    
    enum ClaimStatus { 
        Submitted, 
        UnderReview, 
        RequiresDocuments, 
        Approved, 
        Rejected, 
        Paid 
    }
    
    enum ClaimType { 
        Collision, 
        Theft, 
        Vandalism, 
        NaturalDisaster, 
        Fire, 
        Other 
    }
    
    struct Claim {
        uint256 id;
        uint256 policyId;
        address claimant;
        ClaimType claimType;
        string description;
        uint256 claimedAmount;
        uint256 approvedAmount;
        ClaimStatus status;
        uint256 incidentDate;
        uint256 submissionDate;
        string location;
        string[] documentHashes; // IPFS hashes for documents
        address reviewer;
        string reviewNotes;
        uint256 reviewDate;
    }
    
    mapping(uint256 => Claim) public claims;
    mapping(address => uint256[]) public claimantClaims;
    mapping(uint256 => uint256[]) public policyClaims; // policyId => claimIds
    mapping(address => uint256[]) public reviewerClaims;
    
    // Authorized reviewers (insurance agents)
    mapping(address => bool) public authorizedReviewers;
    
    event ClaimSubmitted(
        uint256 indexed claimId,
        uint256 indexed policyId,
        address indexed claimant,
        uint256 claimedAmount
    );
    
    event ClaimStatusChanged(
        uint256 indexed claimId,
        ClaimStatus previousStatus,
        ClaimStatus newStatus,
        address reviewer
    );
    
    event ClaimApproved(
        uint256 indexed claimId,
        uint256 approvedAmount,
        address reviewer
    );
    
    event ClaimRejected(
        uint256 indexed claimId,
        string reason,
        address reviewer
    );
    
    event DocumentAdded(
        uint256 indexed claimId,
        string documentHash
    );
    
    event ReviewerAuthorized(address indexed reviewer);
    event ReviewerRevoked(address indexed reviewer);
    
    modifier onlyAuthorizedReviewer() {
        require(authorizedReviewers[msg.sender], "Not authorized reviewer");
        _;
    }
    
    modifier onlyClaimant(uint256 _claimId) {
        require(claims[_claimId].claimant == msg.sender, "Not claimant");
        _;
    }
    
    modifier claimExists(uint256 _claimId) {
        require(claims[_claimId].id != 0, "Claim does not exist");
        _;
    }
    
    constructor(
        address _insurancePolicyAddress,
        address _vehicleRegistryAddress
    ) Ownable(msg.sender) {
        insurancePolicy = InsurancePolicy(_insurancePolicyAddress);
        vehicleRegistry = VehicleRegistry(_vehicleRegistryAddress);
    }
    
    function authorizeReviewer(address _reviewer) external onlyOwner {
        require(_reviewer != address(0), "Invalid reviewer address");
        authorizedReviewers[_reviewer] = true;
        emit ReviewerAuthorized(_reviewer);
    }
    
    function revokeReviewer(address _reviewer) external onlyOwner {
        authorizedReviewers[_reviewer] = false;
        emit ReviewerRevoked(_reviewer);
    }
    
    function submitClaim(
        uint256 _policyId,
        ClaimType _claimType,
        string memory _description,
        uint256 _claimedAmount,
        uint256 _incidentDate,
        string memory _location,
        string[] memory _documentHashes
    ) external returns (uint256) {
        require(_claimedAmount > 0, "Claimed amount must be greater than 0");
        require(_incidentDate <= block.timestamp, "Incident date cannot be in future");
        require(bytes(_description).length > 0, "Description cannot be empty");
        require(bytes(_location).length > 0, "Location cannot be empty");
        
        // Verify policy exists and is active
        require(insurancePolicy.isPolicyActive(_policyId), "Policy is not active");
        
        // Verify claimant is the policyholder
        InsurancePolicy.Policy memory policy = insurancePolicy.getPolicy(_policyId);
        require(policy.policyholder == msg.sender, "Only policyholder can submit claims");
        
        // Verify claimed amount doesn't exceed coverage
        require(_claimedAmount <= policy.coverageAmount, "Claimed amount exceeds coverage");
        
        _claimIds.increment();
        uint256 newClaimId = _claimIds.current();
        
        claims[newClaimId] = Claim({
            id: newClaimId,
            policyId: _policyId,
            claimant: msg.sender,
            claimType: _claimType,
            description: _description,
            claimedAmount: _claimedAmount,
            approvedAmount: 0,
            status: ClaimStatus.Submitted,
            incidentDate: _incidentDate,
            submissionDate: block.timestamp,
            location: _location,
            documentHashes: _documentHashes,
            reviewer: address(0),
            reviewNotes: "",
            reviewDate: 0
        });
        
        claimantClaims[msg.sender].push(newClaimId);
        policyClaims[_policyId].push(newClaimId);
        
        emit ClaimSubmitted(newClaimId, _policyId, msg.sender, _claimedAmount);
        
        return newClaimId;
    }
    
    function updateClaimStatus(
        uint256 _claimId,
        ClaimStatus _newStatus,
        string memory _reviewNotes
    ) external onlyAuthorizedReviewer claimExists(_claimId) {
        Claim storage claim = claims[_claimId];
        ClaimStatus previousStatus = claim.status;
        
        claim.status = _newStatus;
        claim.reviewer = msg.sender;
        claim.reviewNotes = _reviewNotes;
        claim.reviewDate = block.timestamp;
        
        // Add to reviewer's claims if not already there
        if (reviewerClaims[msg.sender].length == 0 || 
            !_isClaimInReviewerList(msg.sender, _claimId)) {
            reviewerClaims[msg.sender].push(_claimId);
        }
        
        emit ClaimStatusChanged(_claimId, previousStatus, _newStatus, msg.sender);
    }
    
    function approveClaim(
        uint256 _claimId,
        uint256 _approvedAmount,
        string memory _reviewNotes
    ) external onlyAuthorizedReviewer claimExists(_claimId) {
        Claim storage claim = claims[_claimId];
        require(claim.status == ClaimStatus.UnderReview, "Claim not under review");
        require(_approvedAmount <= claim.claimedAmount, "Approved amount exceeds claimed amount");
        
        InsurancePolicy.Policy memory policy = insurancePolicy.getPolicy(claim.policyId);
        require(_approvedAmount >= policy.deductible, "Approved amount below deductible");
        
        claim.approvedAmount = _approvedAmount;
        claim.status = ClaimStatus.Approved;
        claim.reviewer = msg.sender;
        claim.reviewNotes = _reviewNotes;
        claim.reviewDate = block.timestamp;
        
        if (!_isClaimInReviewerList(msg.sender, _claimId)) {
            reviewerClaims[msg.sender].push(_claimId);
        }
        
        emit ClaimApproved(_claimId, _approvedAmount, msg.sender);
    }
    
    function rejectClaim(
        uint256 _claimId,
        string memory _reason
    ) external onlyAuthorizedReviewer claimExists(_claimId) {
        Claim storage claim = claims[_claimId];
        require(claim.status == ClaimStatus.UnderReview, "Claim not under review");
        
        claim.status = ClaimStatus.Rejected;
        claim.reviewer = msg.sender;
        claim.reviewNotes = _reason;
        claim.reviewDate = block.timestamp;
        
        if (!_isClaimInReviewerList(msg.sender, _claimId)) {
            reviewerClaims[msg.sender].push(_claimId);
        }
        
        emit ClaimRejected(_claimId, _reason, msg.sender);
    }
    
    function addDocument(
        uint256 _claimId,
        string memory _documentHash
    ) external claimExists(_claimId) {
        Claim storage claim = claims[_claimId];
        require(
            msg.sender == claim.claimant || authorizedReviewers[msg.sender],
            "Only claimant or reviewer can add documents"
        );
        
        claim.documentHashes.push(_documentHash);
        emit DocumentAdded(_claimId, _documentHash);
    }
    
    function getClaim(uint256 _claimId) 
        external 
        view 
        claimExists(_claimId) 
        returns (Claim memory) 
    {
        return claims[_claimId];
    }
    
    function getClaimantClaims(address _claimant) 
        external 
        view 
        returns (uint256[] memory) 
    {
        return claimantClaims[_claimant];
    }
    
    function getPolicyClaims(uint256 _policyId) 
        external 
        view 
        returns (uint256[] memory) 
    {
        return policyClaims[_policyId];
    }
    
    function getReviewerClaims(address _reviewer) 
        external 
        view 
        returns (uint256[] memory) 
    {
        return reviewerClaims[_reviewer];
    }
    
    function getTotalClaims() external view returns (uint256) {
        return _claimIds.current();
    }
    
    function _isClaimInReviewerList(address _reviewer, uint256 _claimId) 
        private 
        view 
        returns (bool) 
    {
        uint256[] memory claims_list = reviewerClaims[_reviewer];
        for (uint256 i = 0; i < claims_list.length; i++) {
            if (claims_list[i] == _claimId) {
                return true;
            }
        }
        return false;
    }
}
