// Web3 Integration for Himaya Vehicle Insurance DApp
class HimayaBlockchain {
    constructor() {
        this.web3 = null;
        this.account = null;
        this.contracts = {};
        this.isConnected = false;

        // Contract addresses from deployments.json
        this.contractAddresses = {
            vehicleRegistry: "******************************************",
            insurancePolicy: "******************************************",
            claimManager: "******************************************"
        };

        this.init();
    }

    async init() {
        if (typeof window.ethereum !== 'undefined') {
            try {
                this.web3 = new Web3(window.ethereum);
                // Load contracts if previously connected
                if (window.ethereum.selectedAddress) {
                    this.account = window.ethereum.selectedAddress;
                    this.isConnected = true;
                    await this.loadContracts();
                }
            } catch (error) {
                console.error('Failed to initialize Web3:', error);
            }
        }
    }

    async connectWallet() {
        try {
            const accounts = await window.ethereum.request({ 
                method: 'eth_requestAccounts' 
            });
            
            this.account = accounts[0];
            this.isConnected = true;
            
            // Setup network event listeners
            window.ethereum.on('accountsChanged', (accounts) => {
                this.account = accounts[0];
                window.location.reload();
            });

            window.ethereum.on('chainChanged', () => {
                window.location.reload();
            });

            await this.loadContracts();
            return this.account;
        } catch (error) {
            console.error('Failed to connect wallet:', error);
            throw error;
        }
    }

    async loadContracts() {
        try {
            // Load contract ABIs from the compiled artifacts
            const [vehicleRegistryABI, insurancePolicyABI, claimManagerABI] = await Promise.all([
                fetch('/abi/VehicleRegistry.json').then(r => r.json()),
                fetch('/abi/InsurancePolicy.json').then(r => r.json()),
                fetch('/abi/ClaimManager.json').then(r => r.json())
            ]);

            // Initialize contract instances
            this.contracts.vehicleRegistry = new this.web3.eth.Contract(
                vehicleRegistryABI,
                this.contractAddresses.vehicleRegistry
            );

            this.contracts.insurancePolicy = new this.web3.eth.Contract(
                insurancePolicyABI,
                this.contractAddresses.insurancePolicy
            );

            this.contracts.claimManager = new this.web3.eth.Contract(
                claimManagerABI,
                this.contractAddresses.claimManager
            );

            console.log('Smart contracts loaded successfully');
            return true;
        } catch (error) {
            console.error('Failed to load contracts:', error);
            return false;
        }
    }

    async getBalance() {
        if (!this.isConnected) {
            throw new Error('Wallet not connected');
        }
        
        const balance = await this.web3.eth.getBalance(this.account);
        return this.web3.utils.fromWei(balance, 'ether');
    }

    async registerVehicle(vehicleData) {
        if (!this.isConnected) {
            throw new Error('Wallet not connected');
        }

        try {
            if (!this.contracts.vehicleRegistry) {
                throw new Error('Vehicle registry contract not loaded');
            }

            // Estimate gas for the transaction
            const gasEstimate = await this.contracts.vehicleRegistry.methods
                .registerVehicle(
                    vehicleData.licensePlate,
                    vehicleData.make,
                    vehicleData.model,
                    vehicleData.year,
                    vehicleData.city
                )
                .estimateGas({ from: this.account });

            // Send transaction with 20% gas buffer
            const tx = await this.contracts.vehicleRegistry.methods
                .registerVehicle(
                    vehicleData.licensePlate,
                    vehicleData.make,
                    vehicleData.model,
                    vehicleData.year,
                    vehicleData.city
                )
                .send({
                    from: this.account,
                    gas: Math.floor(gasEstimate * 1.2)
                });

            return {
                success: true,
                txHash: tx.transactionHash,
                vehicleId: tx.events.VehicleRegistered.returnValues.vehicleId
            };
        } catch (error) {
            console.error('Vehicle registration failed:', error);
            throw error;
        }
    }

    async purchaseInsurance(policyData) {
        if (!this.isConnected) {
            throw new Error('Wallet not connected');
        }

        try {
            if (!this.contracts.insurancePolicy) {
                throw new Error('Insurance policy contract not loaded');
            }

            // Convert coverage amounts to Wei
            const premiumWei = this.web3.utils.toWei(policyData.premium.toString(), 'ether');
            const coverageWei = this.web3.utils.toWei(policyData.coverage.toString(), 'ether');
            const deductibleWei = this.web3.utils.toWei(policyData.deductible.toString(), 'ether');

            // Estimate gas
            const gasEstimate = await this.contracts.insurancePolicy.methods
                .createPolicy(
                    policyData.vehicleId,
                    policyData.coverageType,
                    coverageWei,
                    deductibleWei,
                    policyData.duration
                )
                .estimateGas({
                    from: this.account,
                    value: premiumWei
                });

            // Send transaction with premium payment
            const tx = await this.contracts.insurancePolicy.methods
                .createPolicy(
                    policyData.vehicleId,
                    policyData.coverageType,
                    coverageWei,
                    deductibleWei,
                    policyData.duration
                )
                .send({
                    from: this.account,
                    value: premiumWei,
                    gas: Math.floor(gasEstimate * 1.2)
                });

            return {
                success: true,
                policyId: tx.events.PolicyCreated.returnValues.policyId,
                txHash: tx.transactionHash
            };
        } catch (error) {
            console.error('Policy creation failed:', error);
            throw error;
        }
    }

    async submitClaim(claimData) {
        if (!this.isConnected) {
            throw new Error('Wallet not connected');
        }

        try {
            if (!this.contracts.claimManager) {
                throw new Error('Claim manager contract not loaded');
            }

            const claimedAmountWei = this.web3.utils.toWei(claimData.claimedAmount.toString(), 'ether');

            // Estimate gas
            const gasEstimate = await this.contracts.claimManager.methods
                .submitClaim(
                    claimData.policyId,
                    claimData.claimType,
                    claimData.description,
                    claimedAmountWei,
                    claimData.location,
                    claimData.evidenceHashes || []
                )
                .estimateGas({ from: this.account });

            // Submit claim
            const tx = await this.contracts.claimManager.methods
                .submitClaim(
                    claimData.policyId,
                    claimData.claimType,
                    claimData.description,
                    claimedAmountWei,
                    claimData.location,
                    claimData.evidenceHashes || []
                )
                .send({
                    from: this.account,
                    gas: Math.floor(gasEstimate * 1.2)
                });

            return {
                success: true,
                claimId: tx.events.ClaimSubmitted.returnValues.claimId,
                txHash: tx.transactionHash
            };
        } catch (error) {
            console.error('Claim submission failed:', error);
            throw error;
        }
    }

    async getVehicles() {
        if (!this.isConnected || !this.contracts.vehicleRegistry) {
            throw new Error('Not connected to blockchain');
        }

        try {
            // Get total vehicles count
            const vehicleCount = await this.contracts.vehicleRegistry.methods.getVehicleCount().call();
            
            // Fetch all vehicles in parallel
            const vehicles = await Promise.all(
                Array.from({ length: vehicleCount }, (_, i) => 
                    this.contracts.vehicleRegistry.methods.getVehicle(i + 1).call()
                )
            );

            return vehicles.map(v => ({
                id: v.id,
                licensePlate: v.licensePlate,
                make: v.make,
                model: v.model,
                year: v.year,
                city: v.city,
                owner: v.owner,
                isActive: v.isActive,
                registrationDate: new Date(v.registrationDate * 1000).toISOString()
            }));
        } catch (error) {
            console.error('Failed to fetch vehicles:', error);
            throw error;
        }
    }

    async getPolicies() {
        if (!this.isConnected || !this.contracts.insurancePolicy) {
            throw new Error('Not connected to blockchain');
        }

        try {
            // Get user's policies
            const policyCount = await this.contracts.insurancePolicy.methods.getUserPolicyCount(this.account).call();
            
            const policies = await Promise.all(
                Array.from({ length: policyCount }, (_, i) => 
                    this.contracts.insurancePolicy.methods.getUserPolicyByIndex(this.account, i).call()
                )
            );

            return policies.map(p => ({
                id: p.id,
                vehicleId: p.vehicleId,
                owner: p.owner,
                coverageType: p.coverageType,
                premium: this.web3.utils.fromWei(p.premium, 'ether'),
                coverage: this.web3.utils.fromWei(p.coverage, 'ether'),
                deductible: this.web3.utils.fromWei(p.deductible, 'ether'),
                startDate: new Date(p.startDate * 1000).toISOString(),
                endDate: new Date(p.endDate * 1000).toISOString(),
                isActive: p.isActive
            }));
        } catch (error) {
            console.error('Failed to fetch policies:', error);
            throw error;
        }
    }

    async getClaims() {
        if (!this.isConnected || !this.contracts.claimManager) {
            throw new Error('Not connected to blockchain');
        }

        try {
            // Get user's claims
            const claimCount = await this.contracts.claimManager.methods.getUserClaimCount(this.account).call();
            
            const claims = await Promise.all(
                Array.from({ length: claimCount }, (_, i) => 
                    this.contracts.claimManager.methods.getUserClaimByIndex(this.account, i).call()
                )
            );

            return claims.map(c => ({
                id: c.id,
                policyId: c.policyId,
                claimant: c.claimant,
                claimType: c.claimType,
                description: c.description,
                claimedAmount: this.web3.utils.fromWei(c.claimedAmount, 'ether'),
                approvedAmount: this.web3.utils.fromWei(c.approvedAmount, 'ether'),
                location: c.location,
                status: this.getClaimStatus(c.status),
                submissionDate: new Date(c.submissionDate * 1000).toISOString(),
                evidenceHashes: c.evidenceHashes
            }));
        } catch (error) {
            console.error('Failed to fetch claims:', error);
            throw error;
        }
    }

    getClaimStatus(statusCode) {
        const statuses = ['Pending', 'Approved', 'Rejected', 'Paid'];
        return statuses[statusCode] || 'Unknown';
    }
}

// Global instance
window.himayaBlockchain = new HimayaBlockchain();
