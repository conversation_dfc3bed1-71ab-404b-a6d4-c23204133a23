[{"inputs": [{"internalType": "address", "name": "_vehicleRegistryAddress", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "OwnableInvalidOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "OwnableUnauthorizedAccount", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "insurer", "type": "address"}], "name": "InsurerAuthorized", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "insurer", "type": "address"}], "name": "InsurerRevoked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "policyId", "type": "uint256"}, {"indexed": true, "internalType": "uint256", "name": "vehicleId", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "policyholder", "type": "address"}, {"indexed": false, "internalType": "address", "name": "insurer", "type": "address"}, {"indexed": false, "internalType": "string", "name": "policyNumber", "type": "string"}], "name": "PolicyCreated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "policyId", "type": "uint256"}, {"indexed": false, "internalType": "enum InsurancePolicy.PolicyStatus", "name": "previousStatus", "type": "uint8"}, {"indexed": false, "internalType": "enum InsurancePolicy.PolicyStatus", "name": "newStatus", "type": "uint8"}], "name": "PolicyStatusChanged", "type": "event"}, {"inputs": [{"internalType": "address", "name": "_insurer", "type": "address"}], "name": "authorizeInsurer", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "authorizedInsurers", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_vehicleId", "type": "uint256"}, {"internalType": "address", "name": "_policyholder", "type": "address"}, {"internalType": "enum InsurancePolicy.CoverageType", "name": "_coverageType", "type": "uint8"}, {"internalType": "uint256", "name": "_premiumAmount", "type": "uint256"}, {"internalType": "uint256", "name": "_coverageAmount", "type": "uint256"}, {"internalType": "uint256", "name": "_deductible", "type": "uint256"}, {"internalType": "uint256", "name": "_durationInDays", "type": "uint256"}, {"internalType": "string", "name": "_policyNumber", "type": "string"}], "name": "createPolicy", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_insurer", "type": "address"}], "name": "getInsurerPolicies", "outputs": [{"internalType": "uint256[]", "name": "", "type": "uint256[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_policyId", "type": "uint256"}], "name": "getPolicy", "outputs": [{"components": [{"internalType": "uint256", "name": "id", "type": "uint256"}, {"internalType": "uint256", "name": "vehicleId", "type": "uint256"}, {"internalType": "address", "name": "policyholder", "type": "address"}, {"internalType": "address", "name": "insurer", "type": "address"}, {"internalType": "enum InsurancePolicy.CoverageType", "name": "coverageType", "type": "uint8"}, {"internalType": "uint256", "name": "premiumAmount", "type": "uint256"}, {"internalType": "uint256", "name": "coverageAmount", "type": "uint256"}, {"internalType": "uint256", "name": "deductible", "type": "uint256"}, {"internalType": "uint256", "name": "startDate", "type": "uint256"}, {"internalType": "uint256", "name": "endDate", "type": "uint256"}, {"internalType": "enum InsurancePolicy.PolicyStatus", "name": "status", "type": "uint8"}, {"internalType": "string", "name": "policyNumber", "type": "string"}], "internalType": "struct InsurancePolicy.Policy", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "_policyNumber", "type": "string"}], "name": "getPolicyByNumber", "outputs": [{"components": [{"internalType": "uint256", "name": "id", "type": "uint256"}, {"internalType": "uint256", "name": "vehicleId", "type": "uint256"}, {"internalType": "address", "name": "policyholder", "type": "address"}, {"internalType": "address", "name": "insurer", "type": "address"}, {"internalType": "enum InsurancePolicy.CoverageType", "name": "coverageType", "type": "uint8"}, {"internalType": "uint256", "name": "premiumAmount", "type": "uint256"}, {"internalType": "uint256", "name": "coverageAmount", "type": "uint256"}, {"internalType": "uint256", "name": "deductible", "type": "uint256"}, {"internalType": "uint256", "name": "startDate", "type": "uint256"}, {"internalType": "uint256", "name": "endDate", "type": "uint256"}, {"internalType": "enum InsurancePolicy.PolicyStatus", "name": "status", "type": "uint8"}, {"internalType": "string", "name": "policyNumber", "type": "string"}], "internalType": "struct InsurancePolicy.Policy", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_policyholder", "type": "address"}], "name": "getPolicyholderPolicies", "outputs": [{"internalType": "uint256[]", "name": "", "type": "uint256[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getTotalPolicies", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_vehicleId", "type": "uint256"}], "name": "getVehiclePolicies", "outputs": [{"internalType": "uint256[]", "name": "", "type": "uint256[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "uint256", "name": "", "type": "uint256"}], "name": "insurerPolicies", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_policyId", "type": "uint256"}], "name": "isPolicyActive", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "policies", "outputs": [{"internalType": "uint256", "name": "id", "type": "uint256"}, {"internalType": "uint256", "name": "vehicleId", "type": "uint256"}, {"internalType": "address", "name": "policyholder", "type": "address"}, {"internalType": "address", "name": "insurer", "type": "address"}, {"internalType": "enum InsurancePolicy.CoverageType", "name": "coverageType", "type": "uint8"}, {"internalType": "uint256", "name": "premiumAmount", "type": "uint256"}, {"internalType": "uint256", "name": "coverageAmount", "type": "uint256"}, {"internalType": "uint256", "name": "deductible", "type": "uint256"}, {"internalType": "uint256", "name": "startDate", "type": "uint256"}, {"internalType": "uint256", "name": "endDate", "type": "uint256"}, {"internalType": "enum InsurancePolicy.PolicyStatus", "name": "status", "type": "uint8"}, {"internalType": "string", "name": "policyNumber", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "", "type": "string"}], "name": "policyNumberToId", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "uint256", "name": "", "type": "uint256"}], "name": "policyholderPolicies", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_insurer", "type": "address"}], "name": "revokeInsurer", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_policyId", "type": "uint256"}, {"internalType": "enum InsurancePolicy.PolicyStatus", "name": "_newStatus", "type": "uint8"}], "name": "updatePolicyStatus", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}], "name": "vehiclePolicies", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "vehicleRegistry", "outputs": [{"internalType": "contract VehicleRegistry", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}]