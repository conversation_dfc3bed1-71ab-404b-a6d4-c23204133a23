<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🛡️ Himaya Blockchain - Fresh Modern Interface</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🛡️</text></svg>">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 25%, #8b5cf6 50%, #ec4899 75%, #6d28d9 100%);
            color: white;
            min-height: 100vh;
            overflow-x: hidden;
            position: relative;
        }

        /* Animated Background */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                radial-gradient(circle at 25% 25%, rgba(139, 92, 246, 0.5) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(236, 72, 153, 0.5) 0%, transparent 50%);
            z-index: -1;
            animation: backgroundMove 10s ease-in-out infinite alternate;
        }

        @keyframes backgroundMove {
            0% { transform: scale(1) rotate(0deg); opacity: 0.7; }
            100% { transform: scale(1.1) rotate(5deg); opacity: 0.9; }
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
            position: relative;
            z-index: 1;
        }

        /* Header */
        .header {
            text-align: center;
            padding: 4rem 0;
            margin-bottom: 3rem;
        }

        .logo {
            font-size: 8rem;
            margin-bottom: 1rem;
            animation: logoFloat 3s ease-in-out infinite;
            text-shadow: 0 0 50px rgba(139, 92, 246, 0.8);
        }

        @keyframes logoFloat {
            0%, 100% { transform: translateY(0px) scale(1); }
            50% { transform: translateY(-20px) scale(1.05); }
        }

        .title {
            font-size: 5rem;
            font-weight: 900;
            background: linear-gradient(45deg, #8b5cf6, #ec4899, #6d28d9);
            background-size: 300% 300%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1rem;
            animation: gradientAnimation 4s ease-in-out infinite;
        }

        @keyframes gradientAnimation {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .subtitle {
            font-size: 2rem;
            color: #e5e7eb;
            margin-bottom: 1rem;
            font-weight: 600;
        }

        .tagline {
            font-size: 1.3rem;
            color: #9ca3af;
            margin-bottom: 3rem;
        }

        /* Language Selector */
        .lang-selector {
            position: absolute;
            top: 2rem;
            right: 2rem;
            z-index: 100;
        }

        .lang-select {
            background: rgba(255, 255, 255, 0.15);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 15px;
            padding: 1rem 1.5rem;
            color: white;
            font-size: 1rem;
            font-weight: 600;
            backdrop-filter: blur(15px);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .lang-select:hover {
            background: rgba(255, 255, 255, 0.25);
            border-color: #8b5cf6;
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(139, 92, 246, 0.4);
        }

        /* Navigation */
        .nav {
            display: flex;
            justify-content: center;
            gap: 1.5rem;
            margin-bottom: 4rem;
            flex-wrap: wrap;
        }

        .nav-btn {
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 20px;
            padding: 1.2rem 2.5rem;
            color: white;
            text-decoration: none;
            font-weight: 700;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            backdrop-filter: blur(15px);
            position: relative;
            overflow: hidden;
            cursor: pointer;
        }

        .nav-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, #8b5cf6, #ec4899);
            transition: left 0.3s ease;
            z-index: -1;
        }

        .nav-btn:hover::before,
        .nav-btn.active::before {
            left: 0;
        }

        .nav-btn:hover,
        .nav-btn.active {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(139, 92, 246, 0.4);
            border-color: #8b5cf6;
        }

        /* Status Bar */
        .status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: rgba(0, 0, 0, 0.5);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            padding: 2rem 3rem;
            margin-bottom: 4rem;
            flex-wrap: wrap;
            gap: 2rem;
            backdrop-filter: blur(20px);
        }

        .status-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            font-size: 1rem;
            font-weight: 600;
        }

        .status-dot {
            width: 15px;
            height: 15px;
            border-radius: 50%;
            background: #ef4444;
            animation: statusPulse 2s infinite;
        }

        .status-dot.connected {
            background: #10b981;
        }

        @keyframes statusPulse {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.6; transform: scale(1.2); }
        }

        /* Cards */
        .card {
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 30px;
            padding: 3rem;
            margin-bottom: 3rem;
            backdrop-filter: blur(25px);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, #8b5cf6, #ec4899, #6d28d9);
        }

        .card:hover {
            transform: translateY(-10px);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
            border-color: #8b5cf6;
        }

        .card-title {
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 2rem;
            display: flex;
            align-items: center;
            gap: 1.5rem;
            color: white;
        }

        .card-description {
            color: #d1d5db;
            margin-bottom: 3rem;
            font-size: 1.2rem;
            line-height: 1.8;
        }

        /* Buttons */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.8rem;
            padding: 1.2rem 2.5rem;
            border: none;
            border-radius: 20px;
            font-weight: 700;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            position: relative;
            overflow: hidden;
            min-width: 180px;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.6s ease;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: linear-gradient(45deg, #8b5cf6, #6d28d9);
            color: white;
            box-shadow: 0 10px 25px rgba(139, 92, 246, 0.4);
        }

        .btn-primary:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(139, 92, 246, 0.6);
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.15);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.25);
            border-color: #8b5cf6;
            transform: translateY(-5px);
        }

        .btn-success {
            background: linear-gradient(45deg, #10b981, #059669);
            color: white;
            box-shadow: 0 10px 25px rgba(16, 185, 129, 0.4);
        }

        .btn-success:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(16, 185, 129, 0.6);
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }

        /* Button Groups */
        .btn-group {
            display: flex;
            gap: 1.5rem;
            flex-wrap: wrap;
            margin-bottom: 3rem;
        }

        /* Grid */
        .grid {
            display: grid;
            gap: 2.5rem;
        }

        .grid-2 {
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        }

        .grid-3 {
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        }

        /* Tab Content */
        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
            animation: fadeInUp 0.6s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(40px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Forms */
        .form-group {
            margin-bottom: 2rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.8rem;
            font-weight: 700;
            color: #e5e7eb;
            font-size: 1rem;
            text-transform: uppercase;
            letter-spacing: 0.1em;
        }

        .form-input {
            width: 100%;
            padding: 1.2rem;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 15px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            backdrop-filter: blur(15px);
        }

        .form-input:focus {
            outline: none;
            border-color: #8b5cf6;
            box-shadow: 0 0 0 4px rgba(139, 92, 246, 0.3);
            background: rgba(255, 255, 255, 0.2);
        }

        .form-input::placeholder {
            color: #9ca3af;
        }

        .form-select {
            appearance: none;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23ffffff' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 1.2rem center;
            background-repeat: no-repeat;
            background-size: 1.8em 1.8em;
            padding-right: 4rem;
        }

        /* Loading Animation */
        .loading {
            display: inline-block;
            width: 25px;
            height: 25px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top-color: #8b5cf6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Notification */
        .notification {
            position: fixed;
            top: 2rem;
            right: 2rem;
            padding: 1.5rem 2rem;
            border-radius: 15px;
            color: white;
            font-weight: 700;
            font-size: 1.1rem;
            z-index: 1000;
            animation: slideInRight 0.4s ease-out;
            backdrop-filter: blur(15px);
            border: 2px solid rgba(255, 255, 255, 0.3);
            max-width: 400px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .notification.success { background: rgba(16, 185, 129, 0.9); }
        .notification.error { background: rgba(239, 68, 68, 0.9); }
        .notification.info { background: rgba(59, 130, 246, 0.9); }
        .notification.warning { background: rgba(245, 158, 11, 0.9); }

        @keyframes slideInRight {
            from { 
                transform: translateX(100%); 
                opacity: 0; 
            }
            to { 
                transform: translateX(0); 
                opacity: 1; 
            }
        }

        /* Hidden */
        .hidden { display: none !important; }

        /* Responsive */
        @media (max-width: 768px) {
            .container { padding: 1rem; }
            .title { font-size: 3rem; }
            .subtitle { font-size: 1.5rem; }
            .card { padding: 2rem; }
            .btn-group { flex-direction: column; }
            .btn { width: 100%; }
            .status-bar { flex-direction: column; align-items: flex-start; }
            .nav { flex-direction: column; align-items: center; }
            .nav-btn { width: 100%; text-align: center; }
            .lang-selector { position: static; margin-bottom: 2rem; }
        }

        /* Utility Classes */
        .text-center { text-align: center; }
        .font-bold { font-weight: 700; }
        .text-lg { font-size: 1.125rem; }
        .text-xl { font-size: 1.25rem; }
        .text-2xl { font-size: 1.5rem; }
        .mb-4 { margin-bottom: 1rem; }
        .mb-6 { margin-bottom: 1.5rem; }
        .mt-4 { margin-top: 1rem; }
    </style>
</head>
<body>
    <!-- Language Selector -->
    <div class="lang-selector">
        <select id="languageSelect" class="lang-select">
            <option value="fr">🇫🇷 Français</option>
            <option value="en">🇺🇸 English</option>
            <option value="ar">🇲🇦 العربية</option>
        </select>
    </div>

    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="logo">🛡️</div>
            <h1 class="title">Himaya Blockchain</h1>
            <p class="subtitle">Plateforme d'Assurance Véhicules Nouvelle Génération</p>
            <p class="tagline">🇲🇦 Protection Intelligente pour le Maroc • حماية ذكية للمغرب</p>
        </header>

        <!-- Navigation -->
        <nav class="nav">
            <button class="nav-btn active" data-tab="connection">🔗 Connexion Wallet</button>
            <button class="nav-btn" data-tab="insurance">🚗 Assurance Auto</button>
            <button class="nav-btn" data-tab="dashboard">📊 Mon Dashboard</button>
            <button class="nav-btn" data-tab="converter">💱 Convertisseur ETH/MAD</button>
        </nav>

        <!-- Status Bar -->
        <div class="status-bar">
            <div class="status-item">
                <div class="status-dot" id="walletStatus"></div>
                <span id="walletText">Wallet: Non connecté</span>
            </div>
            <div class="status-item">
                <div class="status-dot" id="networkStatus"></div>
                <span id="networkText">Réseau: Déconnecté</span>
            </div>
            <div class="status-item">
                <div class="status-dot" id="contractStatus"></div>
                <span id="contractText">Smart Contracts: Inactifs</span>
            </div>
            <div class="status-item">
                <span id="blockInfo">Bloc: 0 | Gas: 0 gwei</span>
            </div>
        </div>

        <!-- Main Content -->
        <main id="mainContent">
            <!-- Connection Tab -->
            <section id="connection" class="tab-content active">
                <div class="card">
                    <h2 class="card-title">🔗 Connexion Blockchain Himaya</h2>
                    <p class="card-description">
                        Connectez votre portefeuille MetaMask pour accéder aux services d'assurance blockchain révolutionnaires.
                        Sécurité maximale garantie pour toutes vos transactions.
                    </p>
                    <div class="btn-group">
                        <button class="btn btn-primary" id="connectWalletBtn">
                            <span class="loading hidden" id="connectLoading"></span>
                            🦊 Connecter MetaMask
                        </button>
                        <button class="btn btn-secondary" id="addNetworkBtn">⚙️ Ajouter Réseau Himaya</button>
                        <button class="btn btn-secondary" id="refreshInfoBtn">🔄 Actualiser Infos</button>
                    </div>
                    <div class="grid grid-2">
                        <div class="card">
                            <h3 class="text-xl font-bold mb-4">👤 Informations Compte</h3>
                            <div class="form-group">
                                <label class="form-label">Adresse Wallet</label>
                                <div id="accountAddress" class="form-input" style="background: rgba(0,0,0,0.4);">Non connecté</div>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Solde ETH</label>
                                <div id="accountBalance" class="form-input" style="background: rgba(0,0,0,0.4);">0.0000 ETH</div>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Réseau Actuel</label>
                                <div id="networkInfo" class="form-input" style="background: rgba(0,0,0,0.4);">Non connecté</div>
                            </div>
                        </div>
                        <div class="card">
                            <h3 class="text-xl font-bold mb-4">⛓️ État Blockchain</h3>
                            <div class="form-group">
                                <label class="form-label">Dernier Bloc</label>
                                <div id="currentBlock" class="form-input" style="background: rgba(0,0,0,0.4);">0</div>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Prix Gas (Gwei)</label>
                                <div id="gasPrice" class="form-input" style="background: rgba(0,0,0,0.4);">0 gwei</div>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Véhicules Enregistrés</label>
                                <div id="totalVehicles" class="form-input" style="background: rgba(0,0,0,0.4);">0</div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Insurance Tab -->
            <section id="insurance" class="tab-content">
                <div class="card">
                    <h2 class="card-title">🚗 Assurance Véhicule Blockchain</h2>
                    <p class="card-description">
                        Enregistrez votre véhicule et souscrivez à une assurance révolutionnaire basée sur la blockchain.
                        Transparence totale, paiements automatiques, réclamations instantanées.
                    </p>
                    <div class="grid grid-2">
                        <div class="form-group">
                            <label class="form-label">Type de Véhicule</label>
                            <select id="vehicleType" class="form-input form-select">
                                <option value="">Choisissez votre véhicule</option>
                                <option value="car">🚗 Voiture Particulière</option>
                                <option value="motorcycle">🏍️ Moto / Scooter</option>
                                <option value="truck">🚚 Camion / Utilitaire</option>
                                <option value="bus">🚌 Autobus / Transport</option>
                                <option value="van">🚐 Fourgonnette</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Immatriculation Maroc</label>
                            <input type="text" id="vehicleVin" class="form-input" placeholder="Ex: 123456-A-01" maxlength="20">
                        </div>
                        <div class="form-group">
                            <label class="form-label">Marque</label>
                            <input type="text" id="vehicleMake" class="form-input" placeholder="Ex: Toyota, Renault, Dacia, BMW">
                        </div>
                        <div class="form-group">
                            <label class="form-label">Modèle</label>
                            <input type="text" id="vehicleModel" class="form-input" placeholder="Ex: Corolla, Clio, Logan, X3">
                        </div>
                        <div class="form-group">
                            <label class="form-label">Année de Fabrication</label>
                            <input type="number" id="vehicleYear" class="form-input" placeholder="2023" min="1990" max="2024">
                        </div>
                        <div class="form-group">
                            <label class="form-label">Valeur Estimée (ETH)</label>
                            <input type="number" id="vehicleValue" class="form-input" placeholder="5.0" step="0.1" min="0.1">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Ville de Résidence</label>
                        <select id="vehicleCity" class="form-input form-select">
                            <option value="">Sélectionnez votre ville</option>
                            <option value="casablanca">🏙️ Casablanca</option>
                            <option value="rabat">🏛️ Rabat</option>
                            <option value="marrakech">🕌 Marrakech</option>
                            <option value="fes">🎓 Fès</option>
                            <option value="tangier">⛵ Tanger</option>
                            <option value="agadir">🏖️ Agadir</option>
                            <option value="meknes">🌿 Meknès</option>
                            <option value="oujda">🌄 Oujda</option>
                        </select>
                    </div>
                    <div class="btn-group">
                        <button class="btn btn-primary" id="registerVehicleBtn">
                            <span class="loading hidden" id="registerLoading"></span>
                            📝 Enregistrer Véhicule
                        </button>
                        <button class="btn btn-success" id="buyInsuranceBtn" disabled>💰 Souscrire Assurance</button>
                        <button class="btn btn-secondary" id="viewPoliciesBtn">📋 Mes Polices</button>
                    </div>
                </div>
            </section>

            <!-- Dashboard Tab -->
            <section id="dashboard" class="tab-content">
                <div class="card">
                    <h2 class="card-title">📊 Dashboard Himaya</h2>
                    <p class="card-description">Gérez vos polices d'assurance, suivez vos réclamations et analysez vos données en temps réel.</p>
                    <div class="btn-group mb-6">
                        <button class="btn btn-secondary role-btn active" data-role="client">👤 Mode Client</button>
                        <button class="btn btn-secondary role-btn" data-role="insurer">🏢 Mode Assureur</button>
                        <button class="btn btn-secondary role-btn" data-role="admin">⚙️ Mode Admin</button>
                    </div>
                    <div class="grid grid-3 mb-6">
                        <div class="card text-center">
                            <div style="font-size: 3rem; margin-bottom: 1rem;">📋</div>
                            <div class="text-2xl font-bold" style="color: #8b5cf6;" id="totalPolicies">1,247</div>
                            <div style="color: #9ca3af;">Polices Actives</div>
                            <div style="color: #10b981; margin-top: 0.5rem;">+12% ce mois</div>
                        </div>
                        <div class="card text-center">
                            <div style="font-size: 3rem; margin-bottom: 1rem;">💰</div>
                            <div class="text-2xl font-bold" style="color: #10b981;" id="totalPremiums">2.85M MAD</div>
                            <div style="color: #9ca3af;">Primes Collectées</div>
                            <div style="color: #10b981; margin-top: 0.5rem;">+8% ce mois</div>
                        </div>
                        <div class="card text-center">
                            <div style="font-size: 3rem; margin-bottom: 1rem;">🔍</div>
                            <div class="text-2xl font-bold" style="color: #ec4899;" id="totalClaims">23</div>
                            <div style="color: #9ca3af;">Réclamations</div>
                            <div style="color: #ef4444; margin-top: 0.5rem;">-5% ce mois</div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Converter Tab -->
            <section id="converter" class="tab-content">
                <div class="card" style="background: linear-gradient(135deg, rgba(16, 185, 129, 0.3), rgba(5, 150, 105, 0.2));">
                    <h2 class="card-title">💱 Convertisseur ETH ⇄ MAD</h2>
                    <p class="card-description">
                        Convertissez instantanément entre Ethereum (ETH) et Dirham Marocain (MAD)
                        avec les taux de change en temps réel du marché marocain.
                    </p>
                    <div class="grid grid-2 mb-6">
                        <div class="form-group">
                            <label class="form-label">Ethereum (ETH)</label>
                            <div style="position: relative;">
                                <input type="number" id="ethAmount" class="form-input" placeholder="0.000000" step="0.000001" min="0" style="padding-right: 4rem;">
                                <span style="position: absolute; right: 1.5rem; top: 50%; transform: translateY(-50%); color: #10b981; font-weight: 700; font-size: 1.2rem;">ETH</span>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Dirham Marocain (MAD)</label>
                            <div style="position: relative;">
                                <input type="number" id="madAmount" class="form-input" placeholder="0.00" step="0.01" min="0" style="padding-right: 4rem;">
                                <span style="position: absolute; right: 1.5rem; top: 50%; transform: translateY(-50%); color: #10b981; font-weight: 700; font-size: 1.2rem;">MAD</span>
                            </div>
                        </div>
                    </div>
                    <div class="text-center mb-6">
                        <div style="font-size: 4rem; margin-bottom: 1rem;">⇄</div>
                        <div class="text-xl font-bold mb-2">Taux de Change Actuel</div>
                        <div class="text-2xl font-bold" style="color: #10b981;" id="exchangeRate">1 ETH = 35,000.00 MAD</div>
                        <div style="color: #9ca3af;" id="lastUpdate">Dernière mise à jour: maintenant</div>
                    </div>
                    <div class="btn-group text-center">
                        <button class="btn btn-success" id="refreshRatesBtn">🔄 Actualiser Taux</button>
                        <button class="btn btn-secondary" id="swapCurrenciesBtn">⇄ Inverser Devises</button>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- Web3 Library -->
    <script src="https://cdn.jsdelivr.net/npm/web3@4.2.0/dist/web3.min.js"></script>

    <script>
        // Application State
        const HimayaApp = {
            web3: null,
            account: null,
            networkId: null,
            connected: false,
            currentTab: 'connection',
            currentRole: 'client',
            exchangeRate: 35000,
            vehicles: [],
            policies: []
        };

        // Configuration
        const CONFIG = {
            NETWORK_ID: 1337,
            NETWORK_NAME: 'Himaya Private Network',
            RPC_URL: 'http://localhost:8545',
            CHAIN_ID: '0x539'
        };

        // Utility Functions
        function showNotification(message, type = 'info') {
            const existing = document.querySelectorAll('.notification');
            existing.forEach(n => n.remove());

            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 4000);
        }

        function updateStatus(element, connected, text) {
            const statusDot = document.getElementById(element);
            const statusText = document.getElementById(element.replace('Status', 'Text'));

            if (statusDot) {
                statusDot.className = `status-dot ${connected ? 'connected' : ''}`;
            }
            if (statusText) {
                statusText.textContent = text;
            }
        }

        function formatAddress(address) {
            if (!address) return 'Non connecté';
            return `${address.slice(0, 6)}...${address.slice(-4)}`;
        }

        // Tab Management
        function initTabs() {
            const navItems = document.querySelectorAll('.nav-btn');
            const tabContents = document.querySelectorAll('.tab-content');

            navItems.forEach(item => {
                item.addEventListener('click', (e) => {
                    e.preventDefault();
                    const tabId = item.getAttribute('data-tab');

                    navItems.forEach(nav => nav.classList.remove('active'));
                    item.classList.add('active');

                    tabContents.forEach(tab => {
                        tab.classList.remove('active');
                    });

                    const targetTab = document.getElementById(tabId);
                    if (targetTab) {
                        targetTab.classList.add('active');
                        HimayaApp.currentTab = tabId;
                    }
                });
            });
        }

        // Wallet Connection
        async function connectWallet() {
            const connectBtn = document.getElementById('connectWalletBtn');
            const loading = document.getElementById('connectLoading');

            if (!window.ethereum) {
                showNotification('❌ MetaMask non détecté. Veuillez installer MetaMask.', 'error');
                return;
            }

            try {
                loading.classList.remove('hidden');
                connectBtn.disabled = true;
                showNotification('🔄 Connexion en cours...', 'info');

                const accounts = await window.ethereum.request({
                    method: 'eth_requestAccounts'
                });

                if (accounts.length > 0) {
                    HimayaApp.account = accounts[0];
                    HimayaApp.connected = true;

                    HimayaApp.web3 = new Web3(window.ethereum);
                    HimayaApp.networkId = await HimayaApp.web3.eth.net.getId();

                    const balance = await HimayaApp.web3.eth.getBalance(HimayaApp.account);
                    const ethBalance = HimayaApp.web3.utils.fromWei(balance, 'ether');

                    document.getElementById('accountAddress').textContent = formatAddress(HimayaApp.account);
                    document.getElementById('accountBalance').textContent = parseFloat(ethBalance).toFixed(4) + ' ETH';
                    document.getElementById('networkInfo').textContent = `Réseau ID: ${HimayaApp.networkId}`;

                    updateStatus('walletStatus', true, 'Wallet: Connecté ✅');
                    updateStatus('networkStatus', true, 'Réseau: Connecté ✅');

                    connectBtn.innerHTML = '✅ Wallet Connecté';
                    connectBtn.classList.remove('btn-primary');
                    connectBtn.classList.add('btn-success');

                    showNotification('🎉 Wallet connecté avec succès!', 'success');

                    document.getElementById('buyInsuranceBtn').disabled = false;
                    await loadBlockchainInfo();
                }
            } catch (error) {
                console.error('Wallet connection failed:', error);
                showNotification('❌ Échec de la connexion au wallet', 'error');
            } finally {
                loading.classList.add('hidden');
                connectBtn.disabled = false;
            }
        }

        // Add Himaya Network
        async function addHimayaNetwork() {
            if (!window.ethereum) {
                showNotification('❌ MetaMask non détecté', 'error');
                return;
            }

            try {
                showNotification('🔄 Ajout du réseau Himaya...', 'info');

                await window.ethereum.request({
                    method: 'wallet_addEthereumChain',
                    params: [{
                        chainId: CONFIG.CHAIN_ID,
                        chainName: CONFIG.NETWORK_NAME,
                        nativeCurrency: {
                            name: 'Ethereum',
                            symbol: 'ETH',
                            decimals: 18
                        },
                        rpcUrls: [CONFIG.RPC_URL],
                        blockExplorerUrls: null
                    }]
                });

                showNotification('🎉 Réseau Himaya ajouté avec succès!', 'success');
            } catch (error) {
                console.error('Failed to add network:', error);
                showNotification('❌ Échec de l\'ajout du réseau', 'error');
            }
        }

        // Load Blockchain Info
        async function loadBlockchainInfo() {
            if (!HimayaApp.web3) return;

            try {
                const latestBlock = await HimayaApp.web3.eth.getBlockNumber();
                document.getElementById('currentBlock').textContent = latestBlock.toString();

                const gasPrice = await HimayaApp.web3.eth.getGasPrice();
                const gasPriceGwei = HimayaApp.web3.utils.fromWei(gasPrice, 'gwei');
                document.getElementById('gasPrice').textContent = `${parseFloat(gasPriceGwei).toFixed(2)} gwei`;

                updateStatus('contractStatus', true, 'Smart Contracts: Actifs ✅');
                document.getElementById('blockInfo').textContent = `Bloc: ${latestBlock} | Gas: ${parseFloat(gasPriceGwei).toFixed(2)} gwei`;

            } catch (error) {
                console.error('Failed to load blockchain info:', error);
            }
        }

        // Vehicle Registration
        async function registerVehicle() {
            const registerBtn = document.getElementById('registerVehicleBtn');
            const loading = document.getElementById('registerLoading');

            if (!HimayaApp.connected) {
                showNotification('⚠️ Veuillez d\'abord connecter votre wallet', 'warning');
                return;
            }

            const vehicleData = {
                type: document.getElementById('vehicleType').value,
                vin: document.getElementById('vehicleVin').value,
                make: document.getElementById('vehicleMake').value,
                model: document.getElementById('vehicleModel').value,
                year: document.getElementById('vehicleYear').value,
                value: document.getElementById('vehicleValue').value,
                city: document.getElementById('vehicleCity').value
            };

            if (!vehicleData.type || !vehicleData.vin || !vehicleData.make || !vehicleData.model) {
                showNotification('⚠️ Veuillez remplir tous les champs obligatoires', 'warning');
                return;
            }

            try {
                loading.classList.remove('hidden');
                registerBtn.disabled = true;
                showNotification('🔄 Enregistrement du véhicule...', 'info');

                await new Promise(resolve => setTimeout(resolve, 3000));

                HimayaApp.vehicles.push({
                    id: Date.now(),
                    ...vehicleData,
                    owner: HimayaApp.account,
                    registeredAt: new Date().toISOString()
                });

                document.getElementById('totalVehicles').textContent = HimayaApp.vehicles.length;

                // Clear form
                document.getElementById('vehicleType').value = '';
                document.getElementById('vehicleVin').value = '';
                document.getElementById('vehicleMake').value = '';
                document.getElementById('vehicleModel').value = '';
                document.getElementById('vehicleYear').value = '';
                document.getElementById('vehicleValue').value = '';
                document.getElementById('vehicleCity').value = '';

                showNotification('🎉 Véhicule enregistré avec succès!', 'success');
                document.getElementById('buyInsuranceBtn').disabled = false;

            } catch (error) {
                console.error('Vehicle registration failed:', error);
                showNotification('❌ Échec de l\'enregistrement du véhicule', 'error');
            } finally {
                loading.classList.add('hidden');
                registerBtn.disabled = false;
            }
        }

        // Currency Conversion
        function convertETHToMAD(ethValue) {
            if (!ethValue || ethValue <= 0) {
                document.getElementById('madAmount').value = '';
                return;
            }
            const madValue = parseFloat(ethValue) * HimayaApp.exchangeRate;
            document.getElementById('madAmount').value = madValue.toFixed(2);
        }

        function convertMADToETH(madValue) {
            if (!madValue || madValue <= 0) {
                document.getElementById('ethAmount').value = '';
                return;
            }
            const ethValue = parseFloat(madValue) / HimayaApp.exchangeRate;
            document.getElementById('ethAmount').value = ethValue.toFixed(6);
        }

        function refreshExchangeRates() {
            showNotification('🔄 Actualisation des taux de change...', 'info');

            setTimeout(() => {
                const variation = (Math.random() - 0.5) * 2000;
                HimayaApp.exchangeRate = 35000 + variation;

                document.getElementById('exchangeRate').textContent =
                    `1 ETH = ${HimayaApp.exchangeRate.toLocaleString('fr-FR', {minimumFractionDigits: 2})} MAD`;

                document.getElementById('lastUpdate').textContent =
                    `Dernière mise à jour: ${new Date().toLocaleTimeString('fr-FR')}`;

                showNotification('✅ Taux de change mis à jour!', 'success');
            }, 1500);
        }

        function swapCurrencies() {
            const ethInput = document.getElementById('ethAmount');
            const madInput = document.getElementById('madAmount');

            const ethValue = ethInput.value;
            const madValue = madInput.value;

            ethInput.value = madValue ? (parseFloat(madValue) / HimayaApp.exchangeRate).toFixed(6) : '';
            madInput.value = ethValue ? (parseFloat(ethValue) * HimayaApp.exchangeRate).toFixed(2) : '';

            showNotification('🔄 Devises inversées!', 'info');
        }

        // Role Management
        function switchRole(role) {
            HimayaApp.currentRole = role;

            document.querySelectorAll('.role-btn').forEach(btn => {
                btn.classList.remove('active');
                if (btn.getAttribute('data-role') === role) {
                    btn.classList.add('active');
                }
            });

            const stats = {
                client: { totalPolicies: '3', totalPremiums: '3.6 ETH', totalClaims: '1' },
                insurer: { totalPolicies: '1,247', totalPremiums: '2.85M MAD', totalClaims: '23' },
                admin: { totalPolicies: '5,432', totalPremiums: '12.3M MAD', totalClaims: '89' }
            };

            const roleStats = stats[role];
            if (roleStats) {
                document.getElementById('totalPolicies').textContent = roleStats.totalPolicies;
                document.getElementById('totalPremiums').textContent = roleStats.totalPremiums;
                document.getElementById('totalClaims').textContent = roleStats.totalClaims;
            }

            showNotification(`🔄 Rôle changé vers: ${role}`, 'info');
        }

        // Event Listeners
        function initEventListeners() {
            document.getElementById('connectWalletBtn').addEventListener('click', connectWallet);
            document.getElementById('addNetworkBtn').addEventListener('click', addHimayaNetwork);
            document.getElementById('refreshInfoBtn').addEventListener('click', loadBlockchainInfo);
            document.getElementById('registerVehicleBtn').addEventListener('click', registerVehicle);

            document.getElementById('ethAmount').addEventListener('input', (e) => {
                convertETHToMAD(e.target.value);
            });

            document.getElementById('madAmount').addEventListener('input', (e) => {
                convertMADToETH(e.target.value);
            });

            document.getElementById('refreshRatesBtn').addEventListener('click', refreshExchangeRates);
            document.getElementById('swapCurrenciesBtn').addEventListener('click', swapCurrencies);

            document.querySelectorAll('.role-btn').forEach(btn => {
                btn.addEventListener('click', () => {
                    const role = btn.getAttribute('data-role');
                    switchRole(role);
                });
            });

            document.getElementById('languageSelect').addEventListener('change', (e) => {
                showNotification(`🌍 Langue changée vers: ${e.target.value}`, 'info');
            });

            console.log('✅ All event listeners initialized');
        }

        // Initialize Application
        function initApp() {
            console.log('🚀 Initializing Fresh Himaya Blockchain DApp...');

            initTabs();
            initEventListeners();

            document.getElementById('exchangeRate').textContent =
                `1 ETH = ${HimayaApp.exchangeRate.toLocaleString('fr-FR')} MAD`;

            setTimeout(() => {
                showNotification('🛡️ Bienvenue sur Himaya Blockchain! Interface complètement nouvelle.', 'success');
            }, 1000);

            console.log('✅ Fresh Himaya Blockchain DApp initialized successfully');
        }

        // MetaMask event listeners
        if (window.ethereum) {
            window.ethereum.on('accountsChanged', (accounts) => {
                if (accounts.length === 0) {
                    HimayaApp.connected = false;
                    HimayaApp.account = null;
                    updateStatus('walletStatus', false, 'Wallet: Déconnecté');
                    showNotification('⚠️ Wallet déconnecté', 'warning');

                    document.getElementById('accountAddress').textContent = 'Non connecté';
                    document.getElementById('accountBalance').textContent = '0.0000 ETH';

                    const connectBtn = document.getElementById('connectWalletBtn');
                    connectBtn.innerHTML = '🦊 Connecter MetaMask';
                    connectBtn.classList.remove('btn-success');
                    connectBtn.classList.add('btn-primary');
                } else {
                    HimayaApp.account = accounts[0];
                    document.getElementById('accountAddress').textContent = formatAddress(HimayaApp.account);
                    showNotification('🔄 Compte changé', 'info');
                }
            });

            window.ethereum.on('chainChanged', (chainId) => {
                showNotification('🔄 Réseau changé, rechargement...', 'info');
                setTimeout(() => window.location.reload(), 2000);
            });
        }

        // Initialize when DOM is loaded
        document.addEventListener('DOMContentLoaded', initApp);

        // Global functions for console testing
        window.HimayaBlockchain = {
            connectWallet,
            addHimayaNetwork,
            registerVehicle,
            switchRole,
            convertETHToMAD,
            convertMADToETH,
            refreshExchangeRates,
            HimayaApp,
            CONFIG
        };

        console.log('🛡️ Fresh Himaya Blockchain DApp loaded successfully');
        console.log('🇲🇦 Nouvelle interface moderne prête pour le marché marocain');
    </script>
</body>
</html>
