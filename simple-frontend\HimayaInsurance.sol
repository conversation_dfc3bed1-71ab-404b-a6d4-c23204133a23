// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

contract HimayaInsurance {

    struct Vehicle {
        string vehicleId;
        string make;
        string model;
        uint256 year;
        string vehicleType;
        string city;
        address owner;
        string planType;
        uint256 registeredAt;
        bool isActive;
    }

    struct ClaimFile {
        string fileName;
        string fileHash;
        string fileType;
        uint256 fileSize;
        uint256 uploadedAt;
    }

    struct Claim {
        string claimId;
        address claimant;
        string vehicleId;
        string claimType;
        string description;
        uint256 amount;
        uint256 submittedAt;
        bool processed;
        bool approved;
        uint256 paidAmount;
        uint256 processedAt;
        string[] fileHashes;
        string rejectionReason;
    }

    struct ClientWallet {
        address walletAddress;
        string clientName;
        string clientId;
        string phoneNumber;
        string email;
        address assignedBy;
        uint256 assignedAt;
        bool isActive;
        bool isVerified;
    }

    struct Plan {
        string planType;
        uint256 priceWei;
        uint256 durationDays;
        uint256 maxClaimAmount;
        bool isActive;
    }

    struct UserSubscription {
        string planType;
        uint256 subscribedAt;
        uint256 expiresAt;
        uint256 paidAmount;
        bool isActive;
    }

    // State variables
    mapping(address => string[]) public userVehicles;
    mapping(string => Vehicle) public vehicles;
    mapping(string => Claim) public claims;
    mapping(address => UserSubscription) public userSubscriptions;
    mapping(string => Plan) public plans;
    mapping(string => ClaimFile) public claimFiles;
    mapping(string => string[]) public claimToFiles; // claimId => fileHashes[]
    mapping(address => ClientWallet) public clientWallets;
    mapping(address => bool) public authorizedInsurerWallets;

    string[] public allClaims;
    string[] public allVehicles;
    address[] public allUsers;
    address[] public allClientWallets;
    address public owner;

    uint256 public totalPremiumsCollected;
    uint256 public totalClaimsPaid;
    uint256 public totalActiveUsers;
    uint256 public contractBalance;
    
    // Events
    event VehicleRegistered(string vehicleId, address owner, string planType, uint256 timestamp);
    event PlanSubscribed(address user, string planType, uint256 amount, uint256 expiry);
    event ClaimSubmitted(string claimId, address claimant, string vehicleId, uint256 amount, uint256 timestamp);
    event ClaimProcessed(string claimId, bool approved, uint256 paidAmount, uint256 timestamp);
    event FundsWithdrawn(address owner, uint256 amount, uint256 timestamp);
    event PremiumPaid(address user, string planType, uint256 amount, uint256 timestamp);
    event FileUploaded(string claimId, string fileHash, string fileName, uint256 timestamp);
    event ClientWalletAssigned(address clientWallet, string clientName, address assignedBy, uint256 timestamp);
    event ClaimRejected(string claimId, string reason, uint256 timestamp);

    modifier onlyOwner() {
        require(msg.sender == owner, "Only owner can call this function");
        _;
    }

    modifier hasActivePlan() {
        require(userSubscriptions[msg.sender].isActive &&
                userSubscriptions[msg.sender].expiresAt > block.timestamp,
                "No active plan or plan expired");
        _;
    }

    modifier validPlan(string memory _planType) {
        require(plans[_planType].isActive, "Invalid or inactive plan");
        _;
    }

    modifier onlyVerifiedClient() {
        require(clientWallets[msg.sender].isActive && clientWallets[msg.sender].isVerified,
                "Only verified clients can perform this action");
        _;
    }

    modifier onlyAuthorizedInsurer() {
        require(authorizedInsurerWallets[msg.sender] || msg.sender == owner,
                "Only authorized insurers can perform this action");
        _;
    }

    constructor() {
        owner = msg.sender;
        contractBalance = 0;
        totalPremiumsCollected = 0;
        totalClaimsPaid = 0;
        totalActiveUsers = 0;

        // Initialize insurance plans with real prices
        plans["basic"] = Plan({
            planType: "basic",
            priceWei: 0.05 ether,
            durationDays: 30,
            maxClaimAmount: 5 ether,
            isActive: true
        });

        plans["standard"] = Plan({
            planType: "standard",
            priceWei: 0.08 ether,
            durationDays: 30,
            maxClaimAmount: 10 ether,
            isActive: true
        });

        plans["premium"] = Plan({
            planType: "premium",
            priceWei: 0.12 ether,
            durationDays: 30,
            maxClaimAmount: 20 ether,
            isActive: true
        });

        // Authorize the contract owner as an insurer
        authorizedInsurerWallets[owner] = true;
    }

    // Client Wallet Management Functions
    function assignClientWallet(
        address _clientWallet,
        string memory _clientName,
        string memory _clientId,
        string memory _phoneNumber,
        string memory _email
    ) public onlyAuthorizedInsurer {
        require(_clientWallet != address(0), "Invalid wallet address");
        require(bytes(_clientName).length > 0, "Client name required");
        require(bytes(_clientId).length > 0, "Client ID required");
        require(!clientWallets[_clientWallet].isActive, "Wallet already assigned");

        clientWallets[_clientWallet] = ClientWallet({
            walletAddress: _clientWallet,
            clientName: _clientName,
            clientId: _clientId,
            phoneNumber: _phoneNumber,
            email: _email,
            assignedBy: msg.sender,
            assignedAt: block.timestamp,
            isActive: true,
            isVerified: true
        });

        allClientWallets.push(_clientWallet);

        emit ClientWalletAssigned(_clientWallet, _clientName, msg.sender, block.timestamp);
    }

    function authorizeInsurer(address _insurerWallet) public onlyOwner {
        authorizedInsurerWallets[_insurerWallet] = true;
    }

    function revokeInsurerAuthorization(address _insurerWallet) public onlyOwner {
        authorizedInsurerWallets[_insurerWallet] = false;
    }
    
    function subscribeToPlan(string memory _planType) public payable validPlan(_planType) onlyVerifiedClient {
        Plan memory plan = plans[_planType];
        require(msg.value >= plan.priceWei, "Insufficient payment for plan");

        // If user doesn't have active subscription, increment user count
        if (!userSubscriptions[msg.sender].isActive) {
            totalActiveUsers++;
            allUsers.push(msg.sender);
        }

        uint256 expiryTime = block.timestamp + (plan.durationDays * 1 days);

        userSubscriptions[msg.sender] = UserSubscription({
            planType: _planType,
            subscribedAt: block.timestamp,
            expiresAt: expiryTime,
            paidAmount: msg.value,
            isActive: true
        });

        totalPremiumsCollected += msg.value;
        contractBalance += msg.value;

        emit PlanSubscribed(msg.sender, _planType, msg.value, expiryTime);
        emit PremiumPaid(msg.sender, _planType, msg.value, block.timestamp);
    }
    
    function registerVehicle(
        string memory _vehicleId,
        string memory _make,
        string memory _model,
        uint256 _year,
        string memory _vehicleType,
        string memory _city
    ) public hasActivePlan onlyVerifiedClient {
        require(bytes(_vehicleId).length > 0, "Vehicle ID required");
        require(bytes(_make).length > 0, "Vehicle make required");
        require(bytes(_model).length > 0, "Vehicle model required");
        require(_year >= 1990 && _year <= 2024, "Invalid vehicle year");
        require(!vehicles[_vehicleId].isActive, "Vehicle already registered");

        string memory userPlanType = userSubscriptions[msg.sender].planType;

        vehicles[_vehicleId] = Vehicle({
            vehicleId: _vehicleId,
            make: _make,
            model: _model,
            year: _year,
            vehicleType: _vehicleType,
            city: _city,
            owner: msg.sender,
            planType: userPlanType,
            registeredAt: block.timestamp,
            isActive: true
        });

        userVehicles[msg.sender].push(_vehicleId);
        allVehicles.push(_vehicleId);

        emit VehicleRegistered(_vehicleId, msg.sender, userPlanType, block.timestamp);
    }
    
    function submitClaim(
        string memory _claimId,
        string memory _vehicleId,
        string memory _claimType,
        string memory _description,
        uint256 _amount
    ) public hasActivePlan onlyVerifiedClient {
        require(bytes(_claimId).length > 0, "Claim ID required");
        require(bytes(_vehicleId).length > 0, "Vehicle ID required");
        require(bytes(_description).length > 0, "Description required");
        require(_amount > 0, "Amount must be greater than 0");
        require(vehicles[_vehicleId].owner == msg.sender, "Vehicle not owned by claimant");
        require(vehicles[_vehicleId].isActive, "Vehicle not active");
        require(bytes(claims[_claimId].claimId).length == 0, "Claim ID already exists");

        // Check if claim amount is within plan limits
        string memory userPlanType = userSubscriptions[msg.sender].planType;
        uint256 maxClaimAmount = plans[userPlanType].maxClaimAmount;
        require(_amount <= maxClaimAmount, "Claim amount exceeds plan limit");

        claims[_claimId] = Claim({
            claimId: _claimId,
            claimant: msg.sender,
            vehicleId: _vehicleId,
            claimType: _claimType,
            description: _description,
            amount: _amount,
            submittedAt: block.timestamp,
            processed: false,
            approved: false,
            paidAmount: 0,
            processedAt: 0,
            fileHashes: new string[](0),
            rejectionReason: ""
        });

        allClaims.push(_claimId);

        emit ClaimSubmitted(_claimId, msg.sender, _vehicleId, _amount, block.timestamp);
    }

    function uploadClaimFile(
        string memory _claimId,
        string memory _fileHash,
        string memory _fileName,
        string memory _fileType,
        uint256 _fileSize
    ) public {
        require(bytes(claims[_claimId].claimId).length > 0, "Claim does not exist");
        require(claims[_claimId].claimant == msg.sender, "Only claimant can upload files");
        require(!claims[_claimId].processed, "Cannot upload files to processed claim");
        require(bytes(_fileHash).length > 0, "File hash required");
        require(bytes(_fileName).length > 0, "File name required");

        claimFiles[_fileHash] = ClaimFile({
            fileName: _fileName,
            fileHash: _fileHash,
            fileType: _fileType,
            fileSize: _fileSize,
            uploadedAt: block.timestamp
        });

        claimToFiles[_claimId].push(_fileHash);
        claims[_claimId].fileHashes.push(_fileHash);

        emit FileUploaded(_claimId, _fileHash, _fileName, block.timestamp);
    }
    
    function processClaim(string memory _claimId, bool _approved, string memory _rejectionReason) public onlyAuthorizedInsurer {
        require(bytes(claims[_claimId].claimId).length > 0, "Claim does not exist");
        require(!claims[_claimId].processed, "Claim already processed");

        claims[_claimId].processed = true;
        claims[_claimId].approved = _approved;
        claims[_claimId].processedAt = block.timestamp;

        uint256 payoutAmount = 0;

        if (_approved) {
            payoutAmount = claims[_claimId].amount;

            // Ensure contract has sufficient balance
            require(address(this).balance >= payoutAmount, "Insufficient contract balance");
            require(contractBalance >= payoutAmount, "Insufficient tracked balance");

            // Transfer funds directly to claimant's wallet
            payable(claims[_claimId].claimant).transfer(payoutAmount);

            // Update contract state
            claims[_claimId].paidAmount = payoutAmount;
            totalClaimsPaid += payoutAmount;
            contractBalance -= payoutAmount;
        } else {
            // Store rejection reason
            claims[_claimId].rejectionReason = _rejectionReason;
            emit ClaimRejected(_claimId, _rejectionReason, block.timestamp);
        }

        emit ClaimProcessed(_claimId, _approved, payoutAmount, block.timestamp);
    }
    
    // View functions for real data retrieval
    function getUserVehicles(address _user) public view returns (string[] memory) {
        return userVehicles[_user];
    }

    function getAllClaims() public view returns (string[] memory) {
        return allClaims;
    }

    function getAllVehicles() public view returns (string[] memory) {
        return allVehicles;
    }

    function getAllUsers() public view returns (address[] memory) {
        return allUsers;
    }

    function getUserSubscription(address _user) public view returns (UserSubscription memory) {
        return userSubscriptions[_user];
    }

    function getVehicle(string memory _vehicleId) public view returns (Vehicle memory) {
        return vehicles[_vehicleId];
    }

    function getClaim(string memory _claimId) public view returns (Claim memory) {
        return claims[_claimId];
    }

    function getPlan(string memory _planType) public view returns (Plan memory) {
        return plans[_planType];
    }

    function getClientWallet(address _clientWallet) public view returns (ClientWallet memory) {
        return clientWallets[_clientWallet];
    }

    function getClaimFiles(string memory _claimId) public view returns (string[] memory) {
        return claimToFiles[_claimId];
    }

    function getFileDetails(string memory _fileHash) public view returns (ClaimFile memory) {
        return claimFiles[_fileHash];
    }

    function getAllClientWallets() public view returns (address[] memory) {
        return allClientWallets;
    }

    function isAuthorizedInsurer(address _insurer) public view returns (bool) {
        return authorizedInsurerWallets[_insurer];
    }

    function getContractStats() public view returns (
        uint256 totalPremiums,
        uint256 totalClaims,
        uint256 activeUsers,
        uint256 balance,
        uint256 totalVehicles,
        uint256 totalClaimsCount
    ) {
        return (
            totalPremiumsCollected,
            totalClaimsPaid,
            totalActiveUsers,
            contractBalance,
            allVehicles.length,
            allClaims.length
        );
    }

    function getClaimsByUser(address _user) public view returns (string[] memory) {
        string[] memory userClaims = new string[](allClaims.length);
        uint256 count = 0;

        for (uint256 i = 0; i < allClaims.length; i++) {
            if (claims[allClaims[i]].claimant == _user) {
                userClaims[count] = allClaims[i];
                count++;
            }
        }

        // Resize array to actual count
        string[] memory result = new string[](count);
        for (uint256 i = 0; i < count; i++) {
            result[i] = userClaims[i];
        }

        return result;
    }

    function getPendingClaims() public view returns (string[] memory) {
        string[] memory pendingClaims = new string[](allClaims.length);
        uint256 count = 0;

        for (uint256 i = 0; i < allClaims.length; i++) {
            if (!claims[allClaims[i]].processed) {
                pendingClaims[count] = allClaims[i];
                count++;
            }
        }

        // Resize array to actual count
        string[] memory result = new string[](count);
        for (uint256 i = 0; i < count; i++) {
            result[i] = pendingClaims[i];
        }

        return result;
    }

    // Emergency functions
    function emergencyWithdraw() public onlyOwner {
        uint256 balance = address(this).balance;
        require(balance > 0, "No funds to withdraw");

        payable(owner).transfer(balance);
        contractBalance = 0;

        emit FundsWithdrawn(owner, balance, block.timestamp);
    }

    function updatePlanPrice(string memory _planType, uint256 _newPrice) public onlyOwner {
        require(plans[_planType].isActive, "Plan does not exist");
        plans[_planType].priceWei = _newPrice;
    }

    function deactivatePlan(string memory _planType) public onlyOwner {
        plans[_planType].isActive = false;
    }

    function getContractBalance() public view returns (uint256) {
        return address(this).balance;
    }

    receive() external payable {
        contractBalance += msg.value;
    }

    fallback() external payable {
        contractBalance += msg.value;
    }
}
