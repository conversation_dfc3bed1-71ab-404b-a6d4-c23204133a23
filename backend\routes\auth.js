const express = require('express');
const jwt = require('jsonwebtoken');
const { body, validationResult } = require('express-validator');
const User = require('../models/User');
const { auth, verifyWallet } = require('../middleware/auth');
const rateLimit = require('express-rate-limit');

const router = express.Router();

// Configure login rate limiter
const loginLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // 5 failed attempts
  skipSuccessfulRequests: true,
  message: { 
    error: 'Too many failed login attempts, please try again later',
    code: 'AUTH008'
  }
});

// Generate JWT token
const generateToken = (userId, expiresIn = process.env.JWT_EXPIRES_IN || '24h') => {
  return jwt.sign({ id: userId }, process.env.JWT_SECRET, { expiresIn });
};

// Register
router.post('/register', [
  body('email').isEmail().normalizeEmail()
    .withMessage('Invalid email format'),
  body('password').isLength({ min: 6 })
    .withMessage('Password must be at least 6 characters')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .withMessage('Password must contain uppercase, lowercase, number and special character'),
  body('firstName').trim().isLength({ min: 1 })
    .withMessage('First name is required'),
  body('lastName').trim().isLength({ min: 1 })
    .withMessage('Last name is required'),
  body('walletAddress').isEthereumAddress()
    .withMessage('Invalid Ethereum address'),
  body('role').optional().isIn(['policyholder', 'agent', 'admin'])
    .withMessage('Invalid role')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        errors: errors.array(),
        code: 'REG001'
      });
    }

    const {
      email,
      password,
      firstName,
      lastName,
      walletAddress,
      role = 'policyholder',
      phone,
      address,
      dateOfBirth,
      licenseNumber
    } = req.body;

    // Check if user already exists
    const existingUser = await User.findOne({
      $or: [
        { email: email.toLowerCase() },
        { walletAddress: walletAddress.toLowerCase() }
      ]
    });

    if (existingUser) {
      return res.status(400).json({
        error: existingUser.email === email.toLowerCase() 
          ? 'Email already registered' 
          : 'Wallet address already registered',
        code: 'REG002'
      });
    }

    // Create new user
    const user = new User({
      email: email.toLowerCase(),
      password,
      firstName,
      lastName,
      walletAddress: walletAddress.toLowerCase(),
      role,
      phone,
      address,
      dateOfBirth,
      licenseNumber,
      isActive: true,
      trustedIPs: [req.ip], // Add registration IP as trusted
      registeredAt: new Date(),
      lastLogin: new Date()
    });

    await user.save();

    // Generate token
    const token = generateToken(user._id);

    res.status(201).json({
      message: 'User registered successfully',
      code: 'REG003',
      token,
      user: {
        id: user._id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
        walletAddress: user.walletAddress
      }
    });

  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({ 
      error: 'Registration failed',
      code: 'REG999'
    });
  }
});

// Login
router.post('/login', loginLimiter, [
  body('email').isEmail().normalizeEmail(),
  body('password').exists()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        errors: errors.array(),
        code: 'LOG001'
      });
    }

    const { email, password } = req.body;

    // Find user
    const user = await User.findOne({ email: email.toLowerCase() });
    if (!user) {
      return res.status(401).json({ 
        error: 'Invalid credentials',
        code: 'LOG002'
      });
    }

    // Check password
    const isMatch = await user.comparePassword(password);
    if (!isMatch) {
      return res.status(401).json({ 
        error: 'Invalid credentials',
        code: 'LOG002'
      });
    }

    // Check if account is active
    if (!user.isActive) {
      return res.status(401).json({ 
        error: 'Account is deactivated',
        code: 'LOG003'
      });
    }

    // Update last login and add IP to trusted IPs if not already there
    user.lastLogin = new Date();
    const clientIP = req.headers['x-forwarded-for'] || req.ip;
    if (!user.trustedIPs.includes(clientIP)) {
      user.trustedIPs.push(clientIP);
    }
    await user.save();

    // Generate token
    const token = generateToken(user._id);

    res.json({
      message: 'Login successful',
      code: 'LOG004',
      token,
      user: {
        id: user._id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
        walletAddress: user.walletAddress
      }
    });

  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ 
      error: 'Login failed',
      code: 'LOG999'
    });
  }
});

// Wallet login
router.post('/wallet-login', loginLimiter, verifyWallet, [
  body('walletAddress').isEthereumAddress(),
  body('signature').exists(),
  body('message').exists(),
  body('timestamp')
    .isInt()
    .custom((value) => {
      // Ensure timestamp is within last 5 minutes
      const fiveMinutesAgo = Date.now() - (5 * 60 * 1000);
      return parseInt(value) >= fiveMinutesAgo;
    })
    .withMessage('Message timestamp expired')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        errors: errors.array(),
        code: 'WLOG001'
      });
    }

    const { walletAddress } = req.body;

    // Find user by wallet address
    const user = await User.findOne({ 
      walletAddress: walletAddress.toLowerCase() 
    });

    if (!user) {
      return res.status(401).json({ 
        error: 'Wallet not registered',
        code: 'WLOG002'
      });
    }

    // Check if account is active
    if (!user.isActive) {
      return res.status(401).json({ 
        error: 'Account is deactivated',
        code: 'WLOG003'
      });
    }

    // Update last login and add IP to trusted IPs if not already there
    user.lastLogin = new Date();
    const clientIP = req.headers['x-forwarded-for'] || req.ip;
    if (!user.trustedIPs.includes(clientIP)) {
      user.trustedIPs.push(clientIP);
    }
    await user.save();

    // Generate token
    const token = generateToken(user._id);

    res.json({
      message: 'Wallet login successful',
      code: 'WLOG004',
      token,
      user: {
        id: user._id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
        walletAddress: user.walletAddress
      }
    });

  } catch (error) {
    console.error('Wallet login error:', error);
    res.status(500).json({ 
      error: 'Wallet login failed',
      code: 'WLOG999'
    });
  }
});

// Get current user profile
router.get('/profile', auth, async (req, res) => {
  try {
    res.json({
      code: 'PRO001',
      user: req.user
    });
  } catch (error) {
    console.error('Profile fetch error:', error);
    res.status(500).json({ 
      error: 'Failed to fetch profile',
      code: 'PRO999'
    });
  }
});

// Update profile
router.put('/profile', auth, [
  body('firstName').optional().trim().isLength({ min: 1 }),
  body('lastName').optional().trim().isLength({ min: 1 }),
  body('phone').optional().trim(),
  body('address').optional().isObject(),
  body('dateOfBirth').optional().isISO8601(),
  body('licenseNumber').optional().trim()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        errors: errors.array(),
        code: 'UPD001'
      });
    }

    const allowedUpdates = [
      'firstName', 'lastName', 'phone', 'address', 
      'dateOfBirth', 'licenseNumber', 'profileImage'
    ];
    
    const updates = {};
    Object.keys(req.body).forEach(key => {
      if (allowedUpdates.includes(key)) {
        updates[key] = req.body[key];
      }
    });

    const user = await User.findByIdAndUpdate(
      req.user._id,
      updates,
      { new: true, runValidators: true }
    ).select('-password');

    res.json({
      message: 'Profile updated successfully',
      code: 'UPD002',
      user
    });

  } catch (error) {
    console.error('Profile update error:', error);
    res.status(500).json({ 
      error: 'Failed to update profile',
      code: 'UPD999'
    });
  }
});

// Change password
router.put('/change-password', auth, [
  body('currentPassword').exists(),
  body('newPassword')
    .isLength({ min: 6 })
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .withMessage('Password must contain uppercase, lowercase, number and special character')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        errors: errors.array(),
        code: 'PWD001'
      });
    }

    const { currentPassword, newPassword } = req.body;

    const user = await User.findById(req.user._id);
    
    // Verify current password
    const isMatch = await user.comparePassword(currentPassword);
    if (!isMatch) {
      return res.status(400).json({ 
        error: 'Current password is incorrect',
        code: 'PWD002'
      });
    }

    // Update password
    user.password = newPassword;
    await user.save();

    res.json({ 
      message: 'Password changed successfully',
      code: 'PWD003'
    });

  } catch (error) {
    console.error('Password change error:', error);
    res.status(500).json({ 
      error: 'Failed to change password',
      code: 'PWD999'
    });
  }
});

// Logout (client-side token removal)
router.post('/logout', auth, (req, res) => {
  res.json({ 
    message: 'Logout successful',
    code: 'OUT001'
  });
});

module.exports = router;
