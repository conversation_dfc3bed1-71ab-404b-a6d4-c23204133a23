require("@nomicfoundation/hardhat-toolbox");
require("dotenv").config();

/** @type import('hardhat/config').HardhatUserConfig */
module.exports = {
  solidity: {
    version: "0.8.19",
    settings: {
      optimizer: {
        enabled: true,
        runs: 200
      }
    }
  },
  networks: {
    ganache: {
      url: "http://localhost:8545",
      accounts: [
        "0x1ab42cc412b618bdea3a599e3c9bae199ebf030895b0339e9db1e30dafb12b727", // Account 0
        "0x9a983cb3d832fbde5ab49d692b7a8bf5b5d232479c993333d0fc8e1d21f1b55b6", // Account 1
        "0x5b824bd1104617939cd07c117ddc4301eb5beeca0904ff964158963d69ab9d831"  // Account 2
      ],
      chainId: 1337,
      gas: 6000000,
      gasPrice: **********
    },
    hardhat: {
      chainId: 1337
    }
  },
  paths: {
    sources: "./contracts",
    tests: "./test",
    cache: "./cache",
    artifacts: "./artifacts"
  }
};
