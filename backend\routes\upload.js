const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { auth } = require('../middleware/auth');

const router = express.Router();

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadPath = path.join(__dirname, '../uploads');
    
    // Create uploads directory if it doesn't exist
    if (!fs.existsSync(uploadPath)) {
      fs.mkdirSync(uploadPath, { recursive: true });
    }
    
    cb(null, uploadPath);
  },
  filename: (req, file, cb) => {
    // Generate unique filename
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const extension = path.extname(file.originalname);
    cb(null, file.fieldname + '-' + uniqueSuffix + extension);
  }
});

// File filter
const fileFilter = (req, file, cb) => {
  // Allowed file types
  const allowedTypes = [
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/gif',
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'text/plain'
  ];

  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('Invalid file type. Only images, PDFs, and documents are allowed.'), false);
  }
};

const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: parseInt(process.env.MAX_FILE_SIZE) || 10 * 1024 * 1024, // 10MB default
    files: 10 // Maximum 10 files per upload
  }
});

// Upload single file
router.post('/single', auth, upload.single('file'), (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No file uploaded' });
    }

    const fileInfo = {
      filename: req.file.filename,
      originalName: req.file.originalname,
      mimetype: req.file.mimetype,
      size: req.file.size,
      path: req.file.path,
      url: `/uploads/${req.file.filename}`,
      uploadedBy: req.user._id,
      uploadDate: new Date()
    };

    res.json({
      message: 'File uploaded successfully',
      file: fileInfo
    });

  } catch (error) {
    console.error('File upload error:', error);
    res.status(500).json({ error: 'File upload failed' });
  }
});

// Upload multiple files
router.post('/multiple', auth, upload.array('files', 10), (req, res) => {
  try {
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({ error: 'No files uploaded' });
    }

    const filesInfo = req.files.map(file => ({
      filename: file.filename,
      originalName: file.originalname,
      mimetype: file.mimetype,
      size: file.size,
      path: file.path,
      url: `/uploads/${file.filename}`,
      uploadedBy: req.user._id,
      uploadDate: new Date()
    }));

    res.json({
      message: `${req.files.length} files uploaded successfully`,
      files: filesInfo
    });

  } catch (error) {
    console.error('Multiple file upload error:', error);
    res.status(500).json({ error: 'File upload failed' });
  }
});

// Upload claim documents
router.post('/claim-documents', auth, upload.array('documents', 10), (req, res) => {
  try {
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({ error: 'No documents uploaded' });
    }

    const { claimId, category = 'other', descriptions } = req.body;
    
    if (!claimId) {
      return res.status(400).json({ error: 'Claim ID is required' });
    }

    // Parse descriptions if it's a string (from form data)
    let parsedDescriptions = [];
    if (typeof descriptions === 'string') {
      try {
        parsedDescriptions = JSON.parse(descriptions);
      } catch (e) {
        parsedDescriptions = [descriptions];
      }
    } else if (Array.isArray(descriptions)) {
      parsedDescriptions = descriptions;
    }

    const documentsInfo = req.files.map((file, index) => ({
      filename: file.filename,
      originalName: file.originalname,
      mimetype: file.mimetype,
      size: file.size,
      path: file.path,
      url: `/uploads/${file.filename}`,
      claimId: parseInt(claimId),
      category: Array.isArray(category) ? category[index] || 'other' : category,
      description: parsedDescriptions[index] || file.originalname,
      uploadedBy: req.user._id,
      uploadDate: new Date()
    }));

    res.json({
      message: `${req.files.length} claim documents uploaded successfully`,
      documents: documentsInfo
    });

  } catch (error) {
    console.error('Claim documents upload error:', error);
    res.status(500).json({ error: 'Document upload failed' });
  }
});

// Upload vehicle images
router.post('/vehicle-images', auth, upload.array('images', 10), (req, res) => {
  try {
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({ error: 'No images uploaded' });
    }

    const { vehicleId, descriptions } = req.body;
    
    if (!vehicleId) {
      return res.status(400).json({ error: 'Vehicle ID is required' });
    }

    // Parse descriptions if it's a string (from form data)
    let parsedDescriptions = [];
    if (typeof descriptions === 'string') {
      try {
        parsedDescriptions = JSON.parse(descriptions);
      } catch (e) {
        parsedDescriptions = [descriptions];
      }
    } else if (Array.isArray(descriptions)) {
      parsedDescriptions = descriptions;
    }

    const imagesInfo = req.files.map((file, index) => ({
      filename: file.filename,
      originalName: file.originalname,
      mimetype: file.mimetype,
      size: file.size,
      path: file.path,
      url: `/uploads/${file.filename}`,
      vehicleId: parseInt(vehicleId),
      description: parsedDescriptions[index] || `Vehicle image ${index + 1}`,
      uploadedBy: req.user._id,
      uploadDate: new Date()
    }));

    res.json({
      message: `${req.files.length} vehicle images uploaded successfully`,
      images: imagesInfo
    });

  } catch (error) {
    console.error('Vehicle images upload error:', error);
    res.status(500).json({ error: 'Image upload failed' });
  }
});

// Delete file
router.delete('/:filename', auth, (req, res) => {
  try {
    const { filename } = req.params;
    const filePath = path.join(__dirname, '../uploads', filename);

    // Check if file exists
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({ error: 'File not found' });
    }

    // Delete file
    fs.unlinkSync(filePath);

    res.json({ message: 'File deleted successfully' });

  } catch (error) {
    console.error('File deletion error:', error);
    res.status(500).json({ error: 'File deletion failed' });
  }
});

// Get file info
router.get('/info/:filename', auth, (req, res) => {
  try {
    const { filename } = req.params;
    const filePath = path.join(__dirname, '../uploads', filename);

    // Check if file exists
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({ error: 'File not found' });
    }

    const stats = fs.statSync(filePath);
    const fileInfo = {
      filename: filename,
      size: stats.size,
      created: stats.birthtime,
      modified: stats.mtime,
      url: `/uploads/${filename}`
    };

    res.json({ file: fileInfo });

  } catch (error) {
    console.error('Get file info error:', error);
    res.status(500).json({ error: 'Failed to get file info' });
  }
});

// Error handling middleware for multer
router.use((error, req, res, next) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({ 
        error: 'File too large',
        maxSize: process.env.MAX_FILE_SIZE || '10MB'
      });
    }
    if (error.code === 'LIMIT_FILE_COUNT') {
      return res.status(400).json({ 
        error: 'Too many files',
        maxFiles: 10
      });
    }
    if (error.code === 'LIMIT_UNEXPECTED_FILE') {
      return res.status(400).json({ error: 'Unexpected file field' });
    }
  }
  
  if (error.message.includes('Invalid file type')) {
    return res.status(400).json({ error: error.message });
  }

  next(error);
});

module.exports = router;
