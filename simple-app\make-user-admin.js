const { ethers } = require('ethers');

// Configuration
const RPC_URL = 'http://localhost:8545';
const CURRENT_ADMIN_PRIVATE_KEY = '0x8f2a55949038a9610f50fb23b5883af3b4ecb3c3bb792cbcefbd1542c692be63';
const YOUR_WALLET_ADDRESS = '******************************************'; // Votre wallet

async function makeUserAdmin() {
    try {
        console.log('🔑 Configuration des droits admin...');
        console.log('');

        // Connect to blockchain
        const provider = new ethers.JsonRpcProvider(RPC_URL);
        const adminWallet = new ethers.Wallet(CURRENT_ADMIN_PRIVATE_KEY, provider);
        
        console.log(`👨‍💼 Admin actuel: ${adminWallet.address}`);
        console.log(`👤 Votre wallet: ${YOUR_WALLET_ADDRESS}`);
        console.log('');

        // Check balances
        const adminBalance = await provider.getBalance(adminWallet.address);
        const userBalance = await provider.getBalance(YOUR_WALLET_ADDRESS);
        
        console.log('💰 Soldes actuels:');
        console.log(`   Admin: ${ethers.formatEther(adminBalance)} ETH`);
        console.log(`   Vous: ${ethers.formatEther(userBalance)} ETH`);
        console.log('');

        // Send 500 ETH to your wallet for admin operations
        console.log('💸 Envoi de 500 ETH pour les opérations admin...');
        const fundingTx = await adminWallet.sendTransaction({
            to: YOUR_WALLET_ADDRESS,
            value: ethers.parseEther('500'),
            gasLimit: 21000
        });

        console.log(`🔄 Transaction envoyée: ${fundingTx.hash}`);
        console.log('⏳ Attente de confirmation...');
        
        await fundingTx.wait();
        
        // Check new balances
        const newUserBalance = await provider.getBalance(YOUR_WALLET_ADDRESS);
        console.log(`✅ Nouveau solde: ${ethers.formatEther(newUserBalance)} ETH`);
        console.log('');

        console.log('🎉 Configuration terminée!');
        console.log('');
        console.log('📋 Maintenant vous pouvez:');
        console.log('   1. Vous connecter avec votre wallet normal');
        console.log('   2. Aller dans l\'onglet Admin');
        console.log('   3. Gérer les claims avec vos propres fonds');
        console.log('');
        console.log('💡 Note: Votre wallet aura maintenant 700 ETH au total');
        console.log('   (200 ETH d\'avant + 500 ETH pour admin = 700 ETH)');

    } catch (error) {
        console.error('❌ Erreur:', error.message);
        process.exit(1);
    }
}

// Run the script
makeUserAdmin();
