const sqlite3 = require('sqlite3').verbose();
const fs = require('fs');

console.log('🔍 Checking database status...');
console.log('');

// Check if database file exists
const dbPath = './insurance.db';
if (fs.existsSync(dbPath)) {
    console.log('✅ Database file exists');
    const stats = fs.statSync(dbPath);
    console.log(`📊 File size: ${stats.size} bytes`);
    console.log(`📅 Last modified: ${stats.mtime}`);
} else {
    console.log('❌ Database file does not exist');
    process.exit(1);
}

console.log('');

// Open database and check contents
const db = new sqlite3.Database(dbPath);

db.serialize(() => {
    // Check vehicles
    db.all("SELECT COUNT(*) as count FROM vehicles", (err, rows) => {
        if (err) {
            console.log('❌ Error checking vehicles:', err.message);
        } else {
            console.log(`🚗 Vehicles in database: ${rows[0].count}`);
        }
    });

    // Check policies
    db.all("SELECT COUNT(*) as count FROM policies", (err, rows) => {
        if (err) {
            console.log('❌ Error checking policies:', err.message);
        } else {
            console.log(`🛡️ Policies in database: ${rows[0].count}`);
        }
    });

    // Check claims
    db.all("SELECT COUNT(*) as count FROM claims", (err, rows) => {
        if (err) {
            console.log('❌ Error checking claims:', err.message);
        } else {
            console.log(`📋 Claims in database: ${rows[0].count}`);
        }
    });

    // Show all claims with details
    db.all(`
        SELECT c.*, p.coverage_type, v.make, v.model, v.year, v.vin 
        FROM claims c 
        JOIN policies p ON c.policy_id = p.id 
        JOIN vehicles v ON p.vehicle_id = v.id 
        ORDER BY c.created_at DESC
    `, (err, rows) => {
        if (err) {
            console.log('❌ Error getting claim details:', err.message);
        } else {
            console.log('');
            console.log('📋 All claims in database:');
            if (rows.length === 0) {
                console.log('   No claims found');
            } else {
                rows.forEach((claim, index) => {
                    console.log(`   ${index + 1}. Claim ID: ${claim.id}`);
                    console.log(`      Vehicle: ${claim.make} ${claim.model} (${claim.year})`);
                    console.log(`      Amount: ${claim.amount} ETH`);
                    console.log(`      Description: ${claim.description}`);
                    console.log(`      Owner: ${claim.owner}`);
                    console.log(`      Processed: ${claim.processed ? 'Yes' : 'No'}`);
                    console.log(`      Approved: ${claim.approved ? 'Yes' : 'No'}`);
                    console.log('');
                });
            }
        }
        
        db.close();
    });
});
