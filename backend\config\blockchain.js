const { Web3 } = require('web3');
const EventEmitter = require('events');
const fs = require('fs').promises;
const path = require('path');

class BlockchainEventEmitter extends EventEmitter {}
const blockchainEvents = new BlockchainEventEmitter();

// Contract ABIs
let VehicleRegistry, InsurancePolicy, ClaimManager;

// Web3 instance
let web3;

// Contract instances
let contracts = {};

// Connected accounts
let accounts = [];

// Configuration
const config = {
  maxGasLimit: 6000000,
  defaultGasPrice: '***********', // 20 Gwei
  confirmationBlocks: 2,
  maxRetries: 5,
  retryDelay: 1000,
  connectionTimeout: 30000,
  networkCheckInterval: 5000, // 5 seconds
  minPeers: 2
};

// Helper to load contract ABI
const loadContractABI = async (contractName) => {
  try {
    const abiPath = path.join(__dirname, `${contractName}.json`);
    const abi = JSON.parse(await fs.readFile(abiPath, 'utf8'));
    return abi;
  } catch (error) {
    console.error(`Error loading ${contractName} ABI:`, error);
    throw error;
  }
};

// Helper to validate Ethereum address
const isValidAddress = (address) => {
  return web3.utils.isAddress(address);
};

// Helper to estimate gas with safety margin
const estimateGasWithMargin = async (transaction, from) => {
  try {
    const gasEstimate = await transaction.estimateGas({ from });
    return Math.min(
      Math.ceil(gasEstimate * 1.2), // Add 20% margin
      config.maxGasLimit
    );
  } catch (error) {
    console.error('Gas estimation error:', error);
    throw error;
  }
};

// Helper to check network status
const checkNetworkStatus = async () => {
  try {
    const networkId = await web3.eth.net.getId();
    const peerCount = await web3.eth.net.getPeerCount();
    const isSyncing = await web3.eth.isSyncing();
    
    if (peerCount < config.minPeers) {
      blockchainEvents.emit('warning', {
        type: 'PEER_COUNT_LOW',
        message: `Peer count (${peerCount}) below minimum threshold (${config.minPeers})`
      });
    }
    
    return { networkId, peerCount, isSyncing };
  } catch (error) {
    console.error('Network status check failed:', error);
    throw error;
  }
};

// Transaction sender with retry mechanism
const sendTransactionWithRetry = async (transaction, options, retryCount = 0) => {
  try {
    const gas = await estimateGasWithMargin(transaction, options.from);
    
    const txParams = {
      ...options,
      gas,
      gasPrice: options.gasPrice || config.defaultGasPrice
    };
    
    const receipt = await transaction.send(txParams)
      .on('transactionHash', (hash) => {
        blockchainEvents.emit('transactionHash', hash);
      })
      .on('confirmation', (confirmationNumber) => {
        if (confirmationNumber === config.confirmationBlocks) {
          blockchainEvents.emit('confirmed');
        }
      });
    
    return receipt;
  } catch (error) {
    if (retryCount < config.maxRetries && 
        (error.message.includes('nonce too low') || 
         error.message.includes('transaction underpriced'))) {
      console.log(`Retrying transaction (${retryCount + 1}/${config.maxRetries})...`);
      await new Promise(resolve => setTimeout(resolve, config.retryDelay));
      return sendTransactionWithRetry(transaction, options, retryCount + 1);
    }
    throw error;
  }
};

// Initialize blockchain connection
const initializeBlockchain = async () => {
  try {
    // Connect to blockchain
    const provider = new Web3.providers.WebsocketProvider(
      process.env.BLOCKCHAIN_WS_URL || 'ws://localhost:8546',
      {
        timeout: config.connectionTimeout,
        clientConfig: {
          maxReceivedFrameSize: 100000000,
          maxReceivedMessageSize: 100000000,
        },
        reconnect: {
          auto: true,
          delay: 5000,
          maxAttempts: 5,
          onTimeout: false
        }
      }
    );

    web3 = new Web3(provider);

    // Handle provider events
    provider.on('connect', () => {
      console.log('Web3 provider connected');
      blockchainEvents.emit('connected');
    });

    provider.on('error', (error) => {
      console.error('Web3 provider error:', error);
      blockchainEvents.emit('error', error);
    });

    provider.on('end', () => {
      console.log('Web3 provider disconnected');
      blockchainEvents.emit('disconnected');
    });

    // Load contract ABIs
    [VehicleRegistry, InsurancePolicy, ClaimManager] = await Promise.all([
      loadContractABI('VehicleRegistry'),
      loadContractABI('InsurancePolicy'),
      loadContractABI('ClaimManager')
    ]);

    // Get connected accounts
    accounts = await web3.eth.getAccounts();
    if (accounts.length === 0) {
      throw new Error('No accounts found');
    }

    // Initialize contract instances
    contracts = {
      vehicleRegistry: new web3.eth.Contract(
        VehicleRegistry.abi,
        process.env.VEHICLE_REGISTRY_ADDRESS
      ),
      insurancePolicy: new web3.eth.Contract(
        InsurancePolicy.abi,
        process.env.INSURANCE_POLICY_ADDRESS
      ),
      claimManager: new web3.eth.Contract(
        ClaimManager.abi,
        process.env.CLAIM_MANAGER_ADDRESS
      )
    };

    // Start network monitoring
    setInterval(async () => {
      try {
        await checkNetworkStatus();
      } catch (error) {
        console.error('Network monitoring error:', error);
      }
    }, config.networkCheckInterval);

    console.log('Blockchain connection initialized successfully');
    blockchainEvents.emit('initialized');
  } catch (error) {
    console.error('Blockchain initialization error:', error);
    blockchainEvents.emit('initializationError', error);
    throw error;
  }
};

module.exports = {
  web3,
  contracts,
  accounts,
  initializeBlockchain,
  sendTransaction: sendTransactionWithRetry,
  isValidAddress,
  blockchainEvents,
  estimateGas: estimateGasWithMargin,
  checkNetworkStatus
};