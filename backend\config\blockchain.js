const { Web3 } = require('web3');
const fs = require('fs');
const path = require('path');

let web3;
let contracts = {};
let accounts = {};

const initializeBlockchain = async () => {
  try {
    // Initialize Web3
    const rpcUrl = process.env.ETHEREUM_RPC_URL || 'http://localhost:8545';
    web3 = new Web3(rpcUrl);

    console.log('🔗 Connecting to Private Ethereum Network...');

    // Test connection
    try {
      const blockNumber = await web3.eth.getBlockNumber();
      console.log(`✅ Connected to Private Ethereum network. Latest block: ${blockNumber}`);
    } catch (error) {
      console.error(`❌ Failed to connect to blockchain at ${rpcUrl}: ${error.message}`);
      console.log('⚠️ Will attempt to reconnect when blockchain is available');
      // Continue initialization to prevent app from crashing
    }
    
    // Load deployment info
    const deploymentPath = path.join(__dirname, '../../deployments.json');

    if (!fs.existsSync(deploymentPath)) {
      console.log('⚠️ Deployment file not found. Please deploy contracts first.');
      return;
    }

    const deploymentInfo = JSON.parse(fs.readFileSync(deploymentPath, 'utf8'));

    // Load contract ABIs
    const abiDir = path.join(__dirname, '../../abi');
    
    if (!fs.existsSync(abiDir)) {
      console.log('⚠️ ABI directory not found. Creating directory...');
      fs.mkdirSync(abiDir, { recursive: true });
      console.log('⚠️ ABIs not found. Please compile contracts first.');
      return;
    }

    try {
      // Load ABIs with better error handling
      const vehicleRegistryABI = JSON.parse(
        fs.readFileSync(path.join(abiDir, 'VehicleRegistry.json'), 'utf8')
      );
      const insurancePolicyABI = JSON.parse(
        fs.readFileSync(path.join(abiDir, 'InsurancePolicy.json'), 'utf8')
      );
      const claimManagerABI = JSON.parse(
        fs.readFileSync(path.join(abiDir, 'ClaimManager.json'), 'utf8')
      );
      
      // Initialize contracts
      contracts.vehicleRegistry = new web3.eth.Contract(
        vehicleRegistryABI,
        deploymentInfo.contracts.VehicleRegistry.address
      );
      
      contracts.insurancePolicy = new web3.eth.Contract(
        insurancePolicyABI,
        deploymentInfo.contracts.InsurancePolicy.address
      );
      
      contracts.claimManager = new web3.eth.Contract(
        claimManagerABI,
        deploymentInfo.contracts.ClaimManager.address
      );
      
      console.log('✅ Smart contracts initialized:');
      console.log(`   VehicleRegistry: ${contracts.vehicleRegistry.options.address}`);
      console.log(`   InsurancePolicy: ${contracts.insurancePolicy.options.address}`);
      console.log(`   ClaimManager: ${contracts.claimManager.options.address}`);
    } catch (error) {
      console.error('❌ Failed to load contract ABIs:', error.message);
      console.log('⚠️ Please ensure the contracts are compiled and deployed correctly');
      return;
    }
    
    // Initialize accounts
    const deployerPrivateKey = process.env.DEPLOYER_PRIVATE_KEY;
    const insurer1PrivateKey = process.env.INSURER1_PRIVATE_KEY;
    const insurer2PrivateKey = process.env.INSURER2_PRIVATE_KEY;
    
    if (deployerPrivateKey) {
      accounts.deployer = web3.eth.accounts.privateKeyToAccount(deployerPrivateKey);
      web3.eth.accounts.wallet.add(accounts.deployer);
    }
    
    if (insurer1PrivateKey) {
      accounts.insurer1 = web3.eth.accounts.privateKeyToAccount(insurer1PrivateKey);
      web3.eth.accounts.wallet.add(accounts.insurer1);
    }
    
    if (insurer2PrivateKey) {
      accounts.insurer2 = web3.eth.accounts.privateKeyToAccount(insurer2PrivateKey);
      web3.eth.accounts.wallet.add(accounts.insurer2);
    }
    
    // Set up event listeners
    setupEventListeners();
    
  } catch (error) {
    console.error('❌ Blockchain initialization failed:', error.message);
    console.log('⚠️ Application will continue without blockchain integration');
  }
};

const setupEventListeners = () => {
  try {
    // Vehicle Registration Events
    contracts.vehicleRegistry.events.VehicleRegistered({
      fromBlock: 'latest'
    })
    .on('data', (event) => {
      console.log('🚗 Vehicle Registered:', event.returnValues);
    })
    .on('error', (error) => {
      console.error('Vehicle event error:', error.message);
    });
    
    // Policy Events
    contracts.insurancePolicy.events.PolicyCreated({
      fromBlock: 'latest'
    })
    .on('data', (event) => {
      console.log('📋 Policy Created:', event.returnValues);
    })
    .on('error', (error) => {
      console.error('Policy event error:', error.message);
    });
    
    // Claim Events
    contracts.claimManager.events.ClaimSubmitted({
      fromBlock: 'latest'
    })
    .on('data', (event) => {
      console.log('📝 Claim Submitted:', event.returnValues);
    })
    .on('error', (error) => {
      console.error('Claim event error:', error.message);
    });
    
    contracts.claimManager.events.ClaimStatusChanged({
      fromBlock: 'latest'
    })
    .on('data', (event) => {
      console.log('🔄 Claim Status Changed:', event.returnValues);
    })
    .on('error', (error) => {
      console.error('Claim status event error:', error.message);
    });
    
    console.log('👂 Event listeners set up');
    
  } catch (error) {
    console.error('❌ Failed to set up event listeners:', error.message);
  }
};

const getGasPrice = async () => {
  try {
    const gasPrice = await web3.eth.getGasPrice();
    return gasPrice;
  } catch (error) {
    console.error('Failed to get gas price:', error);
    return '0'; // Use 0 gas price for development
  }
};

const estimateGas = async (transaction) => {
  try {
    const gas = await transaction.estimateGas();
    return Math.floor(gas * 1.2); // Add 20% buffer
  } catch (error) {
    console.error('Failed to estimate gas:', error);
    return 6000000; // Default gas limit
  }
};

const sendTransaction = async (transaction, fromAccount) => {
  try {
    const gasPrice = await getGasPrice();
    const gas = await estimateGas(transaction);
    
    const result = await transaction.send({
      from: fromAccount.address,
      gas: gas,
      gasPrice: gasPrice
    });
    
    return result;
  } catch (error) {
    console.error('Transaction failed:', error);
    throw error;
  }
};

// Try to reconnect if initial connection fails
const retryConnection = async (maxRetries = 5, interval = 10000) => {
  let retries = 0;
  
  const attemptConnection = async () => {
    try {
      if (!web3 || !(await web3.eth.net.isListening())) {
        const rpcUrl = process.env.ETHEREUM_RPC_URL || 'http://localhost:8545';
        web3 = new Web3(rpcUrl);
        const blockNumber = await web3.eth.getBlockNumber();
        console.log(`✅ Reconnected to Ethereum network. Latest block: ${blockNumber}`);
        
        // Reinitialize contracts and event listeners
        await initializeBlockchain();
        return true;
      }
      return true;
    } catch (error) {
      retries++;
      console.error(`❌ Connection attempt ${retries}/${maxRetries} failed: ${error.message}`);
      
      if (retries >= maxRetries) {
        console.error('❌ Max retry attempts reached. Giving up.');
        return false;
      }
      
      console.log(`⏳ Retrying in ${interval/1000} seconds...`);
      return new Promise(resolve => {
        setTimeout(() => resolve(attemptConnection()), interval);
      });
    }
  };
  
  return attemptConnection();
};

module.exports = {
  initializeBlockchain,
  retryConnection,
  get web3() { return web3; },
  get contracts() { return contracts; },
  get accounts() { return accounts; },
  getGasPrice,
  estimateGas,
  sendTransaction
};