{"name": "himaya-insurance-simple", "version": "1.0.0", "description": "Himaya Blockchain Insurance Platform - Simplified Version", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "install-deps": "npm install", "setup": "npm install && node server.js"}, "keywords": ["blockchain", "insurance", "ethereum", "sqlite", "web3"], "author": "Himaya Insurance Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "sqlite3": "^5.1.6", "cors": "^2.8.5", "ethers": "^6.7.1"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=16.0.0"}}