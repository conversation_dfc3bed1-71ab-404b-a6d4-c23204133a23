[{"inputs": [{"internalType": "address", "name": "_insurancePolicyAddress", "type": "address"}, {"internalType": "address", "name": "_vehicleRegistryAddress", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "OwnableInvalidOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "OwnableUnauthorizedAccount", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "claimId", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "approvedAmount", "type": "uint256"}, {"indexed": false, "internalType": "address", "name": "reviewer", "type": "address"}], "name": "ClaimApproved", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "claimId", "type": "uint256"}, {"indexed": false, "internalType": "string", "name": "reason", "type": "string"}, {"indexed": false, "internalType": "address", "name": "reviewer", "type": "address"}], "name": "ClaimRejected", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "claimId", "type": "uint256"}, {"indexed": false, "internalType": "enum ClaimManager.ClaimStatus", "name": "previousStatus", "type": "uint8"}, {"indexed": false, "internalType": "enum ClaimManager.ClaimStatus", "name": "newStatus", "type": "uint8"}, {"indexed": false, "internalType": "address", "name": "reviewer", "type": "address"}], "name": "<PERSON>laim<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "claimId", "type": "uint256"}, {"indexed": true, "internalType": "uint256", "name": "policyId", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "claimant", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "claimedAmount", "type": "uint256"}], "name": "<PERSON>laimSubmitted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "claimId", "type": "uint256"}, {"indexed": false, "internalType": "string", "name": "documentHash", "type": "string"}], "name": "DocumentAdded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "reviewer", "type": "address"}], "name": "ReviewerAuthorized", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "reviewer", "type": "address"}], "name": "ReviewerRevoked", "type": "event"}, {"inputs": [{"internalType": "uint256", "name": "_claimId", "type": "uint256"}, {"internalType": "string", "name": "_documentHash", "type": "string"}], "name": "addDocument", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_claimId", "type": "uint256"}, {"internalType": "uint256", "name": "_approvedAmount", "type": "uint256"}, {"internalType": "string", "name": "_reviewNotes", "type": "string"}], "name": "approve<PERSON>laim", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_reviewer", "type": "address"}], "name": "authorize<PERSON><PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "authorizedReviewers", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "uint256", "name": "", "type": "uint256"}], "name": "claimant<PERSON><PERSON><PERSON>", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "claims", "outputs": [{"internalType": "uint256", "name": "id", "type": "uint256"}, {"internalType": "uint256", "name": "policyId", "type": "uint256"}, {"internalType": "address", "name": "claimant", "type": "address"}, {"internalType": "enum ClaimManager.ClaimType", "name": "claimType", "type": "uint8"}, {"internalType": "string", "name": "description", "type": "string"}, {"internalType": "uint256", "name": "claimedAmount", "type": "uint256"}, {"internalType": "uint256", "name": "approvedAmount", "type": "uint256"}, {"internalType": "enum ClaimManager.ClaimStatus", "name": "status", "type": "uint8"}, {"internalType": "uint256", "name": "incidentDate", "type": "uint256"}, {"internalType": "uint256", "name": "submissionDate", "type": "uint256"}, {"internalType": "string", "name": "location", "type": "string"}, {"internalType": "address", "name": "reviewer", "type": "address"}, {"internalType": "string", "name": "reviewNotes", "type": "string"}, {"internalType": "uint256", "name": "reviewDate", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_claimId", "type": "uint256"}], "name": "get<PERSON>laim", "outputs": [{"components": [{"internalType": "uint256", "name": "id", "type": "uint256"}, {"internalType": "uint256", "name": "policyId", "type": "uint256"}, {"internalType": "address", "name": "claimant", "type": "address"}, {"internalType": "enum ClaimManager.ClaimType", "name": "claimType", "type": "uint8"}, {"internalType": "string", "name": "description", "type": "string"}, {"internalType": "uint256", "name": "claimedAmount", "type": "uint256"}, {"internalType": "uint256", "name": "approvedAmount", "type": "uint256"}, {"internalType": "enum ClaimManager.ClaimStatus", "name": "status", "type": "uint8"}, {"internalType": "uint256", "name": "incidentDate", "type": "uint256"}, {"internalType": "uint256", "name": "submissionDate", "type": "uint256"}, {"internalType": "string", "name": "location", "type": "string"}, {"internalType": "string[]", "name": "documentHashes", "type": "string[]"}, {"internalType": "address", "name": "reviewer", "type": "address"}, {"internalType": "string", "name": "reviewNotes", "type": "string"}, {"internalType": "uint256", "name": "reviewDate", "type": "uint256"}], "internalType": "struct ClaimManager.Claim", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_claimant", "type": "address"}], "name": "getClaimantClaims", "outputs": [{"internalType": "uint256[]", "name": "", "type": "uint256[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_policyId", "type": "uint256"}], "name": "getPolicyClaims", "outputs": [{"internalType": "uint256[]", "name": "", "type": "uint256[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_reviewer", "type": "address"}], "name": "getReviewerClaims", "outputs": [{"internalType": "uint256[]", "name": "", "type": "uint256[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getTotalClaims", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "insurancePolicy", "outputs": [{"internalType": "contract InsurancePolicy", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}], "name": "policyClaims", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_claimId", "type": "uint256"}, {"internalType": "string", "name": "_reason", "type": "string"}], "name": "rejectClaim", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "uint256", "name": "", "type": "uint256"}], "name": "reviewerClaims", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_reviewer", "type": "address"}], "name": "revoke<PERSON><PERSON><PERSON>wer", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_policyId", "type": "uint256"}, {"internalType": "enum ClaimManager.ClaimType", "name": "_claimType", "type": "uint8"}, {"internalType": "string", "name": "_description", "type": "string"}, {"internalType": "uint256", "name": "_claimed<PERSON><PERSON>", "type": "uint256"}, {"internalType": "uint256", "name": "_incidentDate", "type": "uint256"}, {"internalType": "string", "name": "_location", "type": "string"}, {"internalType": "string[]", "name": "_documentHashes", "type": "string[]"}], "name": "submitClaim", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_claimId", "type": "uint256"}, {"internalType": "enum ClaimManager.ClaimStatus", "name": "_newStatus", "type": "uint8"}, {"internalType": "string", "name": "_reviewNotes", "type": "string"}], "name": "updateClaimStatus", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "vehicleRegistry", "outputs": [{"internalType": "contract VehicleRegistry", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}]