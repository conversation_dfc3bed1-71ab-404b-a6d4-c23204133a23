# 🛡️ Himaya Insurance - Simplified Blockchain Platform

A streamlined, single-page application for blockchain-based vehicle insurance with SQLite database integration.

## ✨ Features

- **🎨 Beautiful Modern UI** - Responsive design with gradient backgrounds and smooth animations
- **🔗 MetaMask Integration** - Connect your wallet to interact with the blockchain
- **📊 SQLite Database** - Local database for fast data access and offline capability
- **⚡ Real-time Sync** - Automatic synchronization between database and blockchain
- **🚗 Vehicle Management** - Register and manage your vehicles
- **🛡️ Policy Creation** - Create insurance policies with ETH payments
- **📋 Claims Processing** - Submit and track insurance claims
- **📈 Dashboard Analytics** - Overview of your insurance portfolio

## 🚀 Quick Start

### Prerequisites

- Node.js (v16 or higher)
- MetaMask browser extension
- Local Ethereum network (Ganache/Hardhat) running on port 8545

### Installation

1. **Install Dependencies**
   ```bash
   cd simple-app
   npm install
   ```

2. **Start the Server**
   ```bash
   npm start
   ```

3. **Open in Browser**
   ```
   http://localhost:3002
   ```

### Setup MetaMask

1. **Add Local Network**
   - Network Name: `Himaya Local`
   - RPC URL: `http://localhost:8545`
   - Chain ID: `1337`
   - Currency Symbol: `ETH`

2. **Import Test Account**
   - Use one of the private keys from your local blockchain
   - Make sure you have test ETH for transactions

## 🏗️ Architecture

### Frontend (Single HTML File)
- **Pure HTML/CSS/JavaScript** - No build process required
- **Ethers.js** - Blockchain interaction
- **SQL.js** - Client-side SQLite database
- **Responsive Design** - Works on desktop and mobile

### Backend (Node.js)
- **Express.js** - REST API server
- **SQLite3** - Persistent database
- **CORS Enabled** - Cross-origin requests
- **Blockchain Sync** - Automatic data synchronization

### Database Schema

```sql
-- Vehicles table
CREATE TABLE vehicles (
    id INTEGER PRIMARY KEY,
    vin TEXT UNIQUE,
    make TEXT,
    model TEXT,
    year INTEGER,
    owner TEXT,
    blockchain_id INTEGER,
    created_at DATETIME
);

-- Policies table
CREATE TABLE policies (
    id INTEGER PRIMARY KEY,
    vehicle_id INTEGER,
    premium REAL,
    coverage_amount REAL,
    coverage_type TEXT,
    owner TEXT,
    blockchain_id INTEGER,
    active BOOLEAN,
    created_at DATETIME
);

-- Claims table
CREATE TABLE claims (
    id INTEGER PRIMARY KEY,
    policy_id INTEGER,
    amount REAL,
    description TEXT,
    owner TEXT,
    blockchain_id INTEGER,
    approved BOOLEAN,
    processed BOOLEAN,
    created_at DATETIME
);
```

## 📱 Usage Guide

### 1. Connect Wallet
- Click "Connect MetaMask Wallet"
- Approve the connection in MetaMask
- Your wallet address and balance will be displayed

### 2. Register Vehicle
- Go to "Vehicles" tab
- Fill in vehicle details (VIN, Make, Model, Year)
- Click "Register Vehicle"
- Confirm the blockchain transaction

### 3. Create Insurance Policy
- Go to "Policies" tab
- Select a registered vehicle
- Choose coverage type and amounts
- Pay the premium in ETH
- Policy is created on blockchain and database

### 4. Submit Claims
- Go to "Claims" tab
- Select an active policy
- Enter claim amount and description
- Submit claim for processing

### 5. View Dashboard
- Overview of all your insurance data
- Real-time statistics and recent activity
- Total coverage and claim status

## 🔧 API Endpoints

### Health Check
```
GET /api/health
```

### Vehicles
```
GET /api/vehicles/:owner
POST /api/vehicles
```

### Policies
```
GET /api/policies/:owner
POST /api/policies
```

### Claims
```
GET /api/claims/:owner
POST /api/claims
```

### Dashboard
```
GET /api/dashboard/:owner
```

### Sync
```
POST /api/sync
```

## 🛠️ Development

### File Structure
```
simple-app/
├── index.html          # Main application file
├── server.js           # Backend API server
├── package.json        # Dependencies
├── README.md          # This file
└── insurance.db       # SQLite database (created automatically)
```

### Key Features

1. **Hybrid Architecture**: Client-side SQLite for speed + Server-side SQLite for persistence
2. **Blockchain Integration**: Real ETH transactions with smart contracts
3. **Responsive Design**: Works on all screen sizes
4. **Real-time Updates**: Automatic UI updates after blockchain transactions
5. **Error Handling**: Comprehensive error handling and user feedback

### Customization

- **Styling**: Modify CSS variables in the `<style>` section
- **Contract Addresses**: Update `CONTRACT_ADDRESSES` in the JavaScript
- **Database Schema**: Modify table structures in both client and server
- **UI Components**: Add new tabs and forms as needed

## 🔒 Security Features

- **Client-side Validation**: Form validation before submission
- **Blockchain Verification**: All transactions verified on blockchain
- **Local Database**: Sensitive data stored locally
- **CORS Protection**: Configured for secure API access

## 🚨 Troubleshooting

### Common Issues

1. **MetaMask Not Connecting**
   - Ensure MetaMask is installed and unlocked
   - Check network configuration (Chain ID: 1337)
   - Refresh the page and try again

2. **Database Errors**
   - Check browser console for SQL.js errors
   - Clear browser cache and reload
   - Restart the server

3. **Blockchain Connection Failed**
   - Ensure local blockchain is running on port 8545
   - Check contract addresses are correct
   - Verify you have test ETH in your wallet

4. **Transactions Failing**
   - Check gas limits and prices
   - Ensure sufficient ETH balance
   - Verify contract is deployed correctly

## 📞 Support

For issues and questions:
- Check the browser console for error messages
- Verify all prerequisites are installed
- Ensure blockchain network is running
- Check MetaMask connection and network settings

## 🎯 Next Steps

This simplified version provides a solid foundation. Consider adding:
- User authentication
- File upload for claim documents
- Email notifications
- Mobile app version
- Advanced analytics
- Multi-language support

---

**🛡️ Himaya Insurance - Protecting Your Journey with Blockchain Technology**
