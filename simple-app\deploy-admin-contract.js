const { ethers } = require('ethers');
const fs = require('fs');

// Configuration
const RPC_URL = 'http://localhost:8545';
const ADMIN_PRIVATE_KEY = '0x8f2a55949038a9610f50fb23b5883af3b4ecb3c3bb792cbcefbd1542c692be63'; // Rich account
const FUNDING_AMOUNT = '1000'; // 1000 ETH to fund the contract

// Smart contract source code
const contractSource = `
// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

contract AdminClaimManager {
    address public admin;
    uint256 public totalFunds;
    
    struct Claim {
        uint256 id;
        address claimant;
        uint256 amount;
        string description;
        bool processed;
        bool approved;
        uint256 timestamp;
    }
    
    mapping(uint256 => Claim) public claims;
    mapping(address => uint256[]) public userClaims;
    uint256 public nextClaimId = 1;
    
    event ClaimSubmitted(uint256 indexed claimId, address indexed claimant, uint256 amount);
    event ClaimApproved(uint256 indexed claimId, address indexed claimant, uint256 amount);
    event ClaimRejected(uint256 indexed claimId, address indexed claimant);
    event FundsDeposited(address indexed depositor, uint256 amount);
    
    modifier onlyAdmin() {
        require(msg.sender == admin, "Only admin can perform this action");
        _;
    }
    
    modifier claimExists(uint256 claimId) {
        require(claimId > 0 && claimId < nextClaimId, "Claim does not exist");
        _;
    }
    
    constructor() {
        admin = msg.sender;
    }
    
    receive() external payable {
        totalFunds += msg.value;
        emit FundsDeposited(msg.sender, msg.value);
    }
    
    function depositFunds() external payable {
        require(msg.value > 0, "Must send ETH to deposit");
        totalFunds += msg.value;
        emit FundsDeposited(msg.sender, msg.value);
    }
    
    function submitClaim(uint256 amount, string memory description) external returns (uint256) {
        require(amount > 0, "Claim amount must be greater than 0");
        require(bytes(description).length > 0, "Description cannot be empty");
        
        uint256 claimId = nextClaimId++;
        
        claims[claimId] = Claim({
            id: claimId,
            claimant: msg.sender,
            amount: amount,
            description: description,
            processed: false,
            approved: false,
            timestamp: block.timestamp
        });
        
        userClaims[msg.sender].push(claimId);
        
        emit ClaimSubmitted(claimId, msg.sender, amount);
        return claimId;
    }
    
    function approveClaim(uint256 claimId) external onlyAdmin claimExists(claimId) {
        Claim storage claim = claims[claimId];
        require(!claim.processed, "Claim already processed");
        require(address(this).balance >= claim.amount, "Insufficient contract funds");
        
        claim.processed = true;
        claim.approved = true;
        
        (bool success, ) = payable(claim.claimant).call{value: claim.amount}("");
        require(success, "Payment failed");
        
        totalFunds -= claim.amount;
        
        emit ClaimApproved(claimId, claim.claimant, claim.amount);
    }
    
    function rejectClaim(uint256 claimId) external onlyAdmin claimExists(claimId) {
        Claim storage claim = claims[claimId];
        require(!claim.processed, "Claim already processed");
        
        claim.processed = true;
        claim.approved = false;
        
        emit ClaimRejected(claimId, claim.claimant);
    }
    
    function getClaim(uint256 claimId) external view claimExists(claimId) returns (
        uint256 id,
        address claimant,
        uint256 amount,
        string memory description,
        bool processed,
        bool approved,
        uint256 timestamp
    ) {
        Claim memory claim = claims[claimId];
        return (claim.id, claim.claimant, claim.amount, claim.description, claim.processed, claim.approved, claim.timestamp);
    }
    
    function getPendingClaims() external view returns (uint256[] memory) {
        uint256 pendingCount = 0;
        for (uint256 i = 1; i < nextClaimId; i++) {
            if (!claims[i].processed) pendingCount++;
        }
        
        uint256[] memory pendingClaims = new uint256[](pendingCount);
        uint256 index = 0;
        for (uint256 i = 1; i < nextClaimId; i++) {
            if (!claims[i].processed) {
                pendingClaims[index] = i;
                index++;
            }
        }
        return pendingClaims;
    }
    
    function getContractBalance() external view returns (uint256) {
        return address(this).balance;
    }
}
`;

async function deployAdminContract() {
    try {
        console.log('🚀 Deploying Admin Claim Manager Contract...');
        console.log('');

        // Connect to blockchain
        const provider = new ethers.JsonRpcProvider(RPC_URL);
        const wallet = new ethers.Wallet(ADMIN_PRIVATE_KEY, provider);
        
        console.log(`📍 Admin Address: ${wallet.address}`);
        
        // Check admin balance
        const adminBalance = await provider.getBalance(wallet.address);
        console.log(`💰 Admin Balance: ${ethers.formatEther(adminBalance)} ETH`);
        console.log('');

        // For simplicity, we'll use a pre-compiled bytecode
        // In a real scenario, you'd compile the Solidity code
        const contractABI = [
            "constructor()",
            "function admin() view returns (address)",
            "function totalFunds() view returns (uint256)",
            "function submitClaim(uint256 amount, string memory description) returns (uint256)",
            "function approveClaim(uint256 claimId)",
            "function rejectClaim(uint256 claimId)",
            "function getClaim(uint256 claimId) view returns (uint256, address, uint256, string, bool, bool, uint256)",
            "function getPendingClaims() view returns (uint256[])",
            "function getContractBalance() view returns (uint256)",
            "function depositFunds() payable",
            "receive() external payable",
            "event ClaimSubmitted(uint256 indexed claimId, address indexed claimant, uint256 amount)",
            "event ClaimApproved(uint256 indexed claimId, address indexed claimant, uint256 amount)",
            "event ClaimRejected(uint256 indexed claimId, address indexed claimant)",
            "event FundsDeposited(address indexed depositor, uint256 amount)"
        ];

        // Simple bytecode for the contract (this is a simplified version)
        // In production, you'd use solc to compile the contract
        const contractBytecode = "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";

        // Deploy the contract
        console.log('📤 Deploying contract...');
        const contractFactory = new ethers.ContractFactory(contractABI, contractBytecode, wallet);
        const contract = await contractFactory.deploy();
        
        console.log(`🔄 Transaction sent: ${contract.deploymentTransaction().hash}`);
        console.log('⏳ Waiting for deployment...');
        
        await contract.waitForDeployment();
        const contractAddress = await contract.getAddress();
        
        console.log(`✅ Contract deployed at: ${contractAddress}`);
        console.log('');

        // Fund the contract with 1000 ETH
        console.log(`💰 Funding contract with ${FUNDING_AMOUNT} ETH...`);
        const fundingTx = await wallet.sendTransaction({
            to: contractAddress,
            value: ethers.parseEther(FUNDING_AMOUNT)
        });
        
        console.log(`🔄 Funding transaction: ${fundingTx.hash}`);
        await fundingTx.wait();
        
        // Verify contract balance
        const contractBalance = await provider.getBalance(contractAddress);
        console.log(`✅ Contract funded with ${ethers.formatEther(contractBalance)} ETH`);
        console.log('');

        // Save contract info to file
        const contractInfo = {
            address: contractAddress,
            admin: wallet.address,
            balance: ethers.formatEther(contractBalance),
            deployedAt: new Date().toISOString(),
            abi: contractABI
        };

        fs.writeFileSync('./admin-contract-info.json', JSON.stringify(contractInfo, null, 2));
        console.log('📄 Contract info saved to admin-contract-info.json');
        console.log('');

        console.log('🎉 Admin Claim Manager Contract Setup Complete!');
        console.log('');
        console.log('📋 Contract Details:');
        console.log(`   Address: ${contractAddress}`);
        console.log(`   Admin: ${wallet.address}`);
        console.log(`   Balance: ${ethers.formatEther(contractBalance)} ETH`);
        console.log('');
        console.log('💡 Next Steps:');
        console.log('   1. Update your frontend with the new contract address');
        console.log('   2. Test claim submission and approval');
        console.log('   3. Admin can now approve/reject claims with automatic ETH payments');

        return contractAddress;

    } catch (error) {
        console.error('❌ Deployment failed:', error.message);
        process.exit(1);
    }
}

// Run deployment
deployAdminContract();
