/**
 * Enhanced Styles for SecureShield Insurance
 * Improved visual hierarchy, accessibility, and UX
 */

/* Enhanced Loading States */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(15, 23, 42, 0.9);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 1;
    transition: opacity 0.3s ease;
}

.loading-content {
    text-align: center;
    color: var(--text-primary);
    max-width: 300px;
}

.loading-spinner {
    position: relative;
    width: 60px;
    height: 60px;
    margin: 0 auto 20px;
}

.spinner-ring {
    position: absolute;
    width: 100%;
    height: 100%;
    border: 3px solid transparent;
    border-top: 3px solid var(--primary-light);
    border-radius: 50%;
    animation: spin 1.2s linear infinite;
}

.spinner-ring:nth-child(2) {
    animation-delay: -0.4s;
    border-top-color: var(--accent-teal);
}

.spinner-ring:nth-child(3) {
    animation-delay: -0.8s;
    border-top-color: var(--accent-emerald);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-message {
    font-size: 1.1rem;
    font-weight: 500;
    margin-bottom: 15px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.loading-progress {
    width: 100%;
    height: 4px;
    background: rgba(148, 163, 184, 0.3);
    border-radius: 2px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: var(--gradient-primary);
    border-radius: 2px;
    transition: width 0.3s ease;
    width: 0%;
}

/* Enhanced Notifications */
.notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9998;
    max-width: 400px;
}

.notification {
    background: rgba(30, 41, 59, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(148, 163, 184, 0.3);
    border-radius: var(--radius-lg);
    padding: 16px;
    margin-bottom: 12px;
    box-shadow: var(--shadow-lg);
    transform: translateX(100%);
    opacity: 0;
    animation: slideInRight 0.3s ease forwards;
    transition: all 0.3s ease;
}

.notification:hover {
    transform: translateX(0) scale(1.02);
    box-shadow: var(--shadow-xl);
}

.notification-success {
    border-left: 4px solid var(--success);
}

.notification-error {
    border-left: 4px solid var(--error);
}

.notification-warning {
    border-left: 4px solid var(--warning);
}

.notification-info {
    border-left: 4px solid var(--info);
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 12px;
    color: var(--text-primary);
}

.notification-icon {
    font-size: 1.2rem;
    flex-shrink: 0;
}

.notification-message {
    flex: 1;
    font-weight: 500;
}

.notification-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.notification-close:hover {
    background: rgba(148, 163, 184, 0.2);
    color: var(--text-primary);
}

.notification-actions {
    margin-top: 12px;
    display: flex;
    gap: 8px;
}

.notification-action {
    background: var(--primary-light);
    color: var(--white);
    border: none;
    padding: 6px 12px;
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.notification-action:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Enhanced Modals */
.modal-container {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9997;
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(15, 23, 42, 0.8);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    opacity: 1;
    animation: fadeIn 0.3s ease;
}

.modal {
    background: rgba(30, 41, 59, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(148, 163, 184, 0.3);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    max-width: 90vw;
    max-height: 90vh;
    overflow: hidden;
    animation: slideInUp 0.3s ease;
}

.modal-small { max-width: 400px; }
.modal-medium { max-width: 600px; }
.modal-large { max-width: 800px; }
.modal-xlarge { max-width: 1200px; }

.modal-header {
    padding: 24px 24px 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-title {
    color: var(--text-primary);
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 1.5rem;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.modal-close:hover {
    background: rgba(148, 163, 184, 0.2);
    color: var(--text-primary);
}

.modal-body {
    padding: 24px;
    color: var(--text-primary);
    overflow-y: auto;
    max-height: 60vh;
}

.modal-actions {
    padding: 0 24px 24px;
    display: flex;
    gap: 12px;
    justify-content: flex-end;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideInUp {
    from {
        transform: translateY(50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Enhanced Progress Bars */
.progress-container {
    margin: 16px 0;
}

.progress-label {
    display: block;
    color: var(--text-primary);
    font-weight: 500;
    margin-bottom: 8px;
}

.progress-bar-container {
    position: relative;
    width: 100%;
    height: 8px;
    background: rgba(148, 163, 184, 0.3);
    border-radius: 4px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    border-radius: 4px;
    transition: width 0.3s ease;
    position: relative;
}

.progress-primary { background: var(--gradient-primary); }
.progress-success { background: var(--gradient-secondary); }
.progress-warning { background: linear-gradient(90deg, var(--warning), #f97316); }
.progress-error { background: linear-gradient(90deg, var(--error), #dc2626); }

.progress-bar.animated::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

.progress-percentage {
    position: absolute;
    right: 0;
    top: -25px;
    color: var(--text-secondary);
    font-size: 0.875rem;
    font-weight: 500;
}

.progress-complete {
    animation: pulseSuccess 0.5s ease;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

@keyframes pulseSuccess {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.02); }
}

/* Enhanced Tooltips */
.tooltip-container {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 9996;
    pointer-events: none;
}

.tooltip {
    position: absolute;
    background: rgba(15, 23, 42, 0.95);
    backdrop-filter: blur(10px);
    color: var(--text-primary);
    padding: 8px 12px;
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 500;
    box-shadow: var(--shadow-lg);
    border: 1px solid rgba(148, 163, 184, 0.3);
    opacity: 0;
    transform: scale(0.8);
    transition: all 0.2s ease;
    max-width: 250px;
    word-wrap: break-word;
}

.tooltip-visible {
    opacity: 1;
    transform: scale(1);
}

.tooltip-arrow {
    position: absolute;
    width: 8px;
    height: 8px;
    background: rgba(15, 23, 42, 0.95);
    border: 1px solid rgba(148, 163, 184, 0.3);
    transform: rotate(45deg);
}

.tooltip-top .tooltip-arrow {
    bottom: -5px;
    left: 50%;
    margin-left: -4px;
    border-top: none;
    border-left: none;
}

.tooltip-bottom .tooltip-arrow {
    top: -5px;
    left: 50%;
    margin-left: -4px;
    border-bottom: none;
    border-right: none;
}

.tooltip-left .tooltip-arrow {
    right: -5px;
    top: 50%;
    margin-top: -4px;
    border-left: none;
    border-bottom: none;
}

.tooltip-right .tooltip-arrow {
    left: -5px;
    top: 50%;
    margin-top: -4px;
    border-right: none;
    border-top: none;
}

/* Enhanced Charts */
.chart-container {
    background: rgba(30, 41, 59, 0.6);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(148, 163, 184, 0.2);
    border-radius: var(--radius-xl);
    padding: 24px;
    margin: 20px 0;
    box-shadow: var(--shadow-md);
}

.chart-title {
    color: var(--text-primary);
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 20px;
    text-align: center;
}

.chart-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
}

.chart-svg {
    overflow: visible;
}

.chart-bar {
    transition: all 0.3s ease;
    cursor: pointer;
}

.chart-bar:hover {
    opacity: 0.8;
    transform: translateY(-2px);
}

.chart-bar.animated {
    animation: growUp 1s ease-out forwards;
    transform-origin: bottom;
}

.chart-line {
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.chart-point {
    transition: all 0.3s ease;
    cursor: pointer;
}

.chart-point:hover {
    r: 6;
    filter: drop-shadow(0 2px 8px rgba(59, 130, 246, 0.5));
}

.chart-label {
    fill: var(--text-secondary);
    font-size: 12px;
    font-weight: 500;
}

.chart-value {
    fill: var(--text-primary);
    font-size: 11px;
    font-weight: 600;
}

.chart-segment {
    transition: all 0.3s ease;
    cursor: pointer;
}

.chart-segment:hover {
    opacity: 0.8;
    transform: scale(1.05);
    transform-origin: center;
}

.chart-legend {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.legend-color {
    width: 12px;
    height: 12px;
    border-radius: 2px;
    flex-shrink: 0;
}

@keyframes growUp {
    from {
        transform: scaleY(0);
    }
    to {
        transform: scaleY(1);
    }
}

@keyframes drawLine {
    to {
        stroke-dashoffset: 0;
    }
}

/* Skip Links for Accessibility */
.skip-links {
    position: absolute;
    top: -40px;
    left: 6px;
    z-index: 10000;
}

.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--primary-dark);
    color: var(--white);
    padding: 8px 16px;
    border-radius: var(--radius-md);
    text-decoration: none;
    font-weight: 600;
    transition: top 0.3s ease;
}

.skip-link:focus {
    top: 6px;
}

/* Enhanced Focus Indicators */
*:focus-visible {
    outline: 2px solid var(--primary-light);
    outline-offset: 2px;
    border-radius: var(--radius-sm);
}

button:focus-visible,
.btn:focus-visible {
    outline-offset: 4px;
}

input:focus-visible,
select:focus-visible,
textarea:focus-visible {
    outline-offset: 0;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}

/* Enhanced Visual Hierarchy */
.section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24px;
    padding-bottom: 12px;
    border-bottom: 2px solid rgba(148, 163, 184, 0.2);
}

.section-title {
    color: var(--text-primary);
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 12px;
}

.section-subtitle {
    color: var(--text-secondary);
    font-size: 0.875rem;
    margin-top: 4px;
}

.section-actions {
    display: flex;
    gap: 8px;
}

/* Enhanced Cards with Better Hierarchy */
.info-card {
    background: rgba(15, 23, 42, 0.8);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(148, 163, 184, 0.2);
    border-radius: var(--radius-lg);
    padding: 20px;
    margin: 12px 0;
    transition: all 0.3s ease;
}

.info-card:hover {
    border-color: rgba(148, 163, 184, 0.4);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.info-card-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
}

.info-card-icon {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-lg);
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: var(--white);
}

.info-card-title {
    color: var(--text-primary);
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0;
}

.info-card-content {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Enhanced Navigation Breadcrumbs */
.breadcrumb {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 24px;
    padding: 12px 0;
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.breadcrumb-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.breadcrumb-link {
    color: var(--primary-light);
    text-decoration: none;
    transition: color 0.2s ease;
}

.breadcrumb-link:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

.breadcrumb-separator {
    color: var(--text-muted);
    font-size: 0.75rem;
}

.breadcrumb-current {
    color: var(--text-primary);
    font-weight: 500;
}

/* Enhanced Form Elements */
.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    color: var(--text-primary);
    font-weight: 600;
    margin-bottom: 8px;
    font-size: 0.875rem;
}

.form-label.required::after {
    content: ' *';
    color: var(--error);
}

.form-input {
    width: 100%;
    padding: 12px 16px;
    background: rgba(15, 23, 42, 0.8);
    border: 2px solid rgba(148, 163, 184, 0.3);
    border-radius: var(--radius-lg);
    color: var(--text-primary);
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-input:focus {
    border-color: var(--primary-light);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
    background: rgba(15, 23, 42, 0.9);
}

.form-input.error {
    border-color: var(--error);
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.2);
}

.form-error {
    color: var(--error);
    font-size: 0.875rem;
    margin-top: 4px;
    display: flex;
    align-items: center;
    gap: 4px;
}

.form-help {
    color: var(--text-muted);
    font-size: 0.875rem;
    margin-top: 4px;
}

/* Enhanced File Upload */
.file-upload {
    position: relative;
    display: inline-block;
    cursor: pointer;
    width: 100%;
}

.file-upload-input {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.file-upload-area {
    border: 2px dashed rgba(148, 163, 184, 0.3);
    border-radius: var(--radius-lg);
    padding: 40px 20px;
    text-align: center;
    background: rgba(15, 23, 42, 0.4);
    transition: all 0.3s ease;
}

.file-upload-area:hover,
.file-upload-area.dragover {
    border-color: var(--primary-light);
    background: rgba(59, 130, 246, 0.1);
}

.file-upload-icon {
    font-size: 2rem;
    color: var(--text-muted);
    margin-bottom: 12px;
}

.file-upload-text {
    color: var(--text-secondary);
    font-weight: 500;
}

.file-upload-hint {
    color: var(--text-muted);
    font-size: 0.875rem;
    margin-top: 8px;
}

/* Document Upload Styles */
.document-upload-container {
    margin: 20px 0;
}

.upload-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.upload-title {
    color: var(--text-primary);
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0;
}

.upload-counter {
    color: var(--text-secondary);
    font-size: 0.875rem;
    background: rgba(148, 163, 184, 0.2);
    padding: 4px 8px;
    border-radius: var(--radius-md);
}

.uploaded-files {
    margin-top: 20px;
}

.uploaded-file {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: rgba(15, 23, 42, 0.6);
    border: 1px solid rgba(148, 163, 184, 0.2);
    border-radius: var(--radius-lg);
    margin-bottom: 8px;
    transition: all 0.3s ease;
}

.uploaded-file:hover {
    background: rgba(15, 23, 42, 0.8);
    border-color: rgba(148, 163, 184, 0.3);
}

.file-preview {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-md);
    overflow: hidden;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(148, 163, 184, 0.2);
}

.file-thumbnail {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.file-icon {
    font-size: 1.5rem;
}

.file-info {
    flex: 1;
    min-width: 0;
}

.file-name {
    color: var(--text-primary);
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.file-details {
    display: flex;
    gap: 12px;
    margin-top: 4px;
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.file-actions {
    display: flex;
    gap: 4px;
}

.btn-icon {
    background: none;
    border: none;
    color: var(--text-secondary);
    padding: 6px;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 1rem;
}

.btn-icon:hover {
    background: rgba(148, 163, 184, 0.2);
    color: var(--text-primary);
}

.btn-icon.btn-danger:hover {
    background: rgba(239, 68, 68, 0.2);
    color: var(--error);
}

/* Stat Cards */
.stat-card {
    background: rgba(15, 23, 42, 0.6);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(148, 163, 184, 0.2);
    border-radius: var(--radius-lg);
    padding: 20px;
    text-align: center;
    transition: all 0.3s ease;
}

.stat-card:hover {
    background: rgba(15, 23, 42, 0.8);
    border-color: rgba(148, 163, 184, 0.3);
    transform: translateY(-2px);
}

.stat-card .stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-light);
    display: block;
    margin-bottom: 8px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.stat-card .stat-label {
    color: var(--text-secondary);
    font-size: 0.875rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Enhanced File Input */
.enhanced-file-input {
    position: relative;
    display: inline-block;
}

.enhanced-file-input input[type="file"] {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.file-input-label {
    display: inline-block;
    padding: 12px 24px;
    background: var(--gradient-primary);
    color: var(--white);
    border-radius: var(--radius-lg);
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
}

.file-input-label:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* Responsive Design for New Components */
@media (max-width: 768px) {
    .section-header {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
    }

    .section-actions {
        align-self: stretch;
    }

    .breadcrumb {
        flex-wrap: wrap;
        gap: 4px;
    }

    .uploaded-file {
        flex-direction: column;
        align-items: stretch;
        text-align: center;
    }

    .file-actions {
        justify-content: center;
        margin-top: 12px;
    }

    .chart-content {
        flex-direction: column;
        gap: 16px;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 12px;
    }
}

/* PWA Install Prompt */
.pwa-install-prompt {
    position: fixed;
    bottom: 20px;
    left: 20px;
    right: 20px;
    background: rgba(30, 41, 59, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(148, 163, 184, 0.3);
    border-radius: var(--radius-xl);
    padding: 20px;
    box-shadow: var(--shadow-xl);
    z-index: 9999;
    transform: translateY(100%);
    transition: transform 0.3s ease;
}

.pwa-install-prompt.show {
    transform: translateY(0);
}

.pwa-install-content {
    display: flex;
    align-items: center;
    gap: 16px;
    color: var(--text-primary);
}

.pwa-install-icon {
    font-size: 2rem;
    flex-shrink: 0;
}

.pwa-install-text {
    flex: 1;
}

.pwa-install-title {
    font-weight: 600;
    margin-bottom: 4px;
}

.pwa-install-description {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.pwa-install-actions {
    display: flex;
    gap: 8px;
    flex-shrink: 0;
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --text-primary: #ffffff;
        --text-secondary: #e2e8f0;
        --text-muted: #cbd5e1;
    }

    .card, .info-card, .stat-card {
        border-width: 2px;
    }

    .btn {
        border: 2px solid transparent;
    }

    .btn:focus-visible {
        border-color: var(--white);
    }
}
