<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🛡️ HIMAYA BLOCKCHAIN - BRAND NEW INTERFACE</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 50%, #1a1a1a 100%);
            color: #e5e5e5;
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .header {
            text-align: center;
            padding: 3rem 0;
            position: relative;
        }

        .logo {
            font-size: 4rem;
            margin-bottom: 1.5rem;
            color: #8b5cf6;
            transition: transform 0.3s ease;
        }

        .logo:hover {
            transform: scale(1.05);
        }

        .title {
            font-size: 3.5rem;
            font-weight: 700;
            color: #f8f9fa;
            margin-bottom: 1rem;
            letter-spacing: -1px;
        }

        .subtitle {
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
            color: #8b5cf6;
            font-weight: 500;
        }

        .tagline {
            font-size: 1.1rem;
            margin-bottom: 3rem;
            color: #9ca3af;
            font-weight: 400;
        }

        .nav {
            display: flex;
            justify-content: center;
            gap: 2rem;
            margin-bottom: 4rem;
            flex-wrap: wrap;
        }

        .nav-btn {
            background: #374151;
            border: 2px solid #4b5563;
            border-radius: 12px;
            padding: 1rem 2rem;
            color: #e5e7eb;
            font-weight: 600;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.2s ease;
            text-transform: none;
            letter-spacing: 0.5px;
        }

        .nav-btn:hover {
            background: #4b5563;
            border-color: #8b5cf6;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(139, 92, 246, 0.2);
        }

        .nav-btn.active {
            background: #8b5cf6;
            border-color: #8b5cf6;
            color: white;
        }

        .status-bar {
            background: #2d3748;
            border: 1px solid #4a5568;
            border-radius: 16px;
            padding: 1.5rem;
            margin-bottom: 3rem;
            display: flex;
            justify-content: space-around;
            flex-wrap: wrap;
            gap: 2rem;
        }

        .status-item {
            text-align: center;
            font-weight: 500;
            font-size: 0.9rem;
            color: #cbd5e0;
        }

        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #ef4444;
            margin: 0 auto 0.5rem;
            transition: all 0.3s ease;
        }

        .status-dot.connected {
            background: #10b981;
        }

        .card {
            background: #2d3748;
            border: 1px solid #4a5568;
            border-radius: 16px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        }

        .card-title {
            font-size: 1.8rem;
            font-weight: 600;
            margin-bottom: 1.5rem;
            color: #f7fafc;
            text-align: center;
            text-transform: none;
            letter-spacing: normal;
        }

        .btn {
            background: #8b5cf6;
            border: 1px solid #8b5cf6;
            border-radius: 8px;
            padding: 0.75rem 1.5rem;
            color: white;
            font-weight: 500;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.2s ease;
            text-transform: none;
            letter-spacing: normal;
            margin: 0.5rem;
        }

        .btn:hover {
            background: #7c3aed;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
        }

        .form-input {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #4a5568;
            border-radius: 8px;
            background: #374151;
            color: #e5e7eb;
            font-size: 0.9rem;
            font-weight: normal;
            margin-bottom: 1rem;
            transition: all 0.2s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: #8b5cf6;
            box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
            background: #4a5568;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
            animation: fadeIn 0.3s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .notification {
            position: fixed;
            top: 2rem;
            right: 2rem;
            background: #374151;
            color: #e5e7eb;
            padding: 1rem 1.5rem;
            border-radius: 8px;
            font-weight: 500;
            font-size: 0.9rem;
            z-index: 1000;
            animation: slideInRight 0.3s ease;
            border: 1px solid #8b5cf6;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
        }

        @keyframes slideInRight {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        .hidden { display: none !important; }

        @media (max-width: 768px) {
            .title { font-size: 3rem; }
            .subtitle { font-size: 1.5rem; }
            .nav-btn { padding: 1rem 2rem; font-size: 1rem; }
            .card { padding: 2rem; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">🛡️</div>
            <h1 class="title">Himaya Blockchain</h1>
            <p class="subtitle">🇲🇦 Assurance Véhicule Moderne</p>
            <p class="tagline">Solution blockchain pour l'assurance automobile au Maroc</p>
        </div>

        <nav class="nav">
            <button class="nav-btn active" data-tab="connection">🔗 Wallet</button>
            <button class="nav-btn" data-tab="insurance">🚗 Assurance</button>
            <button class="nav-btn" data-tab="dashboard">📊 Dashboard</button>
            <button class="nav-btn" data-tab="converter">💱 Convertisseur</button>
        </nav>

        <div class="status-bar">
            <div class="status-item">
                <div class="status-dot" id="walletStatus"></div>
                <div id="walletText">Wallet: Déconnecté</div>
            </div>
            <div class="status-item">
                <div class="status-dot" id="networkStatus"></div>
                <div id="networkText">Réseau: Offline</div>
            </div>
            <div class="status-item">
                <div class="status-dot" id="contractStatus"></div>
                <div id="contractText">Contrats: Inactifs</div>
            </div>
        </div>

        <main>
            <!-- Connection Tab -->
            <section id="connection" class="tab-content active">
                <div class="card">
                    <h2 class="card-title">🔗 Connexion MetaMask</h2>
                    <div style="text-align: center; margin-bottom: 2rem;">
                        <button class="btn" id="connectWalletBtn">🦊 Connecter MetaMask</button>
                        <button class="btn" id="addNetworkBtn">⚙️ Ajouter Réseau Himaya</button>
                    </div>
                    <div class="grid">
                        <div>
                            <h3 style="color: #8b5cf6; margin-bottom: 1rem; font-size: 1.1rem;">👤 Informations Compte</h3>
                            <div id="accountAddress" class="form-input">Non connecté</div>
                            <div id="accountBalance" class="form-input">0.0000 ETH</div>
                        </div>
                        <div>
                            <h3 style="color: #8b5cf6; margin-bottom: 1rem; font-size: 1.1rem;">⛓️ État Blockchain</h3>
                            <div id="currentBlock" class="form-input">Bloc: 0</div>
                            <div id="gasPrice" class="form-input">Gas: 0 gwei</div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Insurance Tab -->
            <section id="insurance" class="tab-content">
                <div class="card">
                    <h2 class="card-title">🚗 Assurance Véhicule</h2>
                    <div class="grid">
                        <div>
                            <select id="vehicleType" class="form-input">
                                <option value="">Type de véhicule</option>
                                <option value="car">🚗 Voiture</option>
                                <option value="motorcycle">🏍️ Moto</option>
                                <option value="truck">🚚 Camion</option>
                            </select>
                            <input type="text" id="vehicleVin" class="form-input" placeholder="Immatriculation">
                            <input type="text" id="vehicleMake" class="form-input" placeholder="Marque">
                        </div>
                        <div>
                            <input type="text" id="vehicleModel" class="form-input" placeholder="Modèle">
                            <input type="number" id="vehicleYear" class="form-input" placeholder="Année">
                            <select id="vehicleCity" class="form-input">
                                <option value="">Ville</option>
                                <option value="casablanca">🏙️ Casablanca</option>
                                <option value="rabat">🏛️ Rabat</option>
                                <option value="marrakech">🕌 Marrakech</option>
                            </select>
                        </div>
                    </div>
                    <div style="text-align: center; margin-top: 2rem;">
                        <button class="btn" id="registerVehicleBtn">📝 Enregistrer Véhicule</button>
                        <button class="btn" id="buyInsuranceBtn">💰 Souscrire Assurance</button>
                    </div>
                </div>
            </section>

            <!-- Dashboard Tab -->
            <section id="dashboard" class="tab-content">
                <div class="card">
                    <h2 class="card-title">📊 Dashboard Analytics</h2>
                    <div style="text-align: center; margin-bottom: 2rem;">
                        <button class="btn role-btn active" data-role="client">👤 Client</button>
                        <button class="btn role-btn" data-role="insurer">🏢 Assureur</button>
                        <button class="btn role-btn" data-role="admin">⚙️ Admin</button>
                    </div>
                    <div class="grid">
                        <div style="text-align: center; background: rgba(139, 92, 246, 0.1); padding: 1.5rem; border-radius: 12px; border: 1px solid rgba(139, 92, 246, 0.2);">
                            <div style="font-size: 2rem; margin-bottom: 0.5rem;">📋</div>
                            <div style="font-size: 1.5rem; font-weight: 600; color: #8b5cf6;" id="totalPolicies">1,247</div>
                            <div style="color: #9ca3af; font-size: 0.9rem;">Polices Actives</div>
                        </div>
                        <div style="text-align: center; background: rgba(16, 185, 129, 0.1); padding: 1.5rem; border-radius: 12px; border: 1px solid rgba(16, 185, 129, 0.2);">
                            <div style="font-size: 2rem; margin-bottom: 0.5rem;">💰</div>
                            <div style="font-size: 1.5rem; font-weight: 600; color: #10b981;" id="totalPremiums">2.85M MAD</div>
                            <div style="color: #9ca3af; font-size: 0.9rem;">Primes Collectées</div>
                        </div>
                        <div style="text-align: center; background: rgba(239, 68, 68, 0.1); padding: 1.5rem; border-radius: 12px; border: 1px solid rgba(239, 68, 68, 0.2);">
                            <div style="font-size: 2rem; margin-bottom: 0.5rem;">🔍</div>
                            <div style="font-size: 1.5rem; font-weight: 600; color: #ef4444;" id="totalClaims">23</div>
                            <div style="color: #9ca3af; font-size: 0.9rem;">Réclamations</div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Converter Tab -->
            <section id="converter" class="tab-content">
                <div class="card">
                    <h2 class="card-title">💱 Convertisseur ETH ⇄ MAD</h2>
                    <div class="grid">
                        <div>
                            <h3 style="color: #8b5cf6; margin-bottom: 1rem; font-size: 1.1rem;">Ethereum (ETH)</h3>
                            <input type="number" id="ethAmount" class="form-input" placeholder="0.000000" step="0.000001">
                        </div>
                        <div>
                            <h3 style="color: #8b5cf6; margin-bottom: 1rem; font-size: 1.1rem;">Dirham Marocain (MAD)</h3>
                            <input type="number" id="madAmount" class="form-input" placeholder="0.00" step="0.01">
                        </div>
                    </div>
                    <div style="text-align: center; margin: 2rem 0;">
                        <div style="font-size: 2rem; margin-bottom: 1rem; color: #8b5cf6;">⇄</div>
                        <div style="font-size: 1.3rem; font-weight: 600; color: #e5e7eb;" id="exchangeRate">1 ETH = 35,000.00 MAD</div>
                        <div style="color: #9ca3af; font-size: 0.9rem;" id="lastUpdate">Dernière mise à jour: maintenant</div>
                    </div>
                    <div style="text-align: center;">
                        <button class="btn" id="refreshRatesBtn">🔄 Actualiser Taux</button>
                        <button class="btn" id="swapCurrenciesBtn">⇄ Inverser</button>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/web3@4.2.0/dist/web3.min.js"></script>
    <script>
        // Application State
        const HimayaApp = {
            web3: null,
            account: null,
            connected: false,
            currentTab: 'connection',
            exchangeRate: 35000
        };

        // Utility Functions
        function showNotification(message, type = 'info') {
            const existing = document.querySelectorAll('.notification');
            existing.forEach(n => n.remove());

            const notification = document.createElement('div');
            notification.className = 'notification';
            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 4000);
        }

        function updateStatus(element, connected, text) {
            const statusDot = document.getElementById(element);
            const statusText = document.getElementById(element.replace('Status', 'Text'));

            if (statusDot) {
                statusDot.className = `status-dot ${connected ? 'connected' : ''}`;
            }
            if (statusText) {
                statusText.textContent = text;
            }
        }

        // Tab Management
        function initTabs() {
            const navItems = document.querySelectorAll('.nav-btn');
            const tabContents = document.querySelectorAll('.tab-content');

            navItems.forEach(item => {
                item.addEventListener('click', (e) => {
                    const tabId = item.getAttribute('data-tab');

                    navItems.forEach(nav => nav.classList.remove('active'));
                    item.classList.add('active');

                    tabContents.forEach(tab => {
                        tab.classList.remove('active');
                    });

                    const targetTab = document.getElementById(tabId);
                    if (targetTab) {
                        targetTab.classList.add('active');
                        HimayaApp.currentTab = tabId;
                    }
                });
            });
        }

        // Wallet Connection
        async function connectWallet() {
            if (!window.ethereum) {
                showNotification('❌ MetaMask non détecté');
                return;
            }

            try {
                showNotification('🔄 Connexion en cours...');

                const accounts = await window.ethereum.request({
                    method: 'eth_requestAccounts'
                });

                if (accounts.length > 0) {
                    HimayaApp.account = accounts[0];
                    HimayaApp.connected = true;
                    HimayaApp.web3 = new Web3(window.ethereum);

                    const balance = await HimayaApp.web3.eth.getBalance(HimayaApp.account);
                    const ethBalance = HimayaApp.web3.utils.fromWei(balance, 'ether');

                    document.getElementById('accountAddress').textContent =
                        `${HimayaApp.account.slice(0, 6)}...${HimayaApp.account.slice(-4)}`;
                    document.getElementById('accountBalance').textContent =
                        `${parseFloat(ethBalance).toFixed(4)} ETH`;

                    updateStatus('walletStatus', true, 'Wallet: Connecté ✅');
                    updateStatus('networkStatus', true, 'Réseau: Connecté ✅');

                    document.getElementById('connectWalletBtn').textContent = '✅ Connecté';
                    showNotification('🎉 Wallet connecté avec succès');

                    await loadBlockchainInfo();
                }
            } catch (error) {
                showNotification('❌ Échec de la connexion');
            }
        }

        // Load Blockchain Info
        async function loadBlockchainInfo() {
            if (!HimayaApp.web3) return;

            try {
                const latestBlock = await HimayaApp.web3.eth.getBlockNumber();
                const gasPrice = await HimayaApp.web3.eth.getGasPrice();
                const gasPriceGwei = HimayaApp.web3.utils.fromWei(gasPrice, 'gwei');

                document.getElementById('currentBlock').textContent = `Bloc: ${latestBlock}`;
                document.getElementById('gasPrice').textContent = `Gas: ${parseFloat(gasPriceGwei).toFixed(2)} gwei`;

                updateStatus('contractStatus', true, 'Contrats: Actifs ✅');
            } catch (error) {
                console.error('Failed to load blockchain info:', error);
            }
        }

        // Vehicle Registration
        async function registerVehicle() {
            if (!HimayaApp.connected) {
                showNotification('⚠️ CONNECTEZ VOTRE WALLET D\'ABORD!');
                return;
            }

            const vehicleData = {
                type: document.getElementById('vehicleType').value,
                vin: document.getElementById('vehicleVin').value,
                make: document.getElementById('vehicleMake').value,
                model: document.getElementById('vehicleModel').value,
                year: document.getElementById('vehicleYear').value,
                city: document.getElementById('vehicleCity').value
            };

            if (!vehicleData.type || !vehicleData.vin || !vehicleData.make) {
                showNotification('⚠️ Veuillez remplir tous les champs');
                return;
            }

            showNotification('🔄 Enregistrement du véhicule...');

            setTimeout(() => {
                showNotification('🎉 Véhicule enregistré avec succès');

                // Clear form
                document.getElementById('vehicleType').value = '';
                document.getElementById('vehicleVin').value = '';
                document.getElementById('vehicleMake').value = '';
                document.getElementById('vehicleModel').value = '';
                document.getElementById('vehicleYear').value = '';
                document.getElementById('vehicleCity').value = '';
            }, 2000);
        }

        // Currency Conversion
        function convertETHToMAD(ethValue) {
            if (!ethValue || ethValue <= 0) {
                document.getElementById('madAmount').value = '';
                return;
            }
            const madValue = parseFloat(ethValue) * HimayaApp.exchangeRate;
            document.getElementById('madAmount').value = madValue.toFixed(2);
        }

        function convertMADToETH(madValue) {
            if (!madValue || madValue <= 0) {
                document.getElementById('ethAmount').value = '';
                return;
            }
            const ethValue = parseFloat(madValue) / HimayaApp.exchangeRate;
            document.getElementById('ethAmount').value = ethValue.toFixed(6);
        }

        function refreshExchangeRates() {
            showNotification('🔄 Actualisation des taux...');

            setTimeout(() => {
                const variation = (Math.random() - 0.5) * 2000;
                HimayaApp.exchangeRate = 35000 + variation;

                document.getElementById('exchangeRate').textContent =
                    `1 ETH = ${HimayaApp.exchangeRate.toLocaleString('fr-FR', {minimumFractionDigits: 2})} MAD`;

                document.getElementById('lastUpdate').textContent =
                    `Dernière mise à jour: ${new Date().toLocaleTimeString('fr-FR')}`;

                showNotification('✅ Taux mis à jour');
            }, 1500);
        }

        function swapCurrencies() {
            const ethInput = document.getElementById('ethAmount');
            const madInput = document.getElementById('madAmount');

            const ethValue = ethInput.value;
            const madValue = madInput.value;

            ethInput.value = madValue ? (parseFloat(madValue) / HimayaApp.exchangeRate).toFixed(6) : '';
            madInput.value = ethValue ? (parseFloat(ethValue) * HimayaApp.exchangeRate).toFixed(2) : '';

            showNotification('🔄 Devises inversées');
        }

        // Role Management
        function switchRole(role) {
            document.querySelectorAll('.role-btn').forEach(btn => {
                btn.classList.remove('active');
                if (btn.getAttribute('data-role') === role) {
                    btn.classList.add('active');
                }
            });

            const stats = {
                client: { totalPolicies: '3', totalPremiums: '3.6 ETH', totalClaims: '1' },
                insurer: { totalPolicies: '1,247', totalPremiums: '2.85M MAD', totalClaims: '23' },
                admin: { totalPolicies: '5,432', totalPremiums: '12.3M MAD', totalClaims: '89' }
            };

            const roleStats = stats[role];
            if (roleStats) {
                document.getElementById('totalPolicies').textContent = roleStats.totalPolicies;
                document.getElementById('totalPremiums').textContent = roleStats.totalPremiums;
                document.getElementById('totalClaims').textContent = roleStats.totalClaims;
            }

            showNotification(`🔄 Rôle changé: ${role}`);
        }

        // Event Listeners
        function initEventListeners() {
            document.getElementById('connectWalletBtn').addEventListener('click', connectWallet);
            document.getElementById('registerVehicleBtn').addEventListener('click', registerVehicle);

            document.getElementById('ethAmount').addEventListener('input', (e) => {
                convertETHToMAD(e.target.value);
            });

            document.getElementById('madAmount').addEventListener('input', (e) => {
                convertMADToETH(e.target.value);
            });

            document.getElementById('refreshRatesBtn').addEventListener('click', refreshExchangeRates);
            document.getElementById('swapCurrenciesBtn').addEventListener('click', swapCurrencies);

            document.querySelectorAll('.role-btn').forEach(btn => {
                btn.addEventListener('click', () => {
                    const role = btn.getAttribute('data-role');
                    switchRole(role);
                });
            });
        }

        // Initialize Application
        function initApp() {
            console.log('🚀 HIMAYA BLOCKCHAIN - BRAND NEW INTERFACE LOADED!');

            initTabs();
            initEventListeners();

            setTimeout(() => {
                showNotification('🛡️ Bienvenue sur Himaya Blockchain');
            }, 1000);
        }

        // Initialize when DOM is loaded
        document.addEventListener('DOMContentLoaded', initApp);

        console.log('🛡️ HIMAYA BLOCKCHAIN - NOUVELLE INTERFACE RÉVOLUTIONNAIRE CHARGÉE!');
        console.log('🇲🇦 PRÊT POUR LE MARCHÉ MAROCAIN!');
    </script>
</body>
</html>
