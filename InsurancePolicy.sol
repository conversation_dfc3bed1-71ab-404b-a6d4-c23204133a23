// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

interface IVehicleRegistry {
    function validateOwnership(uint256 _vehicleId, address _owner) external view returns (bool);
}

contract InsurancePolicy {
    enum CoverageType { Basic, Standard, Premium }
    
    struct Policy {
        uint256 id;
        uint256 vehicleId;
        address policyholder;
        CoverageType coverageType;
        uint256 premiumAmount;
        uint256 coverageAmount;
        uint256 deductible;
        uint256 startDate;
        uint256 endDate;
        bool isActive;
        string policyNumber;
    }
    
    mapping(uint256 => Policy) public policies;
    mapping(address => uint256[]) public holderPolicies;
    mapping(uint256 => uint256) public vehiclePolicies; // vehicleId => policyId
    
    uint256 public nextPolicyId = 1;
    address public vehicleRegistry;
    address public insurerAddress;
    
    event PolicyCreated(
        uint256 indexed policyId,
        address indexed policyholder,
        uint256 indexed vehicleId,
        uint256 premiumAmount,
        string policyNumber
    );
    
    event PolicyRenewed(
        uint256 indexed policyId,
        uint256 newEndDate,
        uint256 premiumPaid
    );
    
    event PolicyCancelled(
        uint256 indexed policyId,
        uint256 refundAmount
    );
    
    modifier onlyInsurer() {
        require(msg.sender == insurerAddress, "Only insurer can call this");
        _;
    }
    
    modifier onlyPolicyholder(uint256 _policyId) {
        require(policies[_policyId].policyholder == msg.sender, "Not policy holder");
        _;
    }
    
    constructor(address _vehicleRegistry, address _insurerAddress) {
        vehicleRegistry = _vehicleRegistry;
        insurerAddress = _insurerAddress;
    }
    
    function createPolicy(
        uint256 _vehicleId,
        address _policyholder,
        CoverageType _coverageType,
        uint256 _premiumAmount,
        uint256 _coverageAmount,
        uint256 _deductible,
        uint256 _durationInDays,
        string memory _policyNumber
    ) external payable onlyInsurer returns (uint256) {
        require(msg.value == _premiumAmount, "Incorrect premium amount");
        require(_policyholder != address(0), "Invalid policyholder");
        require(vehiclePolicies[_vehicleId] == 0, "Vehicle already insured");
        
        // Validate vehicle ownership
        require(
            IVehicleRegistry(vehicleRegistry).validateOwnership(_vehicleId, _policyholder),
            "Invalid vehicle ownership"
        );
        
        uint256 policyId = nextPolicyId++;
        
        policies[policyId] = Policy({
            id: policyId,
            vehicleId: _vehicleId,
            policyholder: _policyholder,
            coverageType: _coverageType,
            premiumAmount: _premiumAmount,
            coverageAmount: _coverageAmount,
            deductible: _deductible,
            startDate: block.timestamp,
            endDate: block.timestamp + (_durationInDays * 1 days),
            isActive: true,
            policyNumber: _policyNumber
        });
        
        holderPolicies[_policyholder].push(policyId);
        vehiclePolicies[_vehicleId] = policyId;
        
        emit PolicyCreated(policyId, _policyholder, _vehicleId, _premiumAmount, _policyNumber);
        return policyId;
    }
    
    function renewPolicy(uint256 _policyId) 
        external 
        payable 
        onlyPolicyholder(_policyId) 
    {
        Policy storage policy = policies[_policyId];
        require(policy.isActive, "Policy not active");
        require(msg.value == policy.premiumAmount, "Incorrect premium amount");
        
        // Extend policy by 30 days
        policy.endDate = block.timestamp + 30 days;
        
        emit PolicyRenewed(_policyId, policy.endDate, msg.value);
    }
    
    function cancelPolicy(uint256 _policyId) 
        external 
        onlyPolicyholder(_policyId) 
    {
        Policy storage policy = policies[_policyId];
        require(policy.isActive, "Policy already inactive");
        require(block.timestamp < policy.endDate, "Policy already expired");
        
        policy.isActive = false;
        vehiclePolicies[policy.vehicleId] = 0;
        
        // Calculate refund (simplified: 50% if more than half time remaining)
        uint256 remainingTime = policy.endDate - block.timestamp;
        uint256 totalTime = policy.endDate - policy.startDate;
        uint256 refund = 0;
        
        if (remainingTime > totalTime / 2) {
            refund = policy.premiumAmount / 2;
            payable(policy.policyholder).transfer(refund);
        }
        
        emit PolicyCancelled(_policyId, refund);
    }
    
    function getPolicy(uint256 _policyId) 
        external 
        view 
        returns (
            uint256 id,
            uint256 vehicleId,
            address policyholder,
            CoverageType coverageType,
            uint256 premiumAmount,
            uint256 coverageAmount,
            uint256 deductible,
            uint256 startDate,
            uint256 endDate,
            bool isActive,
            string memory policyNumber
        ) 
    {
        Policy memory policy = policies[_policyId];
        return (
            policy.id,
            policy.vehicleId,
            policy.policyholder,
            policy.coverageType,
            policy.premiumAmount,
            policy.coverageAmount,
            policy.deductible,
            policy.startDate,
            policy.endDate,
            policy.isActive,
            policy.policyNumber
        );
    }
    
    function getHolderPolicies(address _holder) 
        external 
        view 
        returns (uint256[] memory) 
    {
        return holderPolicies[_holder];
    }
    
    function isPolicyActive(uint256 _policyId) 
        external 
        view 
        returns (bool) 
    {
        Policy memory policy = policies[_policyId];
        return policy.isActive && block.timestamp <= policy.endDate;
    }
    
    function getVehiclePolicy(uint256 _vehicleId) 
        external 
        view 
        returns (uint256) 
    {
        return vehiclePolicies[_vehicleId];
    }
    
    // Emergency function to withdraw contract balance (only insurer)
    function withdrawBalance() external onlyInsurer {
        payable(insurerAddress).transfer(address(this).balance);
    }
}
