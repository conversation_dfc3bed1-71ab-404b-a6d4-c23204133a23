/**
 * Modern Material Design System for Himaya Blockchain Insurance
 * Purple/Violet theme with Moroccan cultural elements
 */

/* Modern Color Palette */
:root {
    /* Primary Purple/Violet Palette */
    --primary-50: #faf5ff;
    --primary-100: #f3e8ff;
    --primary-200: #e9d5ff;
    --primary-300: #d8b4fe;
    --primary-400: #c084fc;
    --primary-500: #a855f7;
    --primary-600: #9333ea;
    --primary-700: #7c3aed;
    --primary-800: #6b21a8;
    --primary-900: #581c87;
    
    /* Accent Colors */
    --accent-blue: #3b82f6;
    --accent-teal: #14b8a6;
    --accent-emerald: #10b981;
    --accent-orange: #f97316;
    
    /* Neutral Palette */
    --gray-50: #fafafa;
    --gray-100: #f5f5f5;
    --gray-200: #e5e5e5;
    --gray-300: #d4d4d4;
    --gray-400: #a3a3a3;
    --gray-500: #737373;
    --gray-600: #525252;
    --gray-700: #404040;
    --gray-800: #262626;
    --gray-900: #171717;
    
    /* Semantic Colors */
    --success: #22c55e;
    --warning: #f59e0b;
    --error: #ef4444;
    --info: var(--accent-blue);
    
    /* Background Colors */
    --bg-primary: #ffffff;
    --bg-secondary: #fafafa;
    --bg-tertiary: #f5f5f5;
    --bg-dark: #0f0f0f;
    --bg-card: #ffffff;
    
    /* Text Colors */
    --text-primary: #171717;
    --text-secondary: #525252;
    --text-muted: #a3a3a3;
    --text-inverse: #ffffff;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
    
    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    --radius-3xl: 2rem;
    
    /* Spacing */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 4rem;
    --space-20: 5rem;
    
    /* Transitions */
    --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--primary-50) 100%);
    color: var(--text-primary);
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    min-height: 100vh;
}

/* Modern Container */
.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: var(--space-6);
}

/* Modern Header */
.header {
    background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
    color: var(--text-inverse);
    padding: var(--space-12) var(--space-8);
    border-radius: var(--radius-3xl);
    margin-bottom: var(--space-8);
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-xl);
}

.header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="headerPattern" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23headerPattern)"/></svg>');
    opacity: 0.3;
}

.header-content {
    position: relative;
    z-index: 1;
    text-align: center;
}

.header h1 {
    font-size: clamp(2rem, 5vw, 3.5rem);
    font-weight: 800;
    margin-bottom: var(--space-4);
    background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.header .subtitle {
    font-size: clamp(1rem, 2.5vw, 1.25rem);
    opacity: 0.9;
    max-width: 800px;
    margin: 0 auto var(--space-6);
}

.header .tagline {
    font-size: 0.875rem;
    opacity: 0.8;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.1em;
}

/* Language Selector */
.language-selector {
    position: absolute;
    top: var(--space-6);
    right: var(--space-6);
    z-index: 2;
}

.language-dropdown {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-lg);
    padding: var(--space-2) var(--space-3);
    color: var(--text-inverse);
    font-size: 0.875rem;
    cursor: pointer;
    transition: var(--transition-fast);
}

.language-dropdown:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* Modern Cards */
.card {
    background: var(--bg-card);
    border-radius: var(--radius-2xl);
    padding: var(--space-8);
    margin-bottom: var(--space-6);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-500) 0%, var(--accent-blue) 100%);
}

/* Modern Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
    padding: var(--space-3) var(--space-6);
    border: none;
    border-radius: var(--radius-lg);
    font-size: 0.875rem;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition-fast);
    position: relative;
    overflow: hidden;
    min-height: 44px; /* Touch target */
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
    color: var(--text-inverse);
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-700) 0%, var(--primary-800) 100%);
    box-shadow: var(--shadow-lg);
    transform: translateY(-1px);
}

.btn-secondary {
    background: var(--gray-100);
    color: var(--text-primary);
    border: 1px solid var(--gray-300);
}

.btn-secondary:hover {
    background: var(--gray-200);
    border-color: var(--gray-400);
}

.btn-accent {
    background: linear-gradient(135deg, var(--accent-blue) 0%, var(--accent-teal) 100%);
    color: var(--text-inverse);
}

.btn-accent:hover {
    background: linear-gradient(135deg, var(--accent-teal) 0%, var(--accent-emerald) 100%);
}

.btn-outline {
    background: transparent;
    color: var(--primary-600);
    border: 2px solid var(--primary-600);
}

.btn-outline:hover {
    background: var(--primary-600);
    color: var(--text-inverse);
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
}

/* Button Groups */
.btn-group {
    display: flex;
    gap: var(--space-3);
    flex-wrap: wrap;
}

/* Modern Form Elements */
.form-group {
    margin-bottom: var(--space-6);
}

.form-label {
    display: block;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--space-2);
    font-size: 0.875rem;
}

.form-input {
    width: 100%;
    padding: var(--space-4);
    border: 2px solid var(--gray-300);
    border-radius: var(--radius-lg);
    font-size: 1rem;
    transition: var(--transition-fast);
    background: var(--bg-primary);
}

.form-input:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(168, 85, 247, 0.1);
}

.form-input::placeholder {
    color: var(--text-muted);
}

/* Modern Status Bar */
.status-bar {
    background: var(--bg-card);
    border-radius: var(--radius-xl);
    padding: var(--space-4) var(--space-6);
    margin-bottom: var(--space-6);
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: var(--space-4);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
}

.status-item {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    font-size: 0.875rem;
    font-weight: 500;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--gray-400);
    transition: var(--transition-fast);
}

.status-dot.connected {
    background: var(--success);
    box-shadow: 0 0 0 2px rgba(34, 197, 94, 0.2);
}

.status-dot.error {
    background: var(--error);
    box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);
}

.status-dot.warning {
    background: var(--warning);
    box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.2);
}

/* Modern Navigation */
.nav-tabs {
    display: flex;
    background: var(--bg-secondary);
    border-radius: var(--radius-xl);
    padding: var(--space-1);
    margin-bottom: var(--space-6);
    overflow-x: auto;
}

.nav-tab {
    flex: 1;
    padding: var(--space-3) var(--space-4);
    border: none;
    background: transparent;
    color: var(--text-secondary);
    font-weight: 500;
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: var(--transition-fast);
    white-space: nowrap;
}

.nav-tab.active {
    background: var(--bg-primary);
    color: var(--primary-600);
    box-shadow: var(--shadow-sm);
}

.nav-tab:hover:not(.active) {
    color: var(--text-primary);
    background: rgba(0, 0, 0, 0.05);
}

/* Modern Grid System */
.grid {
    display: grid;
    gap: var(--space-6);
}

.grid-cols-1 { grid-template-columns: 1fr; }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }

@media (max-width: 768px) {
    .grid-cols-2,
    .grid-cols-3,
    .grid-cols-4 {
        grid-template-columns: 1fr;
    }
}

/* Modern Spacing Utilities */
.mb-4 { margin-bottom: var(--space-4); }
.mb-6 { margin-bottom: var(--space-6); }
.mb-8 { margin-bottom: var(--space-8); }
.mt-4 { margin-top: var(--space-4); }
.mt-6 { margin-top: var(--space-6); }
.mt-8 { margin-top: var(--space-8); }

.p-4 { padding: var(--space-4); }
.p-6 { padding: var(--space-6); }
.p-8 { padding: var(--space-8); }

/* Modern Typography */
.text-sm { font-size: 0.875rem; }
.text-base { font-size: 1rem; }
.text-lg { font-size: 1.125rem; }
.text-xl { font-size: 1.25rem; }
.text-2xl { font-size: 1.5rem; }
.text-3xl { font-size: 1.875rem; }

.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

.text-center { text-align: center; }
.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-muted { color: var(--text-muted); }

/* Modern Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.animate-fade-in {
    animation: fadeIn 0.5s ease-out;
}

.animate-slide-in {
    animation: slideIn 0.3s ease-out;
}

.animate-pulse {
    animation: pulse 2s infinite;
}

/* Responsive Design */
@media (max-width: 640px) {
    .container {
        padding: var(--space-4);
    }
    
    .header {
        padding: var(--space-8) var(--space-4);
    }
    
    .card {
        padding: var(--space-6);
    }
    
    .btn-group {
        flex-direction: column;
    }
    
    .status-bar {
        flex-direction: column;
        align-items: stretch;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-primary: #0f0f0f;
        --bg-secondary: #171717;
        --bg-tertiary: #262626;
        --bg-card: #171717;
        --text-primary: #fafafa;
        --text-secondary: #d4d4d4;
        --text-muted: #737373;
    }
    
    .card {
        border-color: #404040;
    }

    .form-input {
        background: var(--bg-secondary);
        border-color: #404040;
        color: var(--text-primary);
    }
}

/* Additional Modern Components */
.stat-card {
    background: var(--bg-card);
    border-radius: var(--radius-xl);
    padding: var(--space-6);
    text-align: center;
    border: 1px solid var(--gray-200);
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.stat-card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-500) 0%, var(--accent-blue) 100%);
}

.stat-icon {
    font-size: 2rem;
    margin-bottom: var(--space-2);
    display: block;
}

.stat-value {
    font-size: 2rem;
    font-weight: 800;
    color: var(--primary-600);
    margin-bottom: var(--space-1);
}

.stat-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.stat-change {
    font-size: 0.75rem;
    font-weight: 600;
    margin-top: var(--space-1);
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius-md);
}

.stat-change.positive {
    background: rgba(34, 197, 94, 0.1);
    color: var(--success);
}

.stat-change.negative {
    background: rgba(239, 68, 68, 0.1);
    color: var(--error);
}

.stat-change.neutral {
    background: rgba(115, 115, 115, 0.1);
    color: var(--text-muted);
}

/* Role Indicator */
.role-indicator {
    display: inline-flex;
    align-items: center;
    padding: var(--space-2) var(--space-4);
    border-radius: var(--radius-lg);
    font-size: 0.75rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    margin-top: var(--space-4);
    border: 2px solid rgba(255, 255, 255, 0.3);
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.role-indicator.role-client {
    background: rgba(59, 130, 246, 0.2);
    border-color: rgba(59, 130, 246, 0.3);
    color: #3b82f6;
}

.role-indicator.role-insurer {
    background: rgba(168, 85, 247, 0.2);
    border-color: rgba(168, 85, 247, 0.3);
    color: #a855f7;
}

.role-indicator.role-admin {
    background: rgba(239, 68, 68, 0.2);
    border-color: rgba(239, 68, 68, 0.3);
    color: #ef4444;
}

/* Dashboard Tabs */
.dashboard-tab {
    animation: fadeIn 0.3s ease-out;
}

/* Utility Classes */
.space-y-2 > * + * {
    margin-top: var(--space-2);
}

.space-y-4 > * + * {
    margin-top: var(--space-4);
}

.flex {
    display: flex;
}

.justify-between {
    justify-content: space-between;
}

.items-center {
    align-items: center;
}

.hidden {
    display: none;
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--gray-300);
    border-top-color: var(--primary-500);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Enhanced Hover Effects */
.card:hover::before {
    height: 6px;
}

.btn:hover {
    transform: translateY(-1px);
}

.btn:active {
    transform: translateY(0);
}

/* Focus States */
.btn:focus-visible {
    outline: 2px solid var(--primary-500);
    outline-offset: 2px;
}

/* Disabled States */
.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
}

/* Success/Error States */
.success {
    border-color: var(--success);
    background: rgba(34, 197, 94, 0.05);
}

.error {
    border-color: var(--error);
    background: rgba(239, 68, 68, 0.05);
}

/* Modern Scrollbar */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--gray-100);
    border-radius: var(--radius-md);
}

::-webkit-scrollbar-thumb {
    background: var(--gray-400);
    border-radius: var(--radius-md);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--gray-500);
}
