<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🏢 <PERSON><PERSON> - Assureur</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 50%, #1a1a1a 100%);
            color: #e5e5e5;
            min-height: 100vh;
            padding: 2rem;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid #10b981;
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .header-info h1 {
            font-size: 2.5rem;
            color: #10b981;
            margin-bottom: 0.5rem;
        }
        
        .header-info p {
            color: #9ca3af;
            font-size: 1.1rem;
        }
        
        .card {
            background: #2d3748;
            border: 1px solid #4a5568;
            border-radius: 16px;
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .card-title {
            font-size: 1.8rem;
            font-weight: 600;
            margin-bottom: 1.5rem;
            color: #f7fafc;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid #10b981;
            border-radius: 12px;
            padding: 1.5rem;
            text-align: center;
        }
        
        .stat-value {
            font-size: 2.5rem;
            font-weight: 700;
            color: #10b981;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #9ca3af;
            font-size: 0.9rem;
        }
        
        .btn {
            background: #10b981;
            border: 1px solid #10b981;
            border-radius: 8px;
            padding: 0.75rem 1.5rem;
            color: white;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn:hover {
            background: #059669;
            transform: translateY(-1px);
        }
        
        .btn-danger {
            background: #ef4444;
            border-color: #ef4444;
        }
        
        .btn-danger:hover {
            background: #dc2626;
        }
        
        .btn-secondary {
            background: #374151;
            border-color: #4b5563;
        }
        
        .btn-secondary:hover {
            background: #4b5563;
        }
        
        .claim-item {
            background: #374151;
            padding: 1.5rem;
            border-radius: 12px;
            margin-bottom: 1rem;
            border: 1px solid #4a5568;
        }
        
        .form-input {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #4a5568;
            border-radius: 8px;
            background: #374151;
            color: #e5e7eb;
            margin-bottom: 1rem;
            transition: all 0.2s ease;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #10b981;
            box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
        }
        
        .notification {
            position: fixed;
            top: 2rem;
            right: 2rem;
            background: #374151;
            color: #e5e7eb;
            padding: 1rem 1.5rem;
            border-radius: 8px;
            border: 1px solid #10b981;
            z-index: 1000;
            animation: slideIn 0.3s ease;
        }
        
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        
        @media (max-width: 768px) {
            .header {
                flex-direction: column;
                gap: 1rem;
                text-align: center;
            }
            .stats-grid { grid-template-columns: 1fr; }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="header-info">
                <h1>🏢 Dashboard Assureur</h1>
                <p>Gestion des Réclamations & Analytics</p>
            </div>
            <div>
                <button class="btn btn-secondary" onclick="window.location.href='client-registration.html'">🔙 Accueil</button>
                <button class="btn" id="connectWalletBtn">🦊 Connecter Wallet</button>
            </div>
        </div>

        <!-- Analytics Dashboard -->
        <div class="card">
            <h2 class="card-title">📊 Analytics en Temps Réel</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value" id="totalPolicies">0</div>
                    <div class="stat-label">Polices Actives</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="totalPremiums">0.0000 ETH</div>
                    <div class="stat-label">Primes Collectées</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="pendingClaims">0</div>
                    <div class="stat-label">Réclamations en Attente</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="approvedClaims">0</div>
                    <div class="stat-label">Réclamations Approuvées</div>
                </div>
            </div>
        </div>

        <!-- Client Wallet Assignment -->
        <div class="card">
            <h2 class="card-title">👥 Assignation de Wallets Clients</h2>
            <div style="background: rgba(16, 185, 129, 0.1); border: 1px solid #10b981; border-radius: 8px; padding: 1rem; margin-bottom: 2rem;">
                <h3 style="color: #10b981; margin-bottom: 0.5rem;">🛡️ Système de Vérification des Clients</h3>
                <p style="color: #e5e7eb; margin: 0; font-size: 0.9rem;">
                    Les wallets sont assignés par l'assureur aux clients vérifiés. Cela garantit que seuls les vrais utilisateurs peuvent utiliser la plateforme.
                </p>
            </div>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem;">
                <div>
                    <input type="text" id="clientWalletAddress" class="form-input" placeholder="Adresse Wallet Client (0x...)">
                    <input type="text" id="clientName" class="form-input" placeholder="Nom complet du client">
                    <input type="text" id="clientId" class="form-input" placeholder="CIN / Passeport">
                </div>
                <div>
                    <input type="tel" id="clientPhone" class="form-input" placeholder="Numéro de téléphone">
                    <input type="email" id="clientEmail" class="form-input" placeholder="Email du client">
                    <button class="btn" onclick="assignClientWallet()">👤 Assigner Wallet Client</button>
                </div>
            </div>
            
            <!-- Assigned Wallets List -->
            <div style="margin-top: 2rem;">
                <h3 style="color: #10b981;">📋 Wallets Clients Assignés</h3>
                <div id="assignedWalletsList">
                    <p style="text-align: center; color: #9ca3af; padding: 1rem;">Chargement des wallets assignés...</p>
                </div>
            </div>
        </div>

        <!-- Claims Management -->
        <div class="card">
            <h2 class="card-title">🔍 Gestion des Réclamations</h2>
            <div id="claimsList">
                <p style="text-align: center; color: #9ca3af; padding: 2rem;">Chargement des réclamations...</p>
            </div>
        </div>

        <!-- Risk Analysis -->
        <div class="card">
            <h2 class="card-title">📈 Analyse des Risques</h2>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem;">
                <div style="background: rgba(245, 158, 11, 0.1); border: 1px solid #f59e0b; border-radius: 12px; padding: 1.5rem;">
                    <h3 style="color: #f59e0b; margin-bottom: 1rem;">⚠️ Alertes Risques</h3>
                    <div style="color: #e5e7eb; margin-bottom: 1rem;">
                        • Réclamations multiples: 2 clients<br>
                        • Montants élevés: 1 réclamation<br>
                        • Véhicules récents: 5 réclamations
                    </div>
                    <button class="btn" style="background: #f59e0b;">📊 Rapport Détaillé</button>
                </div>
                <div style="background: rgba(139, 92, 246, 0.1); border: 1px solid #8b5cf6; border-radius: 12px; padding: 1.5rem;">
                    <h3 style="color: #8b5cf6; margin-bottom: 1rem;">📊 Tendances</h3>
                    <div style="color: #e5e7eb; margin-bottom: 1rem;">
                        • Accidents: +15% ce mois<br>
                        • Vols: -5% ce mois<br>
                        • Satisfaction client: 94%
                    </div>
                    <button class="btn" style="background: #8b5cf6;">📈 Analytics Avancées</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Rejection Modal -->
    <div id="rejectModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.8); z-index: 1000; align-items: center; justify-content: center;">
        <div style="background: #2d3748; border-radius: 16px; padding: 2rem; max-width: 500px; width: 90%; border: 1px solid #10b981;">
            <h3 style="color: #10b981; margin-bottom: 1rem;">❌ Rejeter la Réclamation</h3>
            <p style="color: #e5e7eb; margin-bottom: 1rem;">Veuillez indiquer la raison du rejet:</p>
            <textarea id="rejectionReason" style="width: 100%; padding: 0.75rem; border: 1px solid #4a5568; border-radius: 8px; background: #374151; color: #e5e7eb; margin-bottom: 1rem; min-height: 100px;" placeholder="Raison du rejet..."></textarea>
            <div style="display: flex; gap: 1rem; justify-content: flex-end;">
                <button class="btn btn-secondary" onclick="closeRejectModal()">Annuler</button>
                <button class="btn btn-danger" onclick="confirmRejectClaim()">Confirmer le Rejet</button>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/web3@4.2.0/dist/web3.min.js"></script>
    <script>
        // Application State
        const InsurerApp = {
            web3: null,
            account: null,
            contract: null,
            connected: false,
            currentRejectClaimId: null
        };

        // Utility Functions
        function showNotification(message) {
            const existing = document.querySelectorAll('.notification');
            existing.forEach(n => n.remove());

            const notification = document.createElement('div');
            notification.className = 'notification';
            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 4000);
        }

        // Wallet Connection
        async function connectWallet() {
            if (!window.ethereum) {
                showNotification('❌ MetaMask non détecté. Veuillez installer MetaMask.');
                return;
            }

            try {
                showNotification('🔄 Connexion au wallet assureur...');
                
                const accounts = await window.ethereum.request({
                    method: 'eth_requestAccounts'
                });

                if (accounts.length > 0) {
                    InsurerApp.account = accounts[0];
                    InsurerApp.web3 = new Web3(window.ethereum);
                    InsurerApp.connected = true;

                    showNotification('🎉 Wallet assureur connecté avec succès!');
                    await loadInsurerData();
                }
            } catch (error) {
                console.error('Wallet connection failed:', error);
                showNotification('❌ Échec de la connexion au wallet');
            }
        }

        // Wallet Assignment Functions
        async function assignClientWallet() {
            if (!InsurerApp.connected) {
                showNotification('⚠️ Veuillez d\'abord connecter votre wallet');
                return;
            }

            const walletAddress = document.getElementById('clientWalletAddress').value.trim();
            const clientName = document.getElementById('clientName').value.trim();
            const clientId = document.getElementById('clientId').value.trim();
            const clientPhone = document.getElementById('clientPhone').value.trim();
            const clientEmail = document.getElementById('clientEmail').value.trim();

            if (!walletAddress || !clientName || !clientId) {
                showNotification('⚠️ Veuillez remplir tous les champs obligatoires');
                return;
            }

            if (!walletAddress.startsWith('0x') || walletAddress.length !== 42) {
                showNotification('⚠️ Format d\'adresse wallet invalide');
                return;
            }

            try {
                showNotification('🔄 Assignation du wallet client sur la blockchain...');
                
                // Simulate blockchain transaction
                setTimeout(() => {
                    // Clear form
                    document.getElementById('clientWalletAddress').value = '';
                    document.getElementById('clientName').value = '';
                    document.getElementById('clientId').value = '';
                    document.getElementById('clientPhone').value = '';
                    document.getElementById('clientEmail').value = '';

                    showNotification(`🎉 Wallet assigné avec succès à ${clientName}!`);
                    console.log('Wallet assignment:', {walletAddress, clientName, clientId});
                    
                    loadAssignedWallets();
                }, 2000);

            } catch (error) {
                console.error('Wallet assignment failed:', error);
                showNotification('❌ Échec de l\'assignation du wallet');
            }
        }

        function loadAssignedWallets() {
            const walletsList = document.getElementById('assignedWalletsList');

            // Load ONLY real assigned wallets from localStorage - NO MOCK DATA
            const wallets = [];

            // Check if current user has an approved verification with wallet
            const userVerification = localStorage.getItem('himayaRegistration');
            if (userVerification) {
                const userData = JSON.parse(userVerification);
                if (userData.status === 'approved' && userData.walletAddress) {
                    wallets.push({
                        walletAddress: userData.walletAddress,
                        clientName: userData.personalInfo.fullName,
                        clientId: userData.personalInfo.cinNumber,
                        phoneNumber: userData.personalInfo.phoneNumber,
                        email: userData.personalInfo.emailAddress,
                        assignedAt: new Date(userData.approvedAt),
                        isVerified: true
                    });
                }
            }

            if (wallets.length === 0) {
                walletsList.innerHTML = '<p style="text-align: center; color: #9ca3af; padding: 1rem;">Aucun wallet assigné</p>';
                return;
            }

            walletsList.innerHTML = wallets.map(wallet => `
                <div style="background: #374151; padding: 1rem; border-radius: 8px; margin-bottom: 1rem; border: 1px solid #10b981;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.5rem;">
                        <strong style="color: #10b981;">${wallet.clientName}</strong>
                        <span style="background: #10b981; color: white; padding: 0.25rem 0.75rem; border-radius: 12px; font-size: 0.8rem;">
                            ${wallet.isVerified ? 'Vérifié' : 'En attente'}
                        </span>
                    </div>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 0.5rem; color: #e5e7eb; font-size: 0.9rem;">
                        <div><strong>Wallet:</strong> ${wallet.walletAddress.slice(0, 6)}...${wallet.walletAddress.slice(-4)}</div>
                        <div><strong>CIN:</strong> ${wallet.clientId}</div>
                        <div><strong>Téléphone:</strong> ${wallet.phoneNumber}</div>
                        <div><strong>Email:</strong> ${wallet.email}</div>
                    </div>
                    <div style="margin-top: 0.5rem; font-size: 0.8rem; color: #9ca3af;">
                        Assigné le: ${wallet.assignedAt.toLocaleDateString()}
                    </div>
                </div>
            `).join('');
        }

        // Claims Management
        function loadClaims() {
            const claimsList = document.getElementById('claimsList');

            // Load ONLY real claims from localStorage - NO MOCK DATA
            const claims = [];

            // In a real implementation, this would load from blockchain or backend
            // For now, we'll load from localStorage if any claims were submitted
            const storedClaims = localStorage.getItem('himayaClaims');
            if (storedClaims) {
                const claimsData = JSON.parse(storedClaims);
                claims.push(...claimsData.filter(claim => claim.status === 'pending'));
            }

            if (claims.length === 0) {
                claimsList.innerHTML = '<p style="text-align: center; color: #9ca3af; padding: 2rem;">Aucune réclamation pendante</p>';
                return;
            }

            claimsList.innerHTML = claims.map(claim => `
                <div class="claim-item">
                    <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 1rem;">
                        <div style="flex: 1;">
                            <div style="display: flex; align-items: center; gap: 1rem; margin-bottom: 0.5rem;">
                                <strong style="color: #10b981; font-size: 1.1rem;">#${claim.claimId}</strong>
                                <span style="background: rgba(245, 158, 11, 0.2); color: #f59e0b; padding: 0.25rem 0.75rem; border-radius: 12px; font-size: 0.8rem;">
                                    ${claim.claimType}
                                </span>
                            </div>
                            <div style="color: #9ca3af; font-size: 0.9rem; margin-bottom: 0.5rem;">
                                👤 Client: ${claim.claimant.slice(0, 6)}...${claim.claimant.slice(-4)} | 
                                💰 Montant: ${claim.amount} ETH | 
                                📅 Soumis: ${claim.submittedAt.toLocaleDateString()}
                            </div>
                            <div style="color: #e5e7eb; margin-bottom: 1rem; padding: 0.75rem; background: rgba(0,0,0,0.3); border-radius: 6px;">
                                ${claim.description}
                            </div>
                            <div style="margin-bottom: 1rem;">
                                <div style="color: #10b981; font-weight: 600; margin-bottom: 0.5rem;">📎 Documents joints (${claim.fileHashes.length}):</div>
                                <div style="display: flex; flex-wrap: wrap; gap: 0.5rem;">
                                    ${claim.fileHashes.map(hash => `
                                        <div style="background: rgba(16, 185, 129, 0.1); border: 1px solid #10b981; padding: 0.5rem; border-radius: 6px; font-size: 0.8rem;">
                                            📄 ${hash.substring(0, 20)}...
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                        </div>
                        <div style="display: flex; flex-direction: column; gap: 0.5rem; min-width: 200px;">
                            <button class="btn" onclick="approveClaim('${claim.claimId}', '${claim.amount}')" style="width: 100%;">
                                ✅ Approuver (${claim.amount} ETH)
                            </button>
                            <button class="btn btn-danger" onclick="showRejectModal('${claim.claimId}')" style="width: 100%;">
                                ❌ Rejeter
                            </button>
                            <button class="btn btn-secondary" onclick="viewClaimDetails('${claim.claimId}')" style="width: 100%;">
                                👁️ Détails
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        async function approveClaim(claimId, amountEth) {
            if (!InsurerApp.connected) {
                showNotification('⚠️ Veuillez d\'abord connecter votre wallet');
                return;
            }

            try {
                showNotification(`🔄 Approbation de la réclamation avec transfert de ${amountEth} ETH...`);

                // Update claim status in localStorage
                const storedClaims = localStorage.getItem('himayaClaims');
                if (storedClaims) {
                    const claims = JSON.parse(storedClaims);
                    const claimIndex = claims.findIndex(claim => claim.claimId === claimId);
                    if (claimIndex !== -1) {
                        claims[claimIndex].status = 'approved';
                        claims[claimIndex].approvedAt = new Date().toISOString();
                        claims[claimIndex].approvedBy = InsurerApp.account;
                        localStorage.setItem('himayaClaims', JSON.stringify(claims));
                    }
                }

                // Simulate blockchain transaction for fund transfer
                setTimeout(() => {
                    showNotification(`🎉 Réclamation approuvée! ${amountEth} ETH transférés au client!`);
                    console.log('Claim approved with fund transfer:', {claimId, amountEth});

                    // Reload claims
                    loadClaims();
                    updateStats();
                }, 3000);

            } catch (error) {
                console.error('Claim approval failed:', error);
                showNotification('❌ Échec de l\'approbation de la réclamation');
            }
        }

        // Rejection Modal Functions
        function showRejectModal(claimId) {
            InsurerApp.currentRejectClaimId = claimId;
            document.getElementById('rejectModal').style.display = 'flex';
            document.getElementById('rejectionReason').value = '';
            document.getElementById('rejectionReason').focus();
        }

        function closeRejectModal() {
            document.getElementById('rejectModal').style.display = 'none';
            InsurerApp.currentRejectClaimId = null;
        }

        function confirmRejectClaim() {
            const rejectionReason = document.getElementById('rejectionReason').value.trim();

            if (!rejectionReason) {
                showNotification('⚠️ Veuillez indiquer une raison pour le rejet');
                return;
            }

            try {
                showNotification('🔄 Rejet de la réclamation avec raison...');

                // Update claim status in localStorage
                const storedClaims = localStorage.getItem('himayaClaims');
                if (storedClaims) {
                    const claims = JSON.parse(storedClaims);
                    const claimIndex = claims.findIndex(claim => claim.claimId === InsurerApp.currentRejectClaimId);
                    if (claimIndex !== -1) {
                        claims[claimIndex].status = 'rejected';
                        claims[claimIndex].rejectedAt = new Date().toISOString();
                        claims[claimIndex].rejectionReason = rejectionReason;
                        claims[claimIndex].rejectedBy = InsurerApp.account;
                        localStorage.setItem('himayaClaims', JSON.stringify(claims));
                    }
                }

                setTimeout(() => {
                    closeRejectModal();
                    showNotification(`❌ Réclamation ${InsurerApp.currentRejectClaimId} rejetée`);
                    console.log('Claim rejected:', {claimId: InsurerApp.currentRejectClaimId, rejectionReason});

                    loadClaims();
                    updateStats();
                }, 2000);

            } catch (error) {
                console.error('Claim rejection failed:', error);
                showNotification('❌ Échec du rejet de la réclamation');
            }
        }

        function viewClaimDetails(claimId) {
            alert(`Affichage des détails pour la réclamation: ${claimId}\n\nDans une implémentation réelle, ceci afficherait:\n- Tous les documents uploadés\n- Historique complet\n- Données du véhicule\n- Profil du client`);
        }

        // Statistics
        function updateStats() {
            document.getElementById('totalPolicies').textContent = Math.floor(Math.random() * 100) + 50;
            document.getElementById('totalPremiums').textContent = (Math.random() * 50 + 10).toFixed(4) + ' ETH';
            document.getElementById('pendingClaims').textContent = Math.floor(Math.random() * 10) + 2;
            document.getElementById('approvedClaims').textContent = Math.floor(Math.random() * 20) + 15;
        }

        // Data Loading
        async function loadInsurerData() {
            try {
                await Promise.all([
                    loadAssignedWallets(),
                    loadClaims(),
                    updateStats()
                ]);
                console.log('✅ Insurer data loaded');
            } catch (error) {
                console.error('Failed to load insurer data:', error);
            }
        }

        // Event Listeners
        document.addEventListener('DOMContentLoaded', () => {
            document.getElementById('connectWalletBtn').addEventListener('click', connectWallet);
            
            // Load initial data
            loadAssignedWallets();
            loadClaims();
            updateStats();
            
            // Auto-refresh every 30 seconds
            setInterval(() => {
                if (InsurerApp.connected) {
                    loadClaims();
                    updateStats();
                }
            }, 30000);
            
            console.log('🏢 Himaya Insurer App loaded');
        });
    </script>
</body>
</html>
