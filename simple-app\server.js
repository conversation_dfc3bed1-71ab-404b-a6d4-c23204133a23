const express = require('express');
const sqlite3 = require('sqlite3').verbose();
const cors = require('cors');
const path = require('path');
const { ethers } = require('ethers');

const app = express();
const PORT = 3002;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('.'));

// Database setup
const db = new sqlite3.Database('./insurance.db');

// Initialize database tables
db.serialize(() => {
    db.run(`
        CREATE TABLE IF NOT EXISTS vehicles (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            vin TEXT UNIQUE,
            make TEXT,
            model TEXT,
            year INTEGER,
            owner TEXT,
            blockchain_id INTEGER,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    `);

    db.run(`
        CREATE TABLE IF NOT EXISTS policies (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            vehicle_id INTEGER,
            premium REAL,
            coverage_amount REAL,
            coverage_type TEXT,
            owner TEXT,
            blockchain_id INTEGER,
            active BOOLEAN DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (vehicle_id) REFERENCES vehicles (id)
        )
    `);

    db.run(`
        CREATE TABLE IF NOT EXISTS claims (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            policy_id INTEGER,
            amount REAL,
            description TEXT,
            owner TEXT,
            blockchain_id INTEGER,
            approved BOOLEAN DEFAULT 0,
            processed BOOLEAN DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (policy_id) REFERENCES policies (id)
        )
    `);

    db.run(`
        CREATE TABLE IF NOT EXISTS sync_log (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            table_name TEXT,
            record_id INTEGER,
            blockchain_tx TEXT,
            synced_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    `);
});

// Blockchain configuration
const BLOCKCHAIN_CONFIG = {
    rpcUrl: 'http://localhost:8545',
    contractAddresses: {
        vehicleRegistry: '0x880EC53Af800b5Cd051531672EF4fc4De233bD5d',
        insurancePolicy: '0x3Dc2cd8F2E345951508427872d8ac9f635fBe0EC',
        claimManager: '0xe213D8b68cA3d01e51a6dBA669De59AC9A8359eE'
    }
};

// Contract ABIs (simplified)
const CONTRACT_ABIS = {
    vehicleRegistry: [
        "function registerVehicle(string memory vin, string memory make, string memory model, uint256 year) public",
        "function getVehicle(uint256 vehicleId) public view returns (string memory, string memory, string memory, uint256, address)",
        "function getVehiclesByOwner(address owner) public view returns (uint256[] memory)",
        "event VehicleRegistered(uint256 indexed vehicleId, address indexed owner, string vin)"
    ],
    insurancePolicy: [
        "function createPolicy(uint256 vehicleId, uint256 premium, uint256 coverageAmount, string memory coverageType) public payable",
        "function getPolicy(uint256 policyId) public view returns (uint256, uint256, uint256, string memory, address, bool)",
        "function getPoliciesByOwner(address owner) public view returns (uint256[] memory)",
        "event PolicyCreated(uint256 indexed policyId, address indexed owner, uint256 vehicleId)"
    ],
    claimManager: [
        "function submitClaim(uint256 policyId, uint256 amount, string memory description) public",
        "function approveClaim(uint256 claimId) public",
        "function getClaim(uint256 claimId) public view returns (uint256, uint256, string memory, address, bool, bool)",
        "function getClaimsByOwner(address owner) public view returns (uint256[] memory)",
        "event ClaimSubmitted(uint256 indexed claimId, address indexed claimant, uint256 policyId)"
    ]
};

// Initialize blockchain connection
let provider, contracts = {};

async function initBlockchain() {
    try {
        provider = new ethers.JsonRpcProvider(BLOCKCHAIN_CONFIG.rpcUrl);
        
        // Test connection
        await provider.getBlockNumber();
        console.log('✅ Blockchain connected');
        
        return true;
    } catch (error) {
        console.error('❌ Blockchain connection failed:', error.message);
        return false;
    }
}

// API Routes

// Health check
app.get('/api/health', (req, res) => {
    res.json({
        status: 'ok',
        database: 'connected',
        blockchain: provider ? 'connected' : 'disconnected',
        timestamp: new Date().toISOString()
    });
});

// Vehicle routes
app.get('/api/vehicles/:owner', (req, res) => {
    const { owner } = req.params;
    
    db.all(
        "SELECT * FROM vehicles WHERE owner = ? ORDER BY created_at DESC",
        [owner],
        (err, rows) => {
            if (err) {
                res.status(500).json({ error: err.message });
                return;
            }
            res.json(rows);
        }
    );
});

app.post('/api/vehicles', (req, res) => {
    const { vin, make, model, year, owner } = req.body;
    
    db.run(
        "INSERT INTO vehicles (vin, make, model, year, owner) VALUES (?, ?, ?, ?, ?)",
        [vin, make, model, year, owner],
        function(err) {
            if (err) {
                res.status(500).json({ error: err.message });
                return;
            }
            res.json({
                id: this.lastID,
                message: 'Vehicle registered successfully'
            });
        }
    );
});

// Policy routes
app.get('/api/policies/:owner', (req, res) => {
    const { owner } = req.params;
    
    db.all(`
        SELECT p.*, v.make, v.model, v.year, v.vin 
        FROM policies p 
        JOIN vehicles v ON p.vehicle_id = v.id 
        WHERE p.owner = ? 
        ORDER BY p.created_at DESC
    `, [owner], (err, rows) => {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        res.json(rows);
    });
});

app.post('/api/policies', (req, res) => {
    const { vehicle_id, premium, coverage_amount, coverage_type, owner } = req.body;
    
    db.run(
        "INSERT INTO policies (vehicle_id, premium, coverage_amount, coverage_type, owner) VALUES (?, ?, ?, ?, ?)",
        [vehicle_id, premium, coverage_amount, coverage_type, owner],
        function(err) {
            if (err) {
                res.status(500).json({ error: err.message });
                return;
            }
            res.json({
                id: this.lastID,
                message: 'Policy created successfully'
            });
        }
    );
});

// Claim routes
app.get('/api/claims/:owner', (req, res) => {
    const { owner } = req.params;
    
    db.all(`
        SELECT c.*, p.coverage_type, v.make, v.model, v.year 
        FROM claims c 
        JOIN policies p ON c.policy_id = p.id 
        JOIN vehicles v ON p.vehicle_id = v.id 
        WHERE c.owner = ? 
        ORDER BY c.created_at DESC
    `, [owner], (err, rows) => {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        res.json(rows);
    });
});

app.post('/api/claims', (req, res) => {
    const { policy_id, amount, description, owner } = req.body;

    db.run(
        "INSERT INTO claims (policy_id, amount, description, owner) VALUES (?, ?, ?, ?)",
        [policy_id, amount, description, owner],
        function(err) {
            if (err) {
                res.status(500).json({ error: err.message });
                return;
            }
            res.json({
                id: this.lastID,
                message: 'Claim submitted successfully'
            });
        }
    );
});

// Get all claims (for admin)
app.get('/api/claims/all', (req, res) => {
    db.all(`
        SELECT c.*, p.coverage_type, v.make, v.model, v.year, v.vin
        FROM claims c
        JOIN policies p ON c.policy_id = p.id
        JOIN vehicles v ON p.vehicle_id = v.id
        ORDER BY c.created_at DESC
    `, [], (err, rows) => {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        res.json(rows);
    });
});

// Approve claim
app.post('/api/claims/:id/approve', (req, res) => {
    const { id } = req.params;

    db.run(
        "UPDATE claims SET approved = 1, processed = 1 WHERE id = ?",
        [id],
        function(err) {
            if (err) {
                res.status(500).json({ error: err.message });
                return;
            }
            if (this.changes === 0) {
                res.status(404).json({ error: 'Claim not found' });
                return;
            }
            res.json({ message: 'Claim approved successfully' });
        }
    );
});

// Reject claim
app.post('/api/claims/:id/reject', (req, res) => {
    const { id } = req.params;

    db.run(
        "UPDATE claims SET approved = 0, processed = 1 WHERE id = ?",
        [id],
        function(err) {
            if (err) {
                res.status(500).json({ error: err.message });
                return;
            }
            if (this.changes === 0) {
                res.status(404).json({ error: 'Claim not found' });
                return;
            }
            res.json({ message: 'Claim rejected successfully' });
        }
    );
});

// Dashboard stats
app.get('/api/dashboard/:owner', (req, res) => {
    const { owner } = req.params;
    
    const stats = {};
    
    // Get vehicle count
    db.get("SELECT COUNT(*) as count FROM vehicles WHERE owner = ?", [owner], (err, row) => {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        stats.vehicles = row.count;
        
        // Get policy count
        db.get("SELECT COUNT(*) as count FROM policies WHERE owner = ? AND active = 1", [owner], (err, row) => {
            if (err) {
                res.status(500).json({ error: err.message });
                return;
            }
            stats.policies = row.count;
            
            // Get claim count
            db.get("SELECT COUNT(*) as count FROM claims WHERE owner = ?", [owner], (err, row) => {
                if (err) {
                    res.status(500).json({ error: err.message });
                    return;
                }
                stats.claims = row.count;
                
                // Get total coverage
                db.get("SELECT SUM(coverage_amount) as total FROM policies WHERE owner = ? AND active = 1", [owner], (err, row) => {
                    if (err) {
                        res.status(500).json({ error: err.message });
                        return;
                    }
                    stats.totalCoverage = row.total || 0;
                    res.json(stats);
                });
            });
        });
    });
});

// Blockchain sync endpoint
app.post('/api/sync', async (req, res) => {
    try {
        if (!provider) {
            await initBlockchain();
        }
        
        // This would implement actual blockchain synchronization
        // For now, just return success
        res.json({
            message: 'Sync completed successfully',
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// Serve the main HTML file
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'index.html'));
});

// Start server
app.listen(PORT, async () => {
    console.log(`🚀 Himaya Insurance Server running on http://localhost:${PORT}`);
    console.log('📊 Database: SQLite initialized');
    
    const blockchainConnected = await initBlockchain();
    if (blockchainConnected) {
        console.log('🔗 Blockchain: Connected to local network');
    } else {
        console.log('⚠️  Blockchain: Connection failed (will retry)');
    }
    
    console.log('\n🛡️  Himaya Insurance Platform Ready!');
    console.log(`   Frontend: http://localhost:${PORT}`);
    console.log(`   API: http://localhost:${PORT}/api/health`);
});

// Graceful shutdown
process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down server...');
    db.close((err) => {
        if (err) {
            console.error('Error closing database:', err.message);
        } else {
            console.log('📊 Database connection closed');
        }
        process.exit(0);
    });
});
