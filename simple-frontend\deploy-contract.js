// Deployment script for Himaya Insurance Smart Contract
// Run with: node deploy-contract.js

const Web3 = require('web3');
const fs = require('fs');
const solc = require('solc');

// Configuration
const NETWORK_URL = 'http://localhost:8545'; // Local Hardhat/Ganache
const PRIVATE_KEY = '0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80'; // Hardhat account #0

async function deployContract() {
    console.log('🚀 Starting Himaya Insurance Contract Deployment...');
    
    try {
        // Initialize Web3
        const web3 = new Web3(NETWORK_URL);
        
        // Add account
        const account = web3.eth.accounts.privateKeyToAccount(PRIVATE_KEY);
        web3.eth.accounts.wallet.add(account);
        web3.eth.defaultAccount = account.address;
        
        console.log('📝 Deploying from account:', account.address);
        
        // Read and compile contract
        const contractSource = fs.readFileSync('./HimayaInsurance.sol', 'utf8');
        
        const input = {
            language: 'Solidity',
            sources: {
                'HimayaInsurance.sol': {
                    content: contractSource
                }
            },
            settings: {
                outputSelection: {
                    '*': {
                        '*': ['*']
                    }
                }
            }
        };
        
        console.log('🔨 Compiling contract...');
        const compiled = JSON.parse(solc.compile(JSON.stringify(input)));
        
        if (compiled.errors) {
            compiled.errors.forEach(error => {
                if (error.severity === 'error') {
                    console.error('❌ Compilation error:', error.formattedMessage);
                    return;
                }
            });
        }
        
        const contract = compiled.contracts['HimayaInsurance.sol']['HimayaInsurance'];
        const abi = contract.abi;
        const bytecode = contract.evm.bytecode.object;
        
        console.log('✅ Contract compiled successfully');
        
        // Deploy contract
        console.log('🚀 Deploying contract to blockchain...');
        
        const deployContract = new web3.eth.Contract(abi);
        const deployTx = deployContract.deploy({
            data: '0x' + bytecode
        });
        
        const gas = await deployTx.estimateGas({ from: account.address });
        console.log('⛽ Estimated gas:', gas);
        
        const deployedContract = await deployTx.send({
            from: account.address,
            gas: gas + 100000, // Add buffer
            gasPrice: await web3.eth.getGasPrice()
        });
        
        console.log('🎉 Contract deployed successfully!');
        console.log('📍 Contract address:', deployedContract.options.address);
        console.log('🔗 Transaction hash:', deployedContract.transactionHash);
        
        // Save deployment info
        const deploymentInfo = {
            contractAddress: deployedContract.options.address,
            transactionHash: deployedContract.transactionHash,
            deployedAt: new Date().toISOString(),
            network: NETWORK_URL,
            deployer: account.address,
            abi: abi
        };
        
        fs.writeFileSync('./deployment-info.json', JSON.stringify(deploymentInfo, null, 2));
        console.log('💾 Deployment info saved to deployment-info.json');
        
        // Test contract functions
        console.log('\n🧪 Testing contract functions...');
        
        const contractInstance = new web3.eth.Contract(abi, deployedContract.options.address);
        
        // Test getting contract stats
        const stats = await contractInstance.methods.getContractStats().call();
        console.log('📊 Initial contract stats:', {
            totalPremiums: web3.utils.fromWei(stats.totalPremiums, 'ether') + ' ETH',
            totalClaims: web3.utils.fromWei(stats.totalClaims, 'ether') + ' ETH',
            activeUsers: stats.activeUsers,
            balance: web3.utils.fromWei(stats.balance, 'ether') + ' ETH',
            totalVehicles: stats.totalVehicles,
            totalClaimsCount: stats.totalClaimsCount
        });
        
        // Test plan subscription
        console.log('\n💳 Testing plan subscription...');
        const planPrice = web3.utils.toWei('0.05', 'ether');
        
        const subscribeTx = await contractInstance.methods.subscribeToPlan('basic').send({
            from: account.address,
            value: planPrice,
            gas: 500000
        });
        
        console.log('✅ Plan subscription successful:', subscribeTx.transactionHash);
        
        // Check subscription
        const subscription = await contractInstance.methods.getUserSubscription(account.address).call();
        console.log('📋 User subscription:', {
            planType: subscription.planType,
            isActive: subscription.isActive,
            expiresAt: new Date(subscription.expiresAt * 1000).toISOString(),
            paidAmount: web3.utils.fromWei(subscription.paidAmount, 'ether') + ' ETH'
        });
        
        // Test vehicle registration
        console.log('\n🚗 Testing vehicle registration...');
        const vehicleId = `TEST-VEHICLE-${Date.now()}`;
        
        const registerTx = await contractInstance.methods.registerVehicle(
            vehicleId,
            'Toyota',
            'Camry',
            2023,
            'car',
            'Casablanca'
        ).send({
            from: account.address,
            gas: 500000
        });
        
        console.log('✅ Vehicle registration successful:', registerTx.transactionHash);
        
        // Get user vehicles
        const vehicles = await contractInstance.methods.getUserVehicles(account.address).call();
        console.log('🚗 User vehicles:', vehicles);
        
        if (vehicles.length > 0) {
            const vehicleDetails = await contractInstance.methods.getVehicle(vehicles[0]).call();
            console.log('🔍 Vehicle details:', {
                make: vehicleDetails.make,
                model: vehicleDetails.model,
                year: vehicleDetails.year,
                type: vehicleDetails.vehicleType,
                city: vehicleDetails.city,
                planType: vehicleDetails.planType
            });
        }
        
        // Test claim submission
        console.log('\n🔍 Testing claim submission...');
        const claimId = `TEST-CLAIM-${Date.now()}`;
        const claimAmount = web3.utils.toWei('1', 'ether');
        
        const claimTx = await contractInstance.methods.submitClaim(
            claimId,
            vehicles[0],
            'accident',
            'Test accident claim for deployment verification',
            claimAmount
        ).send({
            from: account.address,
            gas: 500000
        });
        
        console.log('✅ Claim submission successful:', claimTx.transactionHash);
        
        // Get pending claims
        const pendingClaims = await contractInstance.methods.getPendingClaims().call();
        console.log('📋 Pending claims:', pendingClaims);
        
        // Final stats
        const finalStats = await contractInstance.methods.getContractStats().call();
        console.log('\n📊 Final contract stats:', {
            totalPremiums: web3.utils.fromWei(finalStats.totalPremiums, 'ether') + ' ETH',
            totalClaims: web3.utils.fromWei(finalStats.totalClaims, 'ether') + ' ETH',
            activeUsers: finalStats.activeUsers,
            balance: web3.utils.fromWei(finalStats.balance, 'ether') + ' ETH',
            totalVehicles: finalStats.totalVehicles,
            totalClaimsCount: finalStats.totalClaimsCount
        });
        
        console.log('\n🎉 DEPLOYMENT AND TESTING COMPLETED SUCCESSFULLY!');
        console.log('🔗 Update the CONTRACT_CONFIG.address in himaya-app.html to:', deployedContract.options.address);
        
    } catch (error) {
        console.error('❌ Deployment failed:', error);
        process.exit(1);
    }
}

// Run deployment
deployContract();
