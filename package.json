{"name": "hardhat-project", "devDependencies": {"@nomicfoundation/hardhat-toolbox": "^5.0.0", "hardhat": "^2.24.1"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.1.1", "@mui/material": "^7.1.1", "@openzeppelin/contracts": "^5.3.0", "@tanstack/react-query": "^5.80.3", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "body-parser": "^2.2.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "express-rate-limit": "^7.5.0", "express-validator": "^7.2.1", "ganache-cli": "^6.12.2", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.15.1", "morgan": "^1.10.0", "multer": "^2.0.1", "path-to-regexp": "^6.2.1", "rate-limit-redis": "^4.2.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.2", "react-scripts": "^5.0.1", "redis": "^5.5.5", "web3": "^4.16.0"}, "version": "1.0.0", "description": "A comprehensive decentralized application for managing vehicle insurance claims using Hyperledger Besu blockchain. This student project demonstrates a full-stack blockchain implementation with smart contracts, backend API, and React frontend.", "main": "hardhat.config.js", "directories": {"test": "test"}, "scripts": {"test": "mocha"}, "keywords": [], "author": "", "license": "ISC"}