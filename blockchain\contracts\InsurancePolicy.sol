// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/Counters.sol";
import "./VehicleRegistry.sol";

contract InsurancePolicy is Ownable {
    using Counters for Counters.Counter;
    
    Counters.Counter private _policyIds;
    VehicleRegistry public vehicleRegistry;
    
    enum PolicyStatus { Active, Expired, Cancelled, Suspended }
    enum CoverageType { Liability, Comprehensive, Collision, Full }
    
    struct Policy {
        uint256 id;
        uint256 vehicleId;
        address policyholder;
        address insurer;
        CoverageType coverageType;
        uint256 premiumAmount;
        uint256 coverageAmount;
        uint256 deductible;
        uint256 startDate;
        uint256 endDate;
        PolicyStatus status;
        string policyNumber;
    }
    
    mapping(uint256 => Policy) public policies;
    mapping(address => uint256[]) public policyholderPolicies;
    mapping(address => uint256[]) public insurerPolicies;
    mapping(uint256 => uint256[]) public vehiclePolicies; // vehicleId => policyIds
    mapping(string => uint256) public policyNumberToId;
    
    // Authorized insurers
    mapping(address => bool) public authorizedInsurers;
    
    event PolicyCreated(
        uint256 indexed policyId,
        uint256 indexed vehicleId,
        address indexed policyholder,
        address insurer,
        string policyNumber
    );
    
    event PolicyStatusChanged(
        uint256 indexed policyId,
        PolicyStatus previousStatus,
        PolicyStatus newStatus
    );
    
    event InsurerAuthorized(address indexed insurer);
    event InsurerRevoked(address indexed insurer);
    
    modifier onlyAuthorizedInsurer() {
        require(authorizedInsurers[msg.sender], "Not authorized insurer");
        _;
    }
    
    modifier onlyPolicyholder(uint256 _policyId) {
        require(policies[_policyId].policyholder == msg.sender, "Not policyholder");
        _;
    }
    
    modifier policyExists(uint256 _policyId) {
        require(policies[_policyId].id != 0, "Policy does not exist");
        _;
    }
    
    constructor(address _vehicleRegistryAddress) Ownable(msg.sender) {
        vehicleRegistry = VehicleRegistry(_vehicleRegistryAddress);
    }
    
    function authorizeInsurer(address _insurer) external onlyOwner {
        require(_insurer != address(0), "Invalid insurer address");
        authorizedInsurers[_insurer] = true;
        emit InsurerAuthorized(_insurer);
    }
    
    function revokeInsurer(address _insurer) external onlyOwner {
        authorizedInsurers[_insurer] = false;
        emit InsurerRevoked(_insurer);
    }
    
    function createPolicy(
        uint256 _vehicleId,
        address _policyholder,
        CoverageType _coverageType,
        uint256 _premiumAmount,
        uint256 _coverageAmount,
        uint256 _deductible,
        uint256 _durationInDays,
        string memory _policyNumber
    ) external onlyAuthorizedInsurer returns (uint256) {
        require(_policyholder != address(0), "Invalid policyholder address");
        require(_premiumAmount > 0, "Premium must be greater than 0");
        require(_coverageAmount > 0, "Coverage amount must be greater than 0");
        require(bytes(_policyNumber).length > 0, "Policy number cannot be empty");
        require(policyNumberToId[_policyNumber] == 0, "Policy number already exists");
        
        // Verify vehicle exists and policyholder owns it
        VehicleRegistry.Vehicle memory vehicle = vehicleRegistry.getVehicle(_vehicleId);
        require(vehicle.owner == _policyholder, "Policyholder must own the vehicle");
        
        _policyIds.increment();
        uint256 newPolicyId = _policyIds.current();
        
        uint256 startDate = block.timestamp;
        uint256 endDate = startDate + (_durationInDays * 1 days);
        
        policies[newPolicyId] = Policy({
            id: newPolicyId,
            vehicleId: _vehicleId,
            policyholder: _policyholder,
            insurer: msg.sender,
            coverageType: _coverageType,
            premiumAmount: _premiumAmount,
            coverageAmount: _coverageAmount,
            deductible: _deductible,
            startDate: startDate,
            endDate: endDate,
            status: PolicyStatus.Active,
            policyNumber: _policyNumber
        });
        
        policyNumberToId[_policyNumber] = newPolicyId;
        policyholderPolicies[_policyholder].push(newPolicyId);
        insurerPolicies[msg.sender].push(newPolicyId);
        vehiclePolicies[_vehicleId].push(newPolicyId);
        
        emit PolicyCreated(newPolicyId, _vehicleId, _policyholder, msg.sender, _policyNumber);
        
        return newPolicyId;
    }
    
    function updatePolicyStatus(uint256 _policyId, PolicyStatus _newStatus) 
        external 
        policyExists(_policyId) 
    {
        Policy storage policy = policies[_policyId];
        require(
            msg.sender == policy.insurer || msg.sender == owner(),
            "Only insurer or owner can update status"
        );
        
        PolicyStatus previousStatus = policy.status;
        policy.status = _newStatus;
        
        emit PolicyStatusChanged(_policyId, previousStatus, _newStatus);
    }
    
    function getPolicy(uint256 _policyId) 
        external 
        view 
        policyExists(_policyId) 
        returns (Policy memory) 
    {
        return policies[_policyId];
    }
    
    function getPolicyByNumber(string memory _policyNumber) 
        external 
        view 
        returns (Policy memory) 
    {
        uint256 policyId = policyNumberToId[_policyNumber];
        require(policyId != 0, "Policy not found");
        return policies[policyId];
    }
    
    function getPolicyholderPolicies(address _policyholder) 
        external 
        view 
        returns (uint256[] memory) 
    {
        return policyholderPolicies[_policyholder];
    }
    
    function getInsurerPolicies(address _insurer) 
        external 
        view 
        returns (uint256[] memory) 
    {
        return insurerPolicies[_insurer];
    }
    
    function getVehiclePolicies(uint256 _vehicleId) 
        external 
        view 
        returns (uint256[] memory) 
    {
        return vehiclePolicies[_vehicleId];
    }
    
    function isPolicyActive(uint256 _policyId) 
        external 
        view 
        policyExists(_policyId) 
        returns (bool) 
    {
        Policy memory policy = policies[_policyId];
        return policy.status == PolicyStatus.Active && 
               block.timestamp >= policy.startDate && 
               block.timestamp <= policy.endDate;
    }
    
    function getTotalPolicies() external view returns (uint256) {
        return _policyIds.current();
    }
}
