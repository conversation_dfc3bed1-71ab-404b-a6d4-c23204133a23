<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>⏳ Himaya - Statut de Vérification</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 50%, #1a1a1a 100%);
            color: #e5e5e5;
            min-height: 100vh;
            padding: 2rem;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 3rem;
            background: rgba(139, 92, 246, 0.1);
            border: 1px solid #8b5cf6;
            border-radius: 20px;
            padding: 2rem;
        }
        
        .logo {
            font-size: 4rem;
            margin-bottom: 1rem;
        }
        
        .title {
            font-size: 2.5rem;
            font-weight: 700;
            color: #8b5cf6;
            margin-bottom: 1rem;
        }
        
        .subtitle {
            font-size: 1.2rem;
            color: #9ca3af;
        }
        
        .card {
            background: #2d3748;
            border: 1px solid #4a5568;
            border-radius: 16px;
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .verification-status {
            text-align: center;
            padding: 2rem;
        }
        
        .status-pending {
            color: #f59e0b;
        }
        
        .status-approved {
            color: #10b981;
        }
        
        .status-rejected {
            color: #ef4444;
        }
        
        .status-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
        }
        
        .status-title {
            font-size: 1.8rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }
        
        .status-description {
            color: #9ca3af;
            margin-bottom: 2rem;
            font-size: 1.1rem;
        }
        
        .wallet-credentials {
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid #10b981;
            border-radius: 12px;
            padding: 1.5rem;
            margin: 1rem 0;
        }
        
        .credential-item {
            background: #374151;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .credential-label {
            color: #8b5cf6;
            font-weight: 600;
        }
        
        .credential-value {
            color: #e5e7eb;
            font-family: monospace;
            background: rgba(0,0,0,0.3);
            padding: 0.5rem;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .copy-btn {
            background: #6b7280;
            border: none;
            border-radius: 4px;
            padding: 0.25rem 0.5rem;
            color: white;
            cursor: pointer;
            font-size: 0.8rem;
            margin-left: 0.5rem;
        }
        
        .copy-btn:hover {
            background: #4b5563;
        }
        
        .btn {
            background: #8b5cf6;
            border: 1px solid #8b5cf6;
            border-radius: 8px;
            padding: 1rem 2rem;
            color: white;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-block;
            font-size: 1.1rem;
        }
        
        .btn:hover {
            background: #7c3aed;
            transform: translateY(-1px);
        }
        
        .btn-secondary {
            background: #374151;
            border-color: #4b5563;
        }
        
        .btn-secondary:hover {
            background: #4b5563;
        }
        
        .process-steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }
        
        .step {
            text-align: center;
            padding: 1rem;
            background: #374151;
            border-radius: 12px;
        }
        
        .step-number {
            background: #8b5cf6;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-weight: 600;
        }
        
        .notification {
            position: fixed;
            top: 2rem;
            right: 2rem;
            background: #374151;
            color: #e5e7eb;
            padding: 1rem 1.5rem;
            border-radius: 8px;
            border: 1px solid #8b5cf6;
            z-index: 1000;
            animation: slideIn 0.3s ease;
        }
        
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        
        @media (max-width: 768px) {
            .title { font-size: 2rem; }
            .process-steps { grid-template-columns: 1fr; }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="logo">⏳</div>
            <h1 class="title">STATUT DE VÉRIFICATION</h1>
            <p class="subtitle">Himaya Blockchain Insurance</p>
        </div>

        <!-- Status Display -->
        <div class="card">
            <div id="verificationStatusContent">
                <!-- Content will be dynamically updated -->
            </div>
        </div>

        <!-- What's Next -->
        <div class="card">
            <h2 style="color: #8b5cf6; margin-bottom: 1.5rem;">📋 Prochaines Étapes</h2>
            <div class="process-steps">
                <div class="step">
                    <div class="step-number">1</div>
                    <h4 style="color: #8b5cf6; margin-bottom: 0.5rem;">Vérification</h4>
                    <p style="color: #9ca3af; font-size: 0.9rem;">Notre équipe vérifie vos documents d'identité</p>
                </div>
                <div class="step">
                    <div class="step-number">2</div>
                    <h4 style="color: #8b5cf6; margin-bottom: 0.5rem;">Wallet Sécurisé</h4>
                    <p style="color: #9ca3af; font-size: 0.9rem;">Réception des identifiants MetaMask par email</p>
                </div>
                <div class="step">
                    <div class="step-number">3</div>
                    <h4 style="color: #8b5cf6; margin-bottom: 0.5rem;">Accès Complet</h4>
                    <p style="color: #9ca3af; font-size: 0.9rem;">Utilisation complète de la plateforme Himaya</p>
                </div>
            </div>
        </div>

        <!-- Contact Support -->
        <div class="card">
            <h2 style="color: #8b5cf6; margin-bottom: 1.5rem;">📞 Besoin d'Aide ?</h2>
            <div style="background: rgba(139, 92, 246, 0.1); border: 1px solid #8b5cf6; border-radius: 12px; padding: 1.5rem;">
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                    <div>
                        <strong style="color: #8b5cf6;">📧 Email Support</strong><br>
                        <span style="color: #e5e7eb;"><EMAIL></span>
                    </div>
                    <div>
                        <strong style="color: #8b5cf6;">📱 Téléphone</strong><br>
                        <span style="color: #e5e7eb;">+212 5XX-XXXXXX</span>
                    </div>
                    <div>
                        <strong style="color: #8b5cf6;">⏰ Horaires</strong><br>
                        <span style="color: #e5e7eb;">Lun-Ven 9h-18h</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div style="text-align: center; margin-top: 2rem;">
            <button class="btn btn-secondary" onclick="window.location.href='client-registration.html'" style="margin-right: 1rem;">
                🔙 Nouvelle Demande
            </button>
            <button class="btn" onclick="checkVerificationStatus()">
                🔄 Vérifier le Statut
            </button>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        // Application State
        let verificationData = null;

        // Utility Functions
        function showNotification(message) {
            const existing = document.querySelectorAll('.notification');
            existing.forEach(n => n.remove());

            const notification = document.createElement('div');
            notification.className = 'notification';
            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 4000);
        }

        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                showNotification('📋 Copié dans le presse-papiers');
            }).catch(() => {
                showNotification('❌ Impossible de copier');
            });
        }

        // Status Management
        function updateVerificationStatusDisplay() {
            const statusContent = document.getElementById('verificationStatusContent');

            if (!verificationData) {
                statusContent.innerHTML = `
                    <div class="verification-status">
                        <div class="status-icon">❓</div>
                        <div class="status-title">Aucune Demande Trouvée</div>
                        <div class="status-description">
                            Aucune demande de vérification n'a été trouvée.<br>
                            Veuillez d'abord soumettre une demande de vérification.
                        </div>
                        <button class="btn" onclick="window.location.href='client-registration.html'">
                            📝 Soumettre une Demande
                        </button>
                    </div>
                `;
                return;
            }

            if (verificationData.status === 'pending') {
                statusContent.innerHTML = `
                    <div class="verification-status">
                        <div class="status-icon status-pending">⏳</div>
                        <div class="status-title status-pending">Vérification en Cours</div>
                        <div class="status-description">
                            Votre demande de vérification a été soumise avec succès.<br>
                            Notre équipe examine actuellement vos documents.
                        </div>
                        <div style="background: rgba(245, 158, 11, 0.1); border: 1px solid #f59e0b; border-radius: 12px; padding: 1.5rem;">
                            <h4 style="color: #f59e0b; margin-bottom: 1rem;">📋 Détails de votre demande</h4>
                            <div style="text-align: left; color: #e5e7eb;">
                                <div><strong>ID de demande:</strong> ${verificationData.submissionId}</div>
                                <div><strong>Nom:</strong> ${verificationData.personalInfo.fullName}</div>
                                <div><strong>CIN:</strong> ${verificationData.personalInfo.cinNumber}</div>
                                <div><strong>Email:</strong> ${verificationData.personalInfo.emailAddress}</div>
                                <div><strong>Soumis le:</strong> ${new Date(verificationData.submittedAt).toLocaleString()}</div>
                            </div>
                        </div>
                        <div style="margin-top: 2rem; color: #9ca3af;">
                            ⏱️ Temps de traitement estimé: 24-48 heures<br>
                            📧 Vous recevrez une notification par email une fois la vérification terminée
                        </div>
                    </div>
                `;
            } else if (verificationData.status === 'approved') {
                statusContent.innerHTML = `
                    <div class="verification-status">
                        <div class="status-icon status-approved">✅</div>
                        <div class="status-title status-approved">Vérification Approuvée!</div>
                        <div class="status-description">
                            Félicitations! Votre identité a été vérifiée avec succès.<br>
                            Voici vos identifiants de wallet sécurisé.
                        </div>
                        <div class="wallet-credentials">
                            <h4 style="color: #10b981; margin-bottom: 1rem;">🔐 Vos Identifiants MetaMask</h4>
                            <div class="credential-item">
                                <div class="credential-label">Adresse Wallet:</div>
                                <div>
                                    <span class="credential-value" onclick="copyToClipboard('${verificationData.walletAddress}')">${verificationData.walletAddress}</span>
                                    <button class="copy-btn" onclick="copyToClipboard('${verificationData.walletAddress}')">Copier</button>
                                </div>
                            </div>
                            <div class="credential-item">
                                <div class="credential-label">Clé Privée:</div>
                                <div>
                                    <span class="credential-value" onclick="copyToClipboard('${verificationData.privateKey}')">${verificationData.privateKey}</span>
                                    <button class="copy-btn" onclick="copyToClipboard('${verificationData.privateKey}')">Copier</button>
                                </div>
                            </div>
                            <div style="background: rgba(239, 68, 68, 0.1); border: 1px solid #ef4444; border-radius: 8px; padding: 1rem; margin-top: 1rem;">
                                <strong style="color: #ef4444;">⚠️ IMPORTANT:</strong>
                                <ul style="color: #e5e7eb; margin: 0.5rem 0 0 1rem;">
                                    <li>Gardez ces informations secrètes et sécurisées</li>
                                    <li>Ne partagez jamais votre clé privée</li>
                                    <li>Sauvegardez ces informations dans un endroit sûr</li>
                                </ul>
                            </div>
                        </div>
                        <button class="btn" onclick="window.location.href='client-app.html'" style="margin-top: 2rem;">
                            🚀 Accéder à Mon Compte Himaya
                        </button>
                    </div>
                `;
            } else if (verificationData.status === 'rejected') {
                statusContent.innerHTML = `
                    <div class="verification-status">
                        <div class="status-icon status-rejected">❌</div>
                        <div class="status-title status-rejected">Vérification Rejetée</div>
                        <div class="status-description">
                            Malheureusement, votre demande de vérification n'a pas pu être approuvée.
                        </div>
                        <div style="background: rgba(239, 68, 68, 0.1); border: 1px solid #ef4444; border-radius: 12px; padding: 1.5rem;">
                            <h4 style="color: #ef4444; margin-bottom: 1rem;">📋 Raison du rejet</h4>
                            <div style="color: #e5e7eb;">
                                ${verificationData.rejectionReason || 'Documents non conformes ou illisibles'}
                            </div>
                        </div>
                        <button class="btn" onclick="window.location.href='client-registration.html'" style="margin-top: 2rem;">
                            🔄 Soumettre une Nouvelle Demande
                        </button>
                    </div>
                `;
            }
        }

        function checkVerificationStatus() {
            // Load from localStorage (in real app, this would check backend/blockchain)
            const stored = localStorage.getItem('himayaRegistration');
            if (stored) {
                verificationData = JSON.parse(stored);
                
                // Simulate status progression for demo
                const submittedTime = new Date(verificationData.submittedAt).getTime();
                const now = Date.now();
                const timeDiff = now - submittedTime;
                
                // After 30 seconds, simulate approval for demo
                if (timeDiff > 30000 && verificationData.status === 'pending') {
                    verificationData.status = 'approved';
                    verificationData.walletAddress = '0x' + Math.random().toString(16).substr(2, 40);
                    verificationData.privateKey = '0x' + Math.random().toString(16).substr(2, 64);
                    verificationData.approvedAt = new Date().toISOString();
                    localStorage.setItem('himayaRegistration', JSON.stringify(verificationData));
                    showNotification('🎉 Vérification approuvée! Vos identifiants sont prêts.');
                }
                
                updateVerificationStatusDisplay();
            } else {
                verificationData = null;
                updateVerificationStatusDisplay();
                showNotification('⚠️ Aucune demande de vérification trouvée');
            }
        }

        // Initialize app
        document.addEventListener('DOMContentLoaded', () => {
            checkVerificationStatus();
            
            // Auto-refresh every 10 seconds
            setInterval(checkVerificationStatus, 10000);
            
            console.log('⏳ Himaya Verification Status App loaded');
        });
    </script>
</body>
</html>
