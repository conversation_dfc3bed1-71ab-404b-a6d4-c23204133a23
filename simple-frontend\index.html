<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🛡️ HIMAYA BLOCKCHAIN - BRAND NEW INTERFACE</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 50%, #1a1a1a 100%);
            color: #e5e5e5;
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .header {
            text-align: center;
            padding: 3rem 0;
            position: relative;
        }

        .logo {
            font-size: 4rem;
            margin-bottom: 1.5rem;
            color: #8b5cf6;
            transition: transform 0.3s ease;
        }

        .logo:hover {
            transform: scale(1.05);
        }

        .title {
            font-size: 3.5rem;
            font-weight: 700;
            color: #f8f9fa;
            margin-bottom: 1rem;
            letter-spacing: -1px;
        }

        .subtitle {
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
            color: #8b5cf6;
            font-weight: 500;
        }

        .tagline {
            font-size: 1.1rem;
            margin-bottom: 3rem;
            color: #9ca3af;
            font-weight: 400;
        }

        .nav {
            display: flex;
            justify-content: center;
            gap: 2rem;
            margin-bottom: 4rem;
            flex-wrap: wrap;
        }

        .nav-btn {
            background: #374151;
            border: 2px solid #4b5563;
            border-radius: 12px;
            padding: 1rem 2rem;
            color: #e5e7eb;
            font-weight: 600;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.2s ease;
            text-transform: none;
            letter-spacing: 0.5px;
        }

        .nav-btn:hover {
            background: #4b5563;
            border-color: #8b5cf6;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(139, 92, 246, 0.2);
        }

        .nav-btn.active {
            background: #8b5cf6;
            border-color: #8b5cf6;
            color: white;
        }

        .status-bar {
            background: #2d3748;
            border: 1px solid #4a5568;
            border-radius: 16px;
            padding: 1.5rem;
            margin-bottom: 3rem;
            display: flex;
            justify-content: space-around;
            flex-wrap: wrap;
            gap: 2rem;
        }

        .status-item {
            text-align: center;
            font-weight: 500;
            font-size: 0.9rem;
            color: #cbd5e0;
        }

        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #ef4444;
            margin: 0 auto 0.5rem;
            transition: all 0.3s ease;
        }

        .status-dot.connected {
            background: #10b981;
        }

        .card {
            background: #2d3748;
            border: 1px solid #4a5568;
            border-radius: 16px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        }

        .card-title {
            font-size: 1.8rem;
            font-weight: 600;
            margin-bottom: 1.5rem;
            color: #f7fafc;
            text-align: center;
            text-transform: none;
            letter-spacing: normal;
        }

        .btn {
            background: #8b5cf6;
            border: 1px solid #8b5cf6;
            border-radius: 8px;
            padding: 0.75rem 1.5rem;
            color: white;
            font-weight: 500;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.2s ease;
            text-transform: none;
            letter-spacing: normal;
            margin: 0.5rem;
        }

        .btn:hover {
            background: #7c3aed;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
        }

        .form-input {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #4a5568;
            border-radius: 8px;
            background: #374151;
            color: #e5e7eb;
            font-size: 0.9rem;
            font-weight: normal;
            margin-bottom: 1rem;
            transition: all 0.2s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: #8b5cf6;
            box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
            background: #4a5568;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
            animation: fadeIn 0.3s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .notification {
            position: fixed;
            top: 2rem;
            right: 2rem;
            background: #374151;
            color: #e5e7eb;
            padding: 1rem 1.5rem;
            border-radius: 8px;
            font-weight: 500;
            font-size: 0.9rem;
            z-index: 1000;
            animation: slideInRight 0.3s ease;
            border: 1px solid #8b5cf6;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
        }

        @keyframes slideInRight {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        .hidden { display: none !important; }

        /* Session Selection */
        .session-selection {
            text-align: center;
            padding: 3rem 0;
        }

        .session-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }

        .session-card {
            background: #374151;
            border: 2px solid #4a5568;
            border-radius: 20px;
            padding: 2rem;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .session-card:hover {
            border-color: #8b5cf6;
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(139, 92, 246, 0.3);
        }

        .session-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
        }

        .session-card h3 {
            color: #f7fafc;
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .session-card p {
            color: #9ca3af;
            margin-bottom: 1.5rem;
            font-size: 1rem;
        }

        .session-card ul {
            list-style: none;
            padding: 0;
            margin-bottom: 2rem;
            text-align: left;
        }

        .session-card li {
            color: #e5e7eb;
            padding: 0.5rem 0;
            font-size: 0.9rem;
        }

        .session-btn {
            background: #8b5cf6;
            border: none;
            border-radius: 12px;
            padding: 1rem 2rem;
            color: white;
            font-weight: 600;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.2s ease;
            width: 100%;
        }

        .session-btn:hover {
            background: #7c3aed;
            transform: translateY(-2px);
        }

        /* Session Header */
        .session-header {
            background: rgba(139, 92, 246, 0.1);
            border: 1px solid rgba(139, 92, 246, 0.3);
            border-radius: 12px;
            padding: 1rem 2rem;
            margin-bottom: 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .session-info h2 {
            color: #8b5cf6;
            margin: 0;
            font-size: 1.5rem;
        }

        .session-info p {
            color: #9ca3af;
            margin: 0;
            font-size: 0.9rem;
        }

        .session-actions {
            display: flex;
            gap: 1rem;
        }

        /* Plan Cards */
        .plan-card {
            background: #374151;
            border: 2px solid #4a5568;
            border-radius: 16px;
            padding: 1.5rem;
            position: relative;
            transition: all 0.3s ease;
        }

        .plan-card:hover {
            border-color: #8b5cf6;
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(139, 92, 246, 0.2);
        }

        .plan-card.popular {
            border-color: #8b5cf6;
            background: linear-gradient(135deg, #374151 0%, rgba(139, 92, 246, 0.1) 100%);
        }

        .popular-badge {
            position: absolute;
            top: -10px;
            right: 20px;
            background: #8b5cf6;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .plan-header {
            text-align: center;
            margin-bottom: 1.5rem;
        }

        .plan-name {
            font-size: 1.3rem;
            font-weight: 600;
            color: #f7fafc;
            margin-bottom: 0.5rem;
        }

        .plan-price {
            display: flex;
            align-items: baseline;
            justify-content: center;
            gap: 0.3rem;
        }

        .price {
            font-size: 2rem;
            font-weight: 700;
            color: #8b5cf6;
        }

        .period {
            color: #9ca3af;
            font-size: 0.9rem;
        }

        .plan-features {
            margin-bottom: 1.5rem;
        }

        .feature {
            padding: 0.5rem 0;
            color: #e5e7eb;
            font-size: 0.9rem;
            border-bottom: 1px solid rgba(75, 85, 99, 0.3);
        }

        .feature:last-child {
            border-bottom: none;
        }

        .plan-btn {
            width: 100%;
            background: #8b5cf6;
            border: none;
            border-radius: 8px;
            padding: 0.75rem;
            color: white;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .plan-btn:hover {
            background: #7c3aed;
            transform: translateY(-2px);
        }

        /* Current Plan Status */
        .current-plan {
            margin-top: 2rem;
            padding: 1.5rem;
            background: rgba(139, 92, 246, 0.1);
            border: 1px solid rgba(139, 92, 246, 0.3);
            border-radius: 12px;
        }

        .plan-status-card {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .plan-current-name {
            font-size: 1.2rem;
            font-weight: 600;
            color: #8b5cf6;
        }

        .plan-expiry, .plan-status {
            color: #9ca3af;
            font-size: 0.9rem;
        }

        .plan-actions {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        /* Claims */
        .claim-section {
            margin-bottom: 2rem;
        }

        .claim-item {
            background: #374151;
            border: 1px solid #4a5568;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .claim-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }

        .claim-id {
            font-weight: 600;
            color: #8b5cf6;
        }

        .claim-status {
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .status-pending {
            background: rgba(245, 158, 11, 0.2);
            color: #f59e0b;
        }

        .status-approved {
            background: rgba(16, 185, 129, 0.2);
            color: #10b981;
        }

        .status-rejected {
            background: rgba(239, 68, 68, 0.2);
            color: #ef4444;
        }

        .claim-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 0.5rem;
            color: #e5e7eb;
            font-size: 0.9rem;
        }

        /* Session-specific styles */
        .session-content {
            animation: fadeIn 0.5s ease;
        }

        .claim-item-insurer {
            background: #374151;
            border: 1px solid #4a5568;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            display: grid;
            grid-template-columns: 1fr auto;
            gap: 1rem;
            align-items: center;
        }

        .claim-actions {
            display: flex;
            gap: 0.5rem;
        }

        .policy-summary {
            background: #374151;
            border: 1px solid #4a5568;
            border-radius: 8px;
            padding: 1.5rem;
        }

        .policy-stats div {
            margin: 0.5rem 0;
            color: #e5e7eb;
        }

        .analytics-section {
            margin-bottom: 2rem;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
        }

        .metric-item {
            text-align: center;
            background: #374151;
            padding: 1rem;
            border-radius: 8px;
        }

        .metric-value {
            font-size: 1.5rem;
            font-weight: 600;
            color: #8b5cf6;
        }

        .metric-label {
            color: #9ca3af;
            font-size: 0.9rem;
        }

        .financial-item {
            background: #374151;
            padding: 1rem;
            border-radius: 8px;
            text-align: center;
        }

        .financial-label {
            color: #9ca3af;
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
        }

        .financial-value {
            font-size: 1.2rem;
            font-weight: 600;
        }

        .financial-value.positive {
            color: #10b981;
        }

        .financial-value.negative {
            color: #ef4444;
        }

        /* Admin styles */
        .admin-stat {
            background: #374151;
            border: 1px solid #4a5568;
            border-radius: 12px;
            padding: 1.5rem;
            text-align: center;
        }

        .stat-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .stat-number {
            font-size: 1.5rem;
            font-weight: 600;
            color: #8b5cf6;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: #9ca3af;
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
        }

        .stat-change {
            font-size: 0.8rem;
            font-weight: 500;
        }

        .stat-change.positive {
            color: #10b981;
        }

        .alert-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 0.75rem;
            background: #374151;
            border-radius: 8px;
            margin-bottom: 0.5rem;
        }

        .alert-level {
            font-size: 1.2rem;
        }

        .alert-message {
            flex: 1;
            color: #e5e7eb;
        }

        .alert-time {
            color: #9ca3af;
            font-size: 0.8rem;
        }

        .users-table {
            background: #374151;
            border-radius: 8px;
            overflow: hidden;
        }

        .table-header {
            display: grid;
            grid-template-columns: 2fr 1fr 1fr 1.5fr 1fr;
            gap: 1rem;
            padding: 1rem;
            background: #4a5568;
            font-weight: 600;
            color: #f7fafc;
        }

        .table-row {
            display: grid;
            grid-template-columns: 2fr 1fr 1fr 1.5fr 1fr;
            gap: 1rem;
            padding: 1rem;
            border-bottom: 1px solid #4a5568;
            align-items: center;
        }

        .status-active {
            color: #10b981;
            font-weight: 500;
        }

        .btn-small {
            background: #8b5cf6;
            border: none;
            border-radius: 4px;
            padding: 0.25rem 0.5rem;
            color: white;
            cursor: pointer;
            margin: 0 0.25rem;
            font-size: 0.8rem;
        }

        .config-section {
            margin-bottom: 2rem;
        }

        .config-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .config-item label {
            flex: 1;
            color: #e5e7eb;
        }

        .plan-config-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .blockchain-metric {
            background: #374151;
            padding: 1rem;
            border-radius: 8px;
            text-align: center;
        }

        .metric-title {
            color: #9ca3af;
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
        }

        .control-buttons {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .logs-container {
            background: #1a1a1a;
            border-radius: 8px;
            padding: 1rem;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
        }

        .log-entry {
            display: grid;
            grid-template-columns: auto auto auto 1fr;
            gap: 1rem;
            padding: 0.5rem 0;
            border-bottom: 1px solid #374151;
            font-size: 0.9rem;
        }

        .log-time {
            color: #9ca3af;
        }

        .log-level {
            font-weight: 600;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.8rem;
        }

        .log-level.info {
            background: rgba(59, 130, 246, 0.2);
            color: #3b82f6;
        }

        .log-level.warning {
            background: rgba(245, 158, 11, 0.2);
            color: #f59e0b;
        }

        .log-service {
            color: #8b5cf6;
            font-weight: 500;
        }

        .log-message {
            color: #e5e7eb;
        }

        .profile-stats {
            background: #374151;
            border-radius: 8px;
            padding: 1rem;
        }

        .stat-item {
            display: flex;
            justify-content: space-between;
            padding: 0.5rem 0;
            border-bottom: 1px solid #4a5568;
        }

        .stat-item:last-child {
            border-bottom: none;
        }

        .stat-label {
            color: #9ca3af;
        }

        .stat-value {
            color: #8b5cf6;
            font-weight: 600;
        }

        .converter-mini {
            background: #374151;
            border-radius: 8px;
            padding: 1rem;
        }

        @media (max-width: 768px) {
            .title { font-size: 3rem; }
            .subtitle { font-size: 1.5rem; }
            .nav-btn { padding: 1rem 2rem; font-size: 1rem; }
            .card { padding: 2rem; }
            .plan-status-card { flex-direction: column; align-items: flex-start; }
            .plan-actions { width: 100%; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">🛡️</div>
            <h1 class="title">Himaya Blockchain</h1>
            <p class="subtitle">🇲🇦 Assurance Véhicule Moderne</p>
            <p class="tagline">Solution blockchain pour l'assurance automobile au Maroc</p>
        </div>

        <!-- Session Selection -->
        <div id="sessionSelection" class="session-selection">
            <h2 style="text-align: center; margin-bottom: 2rem; color: #8b5cf6;">Choisissez votre type de session</h2>
            <div class="session-cards">
                <div class="session-card" data-session="client">
                    <div class="session-icon">👤</div>
                    <h3>Session Client</h3>
                    <p>Gérez vos assurances, véhicules et réclamations</p>
                    <ul>
                        <li>✅ Souscrire aux plans</li>
                        <li>✅ Enregistrer véhicules</li>
                        <li>✅ Soumettre réclamations</li>
                        <li>✅ Suivre les polices</li>
                    </ul>
                    <button class="session-btn">Accéder</button>
                </div>

                <div class="session-card" data-session="insurer">
                    <div class="session-icon">🏢</div>
                    <h3>Session Assureur</h3>
                    <p>Gérez les polices, réclamations et analytics</p>
                    <ul>
                        <li>✅ Approuver réclamations</li>
                        <li>✅ Gérer les polices</li>
                        <li>✅ Analytics avancées</li>
                        <li>✅ Rapports financiers</li>
                    </ul>
                    <button class="session-btn">Accéder</button>
                </div>

                <div class="session-card" data-session="admin">
                    <div class="session-icon">⚙️</div>
                    <h3>Session Admin</h3>
                    <p>Administration complète du système</p>
                    <ul>
                        <li>✅ Gestion utilisateurs</li>
                        <li>✅ Configuration système</li>
                        <li>✅ Audit et logs</li>
                        <li>✅ Contrôle total</li>
                    </ul>
                    <button class="session-btn">Accéder</button>
                </div>
            </div>
        </div>

        <!-- Client Session Navigation -->
        <nav class="nav" id="clientNav" style="display: none;">
            <button class="nav-btn active" data-tab="client-wallet">🔗 Wallet</button>
            <button class="nav-btn" data-tab="client-plans">📋 Plans</button>
            <button class="nav-btn" data-tab="client-vehicles">🚗 Mes Véhicules</button>
            <button class="nav-btn" data-tab="client-claims">🔍 Mes Réclamations</button>
            <button class="nav-btn" data-tab="client-profile">👤 Mon Profil</button>
        </nav>

        <!-- Insurer Session Navigation -->
        <nav class="nav" id="insurerNav" style="display: none;">
            <button class="nav-btn active" data-tab="insurer-dashboard">📊 Dashboard</button>
            <button class="nav-btn" data-tab="insurer-claims">🔍 Réclamations</button>
            <button class="nav-btn" data-tab="insurer-policies">📋 Polices</button>
            <button class="nav-btn" data-tab="insurer-analytics">📈 Analytics</button>
            <button class="nav-btn" data-tab="insurer-reports">📄 Rapports</button>
        </nav>

        <!-- Admin Session Navigation -->
        <nav class="nav" id="adminNav" style="display: none;">
            <button class="nav-btn active" data-tab="admin-overview">🏠 Vue d'ensemble</button>
            <button class="nav-btn" data-tab="admin-users">👥 Utilisateurs</button>
            <button class="nav-btn" data-tab="admin-system">⚙️ Système</button>
            <button class="nav-btn" data-tab="admin-blockchain">⛓️ Blockchain</button>
            <button class="nav-btn" data-tab="admin-logs">📋 Logs</button>
        </nav>

        <div class="status-bar">
            <div class="status-item">
                <div class="status-dot" id="walletStatus"></div>
                <div id="walletText">Wallet: Déconnecté</div>
            </div>
            <div class="status-item">
                <div class="status-dot" id="networkStatus"></div>
                <div id="networkText">Réseau: Offline</div>
            </div>
            <div class="status-item">
                <div class="status-dot" id="contractStatus"></div>
                <div id="contractText">Contrats: Inactifs</div>
            </div>
        </div>

        <main>
            <!-- CLIENT SESSION CONTENT -->
            <div id="clientSession" class="session-content" style="display: none;">
                <div class="session-header">
                    <div class="session-info">
                        <h2>👤 Session Client</h2>
                        <p>Gérez vos assurances et véhicules</p>
                    </div>
                    <div class="session-actions">
                        <button class="btn" id="clientLogoutBtn">🚪 Changer de Session</button>
                    </div>
                </div>

                <!-- Client Wallet Tab -->
                <section id="client-wallet" class="tab-content active">
                    <div class="card">
                        <h2 class="card-title">🔗 Connexion Wallet Client</h2>
                        <div style="text-align: center; margin-bottom: 2rem;">
                            <button class="btn" id="connectWalletBtn">🦊 Connecter MetaMask</button>
                            <button class="btn" id="addNetworkBtn">⚙️ Ajouter Réseau Himaya</button>
                        </div>
                        <div class="grid">
                            <div>
                                <h3 style="color: #8b5cf6; margin-bottom: 1rem; font-size: 1.1rem;">👤 Informations Compte</h3>
                                <div id="accountAddress" class="form-input">Non connecté</div>
                                <div id="accountBalance" class="form-input">0.0000 ETH</div>
                            </div>
                            <div>
                                <h3 style="color: #8b5cf6; margin-bottom: 1rem; font-size: 1.1rem;">⛓️ État Blockchain</h3>
                                <div id="currentBlock" class="form-input">Bloc: 0</div>
                                <div id="gasPrice" class="form-input">Gas: 0 gwei</div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Client Plans Tab -->
                <section id="client-plans" class="tab-content">
                    <div class="card">
                        <h2 class="card-title">📋 Plans d'Assurance Disponibles</h2>
                        <p style="text-align: center; color: #9ca3af; margin-bottom: 2rem;">
                            Choisissez le plan qui correspond le mieux à vos besoins personnels.
                        </p>

                        <div class="grid" style="grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 1.5rem;">
                            <!-- Plan Basique -->
                            <div class="plan-card" data-plan="basic">
                                <div class="plan-header">
                                    <h3 class="plan-name">🥉 Plan Basique</h3>
                                    <div class="plan-price">
                                        <span class="price">0.05 ETH</span>
                                        <span class="period">/mois</span>
                                    </div>
                                </div>
                                <div class="plan-features">
                                    <div class="feature">✅ Couverture accidents</div>
                                    <div class="feature">✅ Vol et vandalisme</div>
                                    <div class="feature">✅ Assistance 24h/7j</div>
                                    <div class="feature">✅ Franchise: 5,000 MAD</div>
                                    <div class="feature">❌ Tous risques</div>
                                    <div class="feature">❌ Véhicule de remplacement</div>
                                </div>
                                <button class="plan-btn" data-plan="basic">Choisir ce plan</button>
                            </div>

                            <!-- Plan Standard -->
                            <div class="plan-card popular" data-plan="standard">
                                <div class="popular-badge">⭐ Populaire</div>
                                <div class="plan-header">
                                    <h3 class="plan-name">🥈 Plan Standard</h3>
                                    <div class="plan-price">
                                        <span class="price">0.08 ETH</span>
                                        <span class="period">/mois</span>
                                    </div>
                                </div>
                                <div class="plan-features">
                                    <div class="feature">✅ Couverture accidents</div>
                                    <div class="feature">✅ Vol et vandalisme</div>
                                    <div class="feature">✅ Assistance 24h/7j</div>
                                    <div class="feature">✅ Franchise: 2,500 MAD</div>
                                    <div class="feature">✅ Tous risques</div>
                                    <div class="feature">✅ Véhicule de remplacement (3 jours)</div>
                                </div>
                                <button class="plan-btn" data-plan="standard">Choisir ce plan</button>
                            </div>

                            <!-- Plan Premium -->
                            <div class="plan-card" data-plan="premium">
                                <div class="plan-header">
                                    <h3 class="plan-name">🥇 Plan Premium</h3>
                                    <div class="plan-price">
                                        <span class="price">0.12 ETH</span>
                                        <span class="period">/mois</span>
                                    </div>
                                </div>
                                <div class="plan-features">
                                    <div class="feature">✅ Couverture accidents</div>
                                    <div class="feature">✅ Vol et vandalisme</div>
                                    <div class="feature">✅ Assistance 24h/7j</div>
                                    <div class="feature">✅ Franchise: 1,000 MAD</div>
                                    <div class="feature">✅ Tous risques</div>
                                    <div class="feature">✅ Véhicule de remplacement (7 jours)</div>
                                    <div class="feature">✅ Couverture internationale</div>
                                    <div class="feature">✅ Réparations premium</div>
                                </div>
                                <button class="plan-btn" data-plan="premium">Choisir ce plan</button>
                            </div>
                        </div>

                        <!-- Current Plan Status -->
                        <div id="currentPlanStatus" class="current-plan" style="display: none;">
                            <h3 style="color: #8b5cf6; margin-bottom: 1rem;">📋 Votre Plan Actuel</h3>
                            <div class="plan-status-card">
                                <div class="plan-info">
                                    <div class="plan-current-name" id="currentPlanName">Plan Standard</div>
                                    <div class="plan-expiry" id="currentPlanExpiry">Expire le: 15/07/2024</div>
                                    <div class="plan-status" id="currentPlanStatus">Statut: Actif</div>
                                </div>
                                <div class="plan-actions">
                                    <button class="btn" id="renewPlanBtn">🔄 Renouveler</button>
                                    <button class="btn" id="changePlanBtn">🔄 Changer de Plan</button>
                                    <button class="btn" style="background: #ef4444;" id="cancelPlanBtn">❌ Annuler</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Client Vehicles Tab -->
                <section id="client-vehicles" class="tab-content">
                    <div class="card">
                        <h2 class="card-title">🚗 Mes Véhicules Assurés</h2>

                        <!-- Add New Vehicle -->
                        <div class="vehicle-section">
                            <h3 style="color: #8b5cf6; margin-bottom: 1rem;">➕ Ajouter un Nouveau Véhicule</h3>
                            <div class="grid">
                                <div>
                                    <select id="vehicleType" class="form-input">
                                        <option value="">Type de véhicule</option>
                                        <option value="car">🚗 Voiture</option>
                                        <option value="motorcycle">🏍️ Moto</option>
                                        <option value="truck">🚚 Camion</option>
                                    </select>
                                    <input type="text" id="vehicleVin" class="form-input" placeholder="Immatriculation">
                                    <input type="text" id="vehicleMake" class="form-input" placeholder="Marque">
                                </div>
                                <div>
                                    <input type="text" id="vehicleModel" class="form-input" placeholder="Modèle">
                                    <input type="number" id="vehicleYear" class="form-input" placeholder="Année">
                                    <select id="vehicleCity" class="form-input">
                                        <option value="">Ville</option>
                                        <option value="casablanca">🏙️ Casablanca</option>
                                        <option value="rabat">🏛️ Rabat</option>
                                        <option value="marrakech">🕌 Marrakech</option>
                                    </select>
                                </div>
                            </div>
                            <div style="text-align: center; margin-top: 2rem;">
                                <button class="btn" id="registerVehicleBtn">📝 Enregistrer Véhicule</button>
                                <div id="planRequiredMessage" style="background: rgba(239, 68, 68, 0.1); border: 1px solid #ef4444; border-radius: 8px; padding: 1rem; margin: 1rem 0; color: #ef4444;">
                                    ⚠️ Vous devez d'abord souscrire à un plan d'assurance pour enregistrer un véhicule.
                                </div>
                            </div>
                        </div>

                        <!-- My Vehicles List -->
                        <div class="vehicle-section" style="margin-top: 2rem;">
                            <h3 style="color: #8b5cf6; margin-bottom: 1rem;">🚗 Mes Véhicules</h3>
                            <div id="vehiclesList">
                                <div style="text-align: center; color: #9ca3af; padding: 2rem;">
                                    Aucun véhicule enregistré
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Client Claims Tab -->
                <section id="client-claims" class="tab-content">
                    <div class="card">
                        <h2 class="card-title">🔍 Mes Réclamations</h2>

                        <!-- New Claim Form -->
                        <div class="claim-section">
                            <h3 style="color: #8b5cf6; margin-bottom: 1rem;">📝 Nouvelle Réclamation</h3>
                            <div class="grid">
                                <div>
                                    <select id="claimType" class="form-input">
                                        <option value="">Type de sinistre</option>
                                        <option value="accident">🚗 Accident de circulation</option>
                                        <option value="theft">🔒 Vol du véhicule</option>
                                        <option value="vandalism">💥 Vandalisme</option>
                                        <option value="fire">🔥 Incendie</option>
                                        <option value="natural">🌪️ Catastrophe naturelle</option>
                                    </select>
                                    <input type="date" id="claimDate" class="form-input" placeholder="Date du sinistre">
                                    <input type="text" id="claimLocation" class="form-input" placeholder="Lieu du sinistre">
                                </div>
                                <div>
                                    <textarea id="claimDescription" class="form-input" rows="4" placeholder="Description détaillée du sinistre..."></textarea>
                                    <input type="number" id="estimatedAmount" class="form-input" placeholder="Montant estimé (MAD)" step="100">
                                </div>
                            </div>
                            <div style="text-align: center; margin-top: 1rem;">
                                <button class="btn" id="submitClaimBtn">📤 Soumettre Réclamation</button>
                            </div>
                        </div>

                        <!-- Claims History -->
                        <div class="claim-section" style="margin-top: 2rem;">
                            <h3 style="color: #8b5cf6; margin-bottom: 1rem;">📋 Historique de mes Réclamations</h3>
                            <div id="claimsHistory">
                                <div style="text-align: center; color: #9ca3af; padding: 2rem;">
                                    Aucune réclamation trouvée
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Client Profile Tab -->
                <section id="client-profile" class="tab-content">
                    <div class="card">
                        <h2 class="card-title">👤 Mon Profil Client</h2>
                        <div class="grid">
                            <div>
                                <h3 style="color: #8b5cf6; margin-bottom: 1rem;">📊 Statistiques</h3>
                                <div class="profile-stats">
                                    <div class="stat-item">
                                        <span class="stat-label">Véhicules assurés:</span>
                                        <span class="stat-value" id="clientVehicleCount">0</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">Réclamations soumises:</span>
                                        <span class="stat-value" id="clientClaimCount">0</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">Plan actuel:</span>
                                        <span class="stat-value" id="clientCurrentPlan">Aucun</span>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <h3 style="color: #8b5cf6; margin-bottom: 1rem;">💱 Convertisseur ETH ⇄ MAD</h3>
                                <div class="converter-mini">
                                    <input type="number" id="ethAmount" class="form-input" placeholder="ETH" step="0.000001">
                                    <div style="text-align: center; margin: 0.5rem 0; color: #8b5cf6;">⇄</div>
                                    <input type="number" id="madAmount" class="form-input" placeholder="MAD" step="0.01">
                                    <div style="text-align: center; margin-top: 1rem;">
                                        <div style="font-size: 1rem; font-weight: 600; color: #e5e7eb;" id="exchangeRate">1 ETH = 35,000.00 MAD</div>
                                        <button class="btn" id="refreshRatesBtn" style="margin-top: 0.5rem;">🔄 Actualiser</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </div>

            <!-- INSURER SESSION CONTENT -->
            <div id="insurerSession" class="session-content" style="display: none;">
                <div class="session-header">
                    <div class="session-info">
                        <h2>🏢 Session Assureur</h2>
                        <p>Gérez les polices et réclamations</p>
                    </div>
                    <div class="session-actions">
                        <button class="btn" id="insurerLogoutBtn">🚪 Changer de Session</button>
                    </div>
                </div>

                <!-- Insurer Dashboard -->
                <section id="insurer-dashboard" class="tab-content active">
                    <div class="card">
                        <h2 class="card-title">📊 Dashboard Assureur</h2>
                        <div class="grid" style="grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));">
                            <div style="text-align: center; background: rgba(139, 92, 246, 0.1); padding: 1.5rem; border-radius: 12px; border: 1px solid rgba(139, 92, 246, 0.2);">
                                <div style="font-size: 2rem; margin-bottom: 0.5rem;">📋</div>
                                <div style="font-size: 1.5rem; font-weight: 600; color: #8b5cf6;">1,247</div>
                                <div style="color: #9ca3af; font-size: 0.9rem;">Polices Actives</div>
                            </div>
                            <div style="text-align: center; background: rgba(16, 185, 129, 0.1); padding: 1.5rem; border-radius: 12px; border: 1px solid rgba(16, 185, 129, 0.2);">
                                <div style="font-size: 2rem; margin-bottom: 0.5rem;">💰</div>
                                <div style="font-size: 1.5rem; font-weight: 600; color: #10b981;">2.85M MAD</div>
                                <div style="color: #9ca3af; font-size: 0.9rem;">Primes Collectées</div>
                            </div>
                            <div style="text-align: center; background: rgba(245, 158, 11, 0.1); padding: 1.5rem; border-radius: 12px; border: 1px solid rgba(245, 158, 11, 0.2);">
                                <div style="font-size: 2rem; margin-bottom: 0.5rem;">🔍</div>
                                <div style="font-size: 1.5rem; font-weight: 600; color: #f59e0b;">23</div>
                                <div style="color: #9ca3af; font-size: 0.9rem;">Réclamations Pendantes</div>
                            </div>
                            <div style="text-align: center; background: rgba(239, 68, 68, 0.1); padding: 1.5rem; border-radius: 12px; border: 1px solid rgba(239, 68, 68, 0.2);">
                                <div style="font-size: 2rem; margin-bottom: 0.5rem;">⚠️</div>
                                <div style="font-size: 1.5rem; font-weight: 600; color: #ef4444;">5</div>
                                <div style="color: #9ca3af; font-size: 0.9rem;">Alertes Système</div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Insurer Claims Management -->
                <section id="insurer-claims" class="tab-content">
                    <div class="card">
                        <h2 class="card-title">🔍 Gestion des Réclamations</h2>

                        <div class="claims-filters" style="margin-bottom: 2rem;">
                            <div class="grid" style="grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));">
                                <select class="form-input">
                                    <option>Tous les statuts</option>
                                    <option>En attente</option>
                                    <option>En cours d'expertise</option>
                                    <option>Approuvées</option>
                                    <option>Rejetées</option>
                                </select>
                                <select class="form-input">
                                    <option>Tous les types</option>
                                    <option>Accident</option>
                                    <option>Vol</option>
                                    <option>Vandalisme</option>
                                </select>
                                <input type="date" class="form-input" placeholder="Date début">
                                <input type="date" class="form-input" placeholder="Date fin">
                            </div>
                        </div>

                        <div class="claims-list">
                            <div class="claim-item-insurer">
                                <div class="claim-header">
                                    <span class="claim-id">#CLM-2024-001</span>
                                    <span class="claim-status status-pending">En attente</span>
                                </div>
                                <div class="claim-details">
                                    <div><strong>Client:</strong> 0x1234...5678</div>
                                    <div><strong>Type:</strong> Accident de circulation</div>
                                    <div><strong>Date:</strong> 15/06/2024</div>
                                    <div><strong>Montant:</strong> 15,000 MAD</div>
                                </div>
                                <div class="claim-actions">
                                    <button class="btn" style="background: #10b981;">✅ Approuver</button>
                                    <button class="btn" style="background: #ef4444;">❌ Rejeter</button>
                                    <button class="btn">👁️ Détails</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Insurer Policies -->
                <section id="insurer-policies" class="tab-content">
                    <div class="card">
                        <h2 class="card-title">📋 Gestion des Polices</h2>
                        <div class="policies-overview">
                            <div class="grid">
                                <div class="policy-summary">
                                    <h3 style="color: #8b5cf6;">Plans Basiques</h3>
                                    <div class="policy-stats">
                                        <div>Actifs: <strong>456</strong></div>
                                        <div>Revenus: <strong>798,000 MAD</strong></div>
                                        <div>Taux sinistres: <strong>2.3%</strong></div>
                                    </div>
                                </div>
                                <div class="policy-summary">
                                    <h3 style="color: #8b5cf6;">Plans Standard</h3>
                                    <div class="policy-stats">
                                        <div>Actifs: <strong>623</strong></div>
                                        <div>Revenus: <strong>1,744,400 MAD</strong></div>
                                        <div>Taux sinistres: <strong>1.8%</strong></div>
                                    </div>
                                </div>
                                <div class="policy-summary">
                                    <h3 style="color: #8b5cf6;">Plans Premium</h3>
                                    <div class="policy-stats">
                                        <div>Actifs: <strong>168</strong></div>
                                        <div>Revenus: <strong>705,600 MAD</strong></div>
                                        <div>Taux sinistres: <strong>1.2%</strong></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Insurer Analytics -->
                <section id="insurer-analytics" class="tab-content">
                    <div class="card">
                        <h2 class="card-title">📈 Analytics Avancées</h2>
                        <div class="analytics-content">
                            <div class="analytics-section">
                                <h3 style="color: #8b5cf6;">Tendances Mensuelles</h3>
                                <div class="chart-placeholder" style="background: #374151; height: 200px; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: #9ca3af;">
                                    📊 Graphique des tendances (à implémenter)
                                </div>
                            </div>
                            <div class="analytics-section" style="margin-top: 2rem;">
                                <h3 style="color: #8b5cf6;">Métriques Clés</h3>
                                <div class="metrics-grid">
                                    <div class="metric-item">
                                        <div class="metric-value">94.2%</div>
                                        <div class="metric-label">Taux de satisfaction</div>
                                    </div>
                                    <div class="metric-item">
                                        <div class="metric-value">2.1 jours</div>
                                        <div class="metric-label">Temps moyen traitement</div>
                                    </div>
                                    <div class="metric-item">
                                        <div class="metric-value">87.5%</div>
                                        <div class="metric-label">Taux d'approbation</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Insurer Reports -->
                <section id="insurer-reports" class="tab-content">
                    <div class="card">
                        <h2 class="card-title">📄 Rapports Financiers</h2>
                        <div class="reports-section">
                            <div class="report-filters" style="margin-bottom: 2rem;">
                                <div class="grid" style="grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));">
                                    <select class="form-input">
                                        <option>Rapport mensuel</option>
                                        <option>Rapport trimestriel</option>
                                        <option>Rapport annuel</option>
                                    </select>
                                    <input type="month" class="form-input">
                                    <button class="btn">📊 Générer Rapport</button>
                                </div>
                            </div>

                            <div class="financial-summary">
                                <h3 style="color: #8b5cf6;">Résumé Financier - Juin 2024</h3>
                                <div class="grid">
                                    <div class="financial-item">
                                        <div class="financial-label">Primes collectées</div>
                                        <div class="financial-value positive">+3,248,000 MAD</div>
                                    </div>
                                    <div class="financial-item">
                                        <div class="financial-label">Sinistres payés</div>
                                        <div class="financial-value negative">-892,000 MAD</div>
                                    </div>
                                    <div class="financial-item">
                                        <div class="financial-label">Bénéfice net</div>
                                        <div class="financial-value positive">+2,356,000 MAD</div>
                                    </div>
                                    <div class="financial-item">
                                        <div class="financial-label">Ratio combiné</div>
                                        <div class="financial-value">72.5%</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </div>

            <!-- ADMIN SESSION CONTENT -->
            <div id="adminSession" class="session-content" style="display: none;">
                <div class="session-header">
                    <div class="session-info">
                        <h2>⚙️ Session Administrateur</h2>
                        <p>Contrôle total du système Himaya</p>
                    </div>
                    <div class="session-actions">
                        <button class="btn" id="adminLogoutBtn">🚪 Changer de Session</button>
                    </div>
                </div>

                <!-- Admin Overview -->
                <section id="admin-overview" class="tab-content active">
                    <div class="card">
                        <h2 class="card-title">🏠 Vue d'ensemble Système</h2>
                        <div class="admin-overview">
                            <div class="grid" style="grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));">
                                <div class="admin-stat">
                                    <div class="stat-icon">👥</div>
                                    <div class="stat-number">5,432</div>
                                    <div class="stat-label">Utilisateurs Total</div>
                                    <div class="stat-change positive">+12% ce mois</div>
                                </div>
                                <div class="admin-stat">
                                    <div class="stat-icon">🏢</div>
                                    <div class="stat-number">23</div>
                                    <div class="stat-label">Assureurs Actifs</div>
                                    <div class="stat-change positive">+2 nouveaux</div>
                                </div>
                                <div class="admin-stat">
                                    <div class="stat-icon">⛓️</div>
                                    <div class="stat-number">99.9%</div>
                                    <div class="stat-label">Uptime Blockchain</div>
                                    <div class="stat-change positive">Excellent</div>
                                </div>
                                <div class="admin-stat">
                                    <div class="stat-icon">💰</div>
                                    <div class="stat-number">12.3M MAD</div>
                                    <div class="stat-label">Volume Total</div>
                                    <div class="stat-change positive">+18% ce mois</div>
                                </div>
                            </div>
                        </div>

                        <div class="system-alerts" style="margin-top: 2rem;">
                            <h3 style="color: #8b5cf6;">🚨 Alertes Système</h3>
                            <div class="alert-item">
                                <span class="alert-level warning">⚠️</span>
                                <span class="alert-message">Utilisation mémoire élevée sur le nœud 3</span>
                                <span class="alert-time">Il y a 15 min</span>
                            </div>
                            <div class="alert-item">
                                <span class="alert-level info">ℹ️</span>
                                <span class="alert-message">Mise à jour système programmée pour demain</span>
                                <span class="alert-time">Il y a 2h</span>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Admin Users -->
                <section id="admin-users" class="tab-content">
                    <div class="card">
                        <h2 class="card-title">👥 Gestion des Utilisateurs</h2>

                        <div class="user-controls" style="margin-bottom: 2rem;">
                            <div class="grid" style="grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));">
                                <input type="text" class="form-input" placeholder="Rechercher utilisateur...">
                                <select class="form-input">
                                    <option>Tous les rôles</option>
                                    <option>Clients</option>
                                    <option>Assureurs</option>
                                    <option>Admins</option>
                                </select>
                                <select class="form-input">
                                    <option>Tous les statuts</option>
                                    <option>Actifs</option>
                                    <option>Suspendus</option>
                                    <option>Bannis</option>
                                </select>
                                <button class="btn">🔍 Filtrer</button>
                            </div>
                        </div>

                        <div class="users-table">
                            <div class="table-header">
                                <div>Utilisateur</div>
                                <div>Rôle</div>
                                <div>Statut</div>
                                <div>Dernière connexion</div>
                                <div>Actions</div>
                            </div>
                            <div class="table-row">
                                <div>0x1234...5678</div>
                                <div>Client</div>
                                <div><span class="status-active">Actif</span></div>
                                <div>Il y a 2h</div>
                                <div>
                                    <button class="btn-small">👁️</button>
                                    <button class="btn-small">⚠️</button>
                                    <button class="btn-small">🚫</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Admin System -->
                <section id="admin-system" class="tab-content">
                    <div class="card">
                        <h2 class="card-title">⚙️ Configuration Système</h2>

                        <div class="system-config">
                            <div class="config-section">
                                <h3 style="color: #8b5cf6;">🔧 Paramètres Généraux</h3>
                                <div class="config-item">
                                    <label>Frais de transaction (%)</label>
                                    <input type="number" class="form-input" value="2.5" step="0.1">
                                </div>
                                <div class="config-item">
                                    <label>Délai d'expiration des plans (jours)</label>
                                    <input type="number" class="form-input" value="30">
                                </div>
                                <div class="config-item">
                                    <label>Limite de réclamations par mois</label>
                                    <input type="number" class="form-input" value="3">
                                </div>
                            </div>

                            <div class="config-section" style="margin-top: 2rem;">
                                <h3 style="color: #8b5cf6;">💰 Configuration des Plans</h3>
                                <div class="plan-config">
                                    <div class="plan-config-item">
                                        <span>Plan Basique</span>
                                        <input type="number" class="form-input" value="0.05" step="0.01">
                                        <span>ETH/mois</span>
                                    </div>
                                    <div class="plan-config-item">
                                        <span>Plan Standard</span>
                                        <input type="number" class="form-input" value="0.08" step="0.01">
                                        <span>ETH/mois</span>
                                    </div>
                                    <div class="plan-config-item">
                                        <span>Plan Premium</span>
                                        <input type="number" class="form-input" value="0.12" step="0.01">
                                        <span>ETH/mois</span>
                                    </div>
                                </div>
                            </div>

                            <div style="text-align: center; margin-top: 2rem;">
                                <button class="btn">💾 Sauvegarder Configuration</button>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Admin Blockchain -->
                <section id="admin-blockchain" class="tab-content">
                    <div class="card">
                        <h2 class="card-title">⛓️ Monitoring Blockchain</h2>

                        <div class="blockchain-status">
                            <div class="grid">
                                <div class="blockchain-metric">
                                    <div class="metric-title">Dernier Bloc</div>
                                    <div class="metric-value" id="adminCurrentBlock">2,847,392</div>
                                </div>
                                <div class="blockchain-metric">
                                    <div class="metric-title">Prix Gas Moyen</div>
                                    <div class="metric-value">23.5 gwei</div>
                                </div>
                                <div class="blockchain-metric">
                                    <div class="metric-title">Transactions/sec</div>
                                    <div class="metric-value">15.2</div>
                                </div>
                                <div class="blockchain-metric">
                                    <div class="metric-title">Nœuds Actifs</div>
                                    <div class="metric-value">8/8</div>
                                </div>
                            </div>
                        </div>

                        <div class="blockchain-controls" style="margin-top: 2rem;">
                            <h3 style="color: #8b5cf6;">🔧 Contrôles Blockchain</h3>
                            <div class="control-buttons">
                                <button class="btn">🔄 Redémarrer Nœud</button>
                                <button class="btn">📊 Synchroniser</button>
                                <button class="btn">🔍 Audit Complet</button>
                                <button class="btn" style="background: #ef4444;">🚨 Mode Maintenance</button>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Admin Logs -->
                <section id="admin-logs" class="tab-content">
                    <div class="card">
                        <h2 class="card-title">📋 Logs Système</h2>

                        <div class="logs-filters" style="margin-bottom: 2rem;">
                            <div class="grid" style="grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));">
                                <select class="form-input">
                                    <option>Tous les niveaux</option>
                                    <option>Info</option>
                                    <option>Warning</option>
                                    <option>Error</option>
                                    <option>Critical</option>
                                </select>
                                <select class="form-input">
                                    <option>Tous les services</option>
                                    <option>Blockchain</option>
                                    <option>API</option>
                                    <option>Database</option>
                                </select>
                                <input type="datetime-local" class="form-input">
                                <button class="btn">🔍 Filtrer Logs</button>
                            </div>
                        </div>

                        <div class="logs-container">
                            <div class="log-entry">
                                <span class="log-time">2024-06-15 14:32:15</span>
                                <span class="log-level info">INFO</span>
                                <span class="log-service">API</span>
                                <span class="log-message">Nouvelle connexion utilisateur: 0x1234...5678</span>
                            </div>
                            <div class="log-entry">
                                <span class="log-time">2024-06-15 14:31:42</span>
                                <span class="log-level warning">WARN</span>
                                <span class="log-service">Blockchain</span>
                                <span class="log-message">Prix du gas élevé détecté: 45.2 gwei</span>
                            </div>
                            <div class="log-entry">
                                <span class="log-time">2024-06-15 14:30:18</span>
                                <span class="log-level info">INFO</span>
                                <span class="log-service">Database</span>
                                <span class="log-message">Sauvegarde automatique terminée avec succès</span>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </main>

            <!-- Plans Tab -->
            <section id="plans" class="tab-content">
                <div class="card">
                    <h2 class="card-title">📋 Plans d'Assurance Himaya</h2>
                    <p style="text-align: center; color: #9ca3af; margin-bottom: 2rem;">
                        Choisissez le plan qui correspond le mieux à vos besoins. Tous les plans incluent une couverture blockchain sécurisée.
                    </p>

                    <div class="grid" style="grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 1.5rem;">
                        <!-- Plan Basique -->
                        <div class="plan-card" data-plan="basic">
                            <div class="plan-header">
                                <h3 class="plan-name">🥉 Plan Basique</h3>
                                <div class="plan-price">
                                    <span class="price">0.05 ETH</span>
                                    <span class="period">/mois</span>
                                </div>
                            </div>
                            <div class="plan-features">
                                <div class="feature">✅ Couverture accidents</div>
                                <div class="feature">✅ Vol et vandalisme</div>
                                <div class="feature">✅ Assistance 24h/7j</div>
                                <div class="feature">✅ Franchise: 5,000 MAD</div>
                                <div class="feature">❌ Tous risques</div>
                                <div class="feature">❌ Véhicule de remplacement</div>
                            </div>
                            <button class="plan-btn" data-plan="basic">Choisir ce plan</button>
                        </div>

                        <!-- Plan Standard -->
                        <div class="plan-card popular" data-plan="standard">
                            <div class="popular-badge">⭐ Populaire</div>
                            <div class="plan-header">
                                <h3 class="plan-name">🥈 Plan Standard</h3>
                                <div class="plan-price">
                                    <span class="price">0.08 ETH</span>
                                    <span class="period">/mois</span>
                                </div>
                            </div>
                            <div class="plan-features">
                                <div class="feature">✅ Couverture accidents</div>
                                <div class="feature">✅ Vol et vandalisme</div>
                                <div class="feature">✅ Assistance 24h/7j</div>
                                <div class="feature">✅ Franchise: 2,500 MAD</div>
                                <div class="feature">✅ Tous risques</div>
                                <div class="feature">✅ Véhicule de remplacement (3 jours)</div>
                            </div>
                            <button class="plan-btn" data-plan="standard">Choisir ce plan</button>
                        </div>

                        <!-- Plan Premium -->
                        <div class="plan-card" data-plan="premium">
                            <div class="plan-header">
                                <h3 class="plan-name">🥇 Plan Premium</h3>
                                <div class="plan-price">
                                    <span class="price">0.12 ETH</span>
                                    <span class="period">/mois</span>
                                </div>
                            </div>
                            <div class="plan-features">
                                <div class="feature">✅ Couverture accidents</div>
                                <div class="feature">✅ Vol et vandalisme</div>
                                <div class="feature">✅ Assistance 24h/7j</div>
                                <div class="feature">✅ Franchise: 1,000 MAD</div>
                                <div class="feature">✅ Tous risques</div>
                                <div class="feature">✅ Véhicule de remplacement (7 jours)</div>
                                <div class="feature">✅ Couverture internationale</div>
                                <div class="feature">✅ Réparations premium</div>
                            </div>
                            <button class="plan-btn" data-plan="premium">Choisir ce plan</button>
                        </div>
                    </div>

                    <!-- Current Plan Status -->
                    <div id="currentPlanStatus" class="current-plan" style="display: none;">
                        <h3 style="color: #8b5cf6; margin-bottom: 1rem;">📋 Votre Plan Actuel</h3>
                        <div class="plan-status-card">
                            <div class="plan-info">
                                <div class="plan-current-name" id="currentPlanName">Plan Standard</div>
                                <div class="plan-expiry" id="currentPlanExpiry">Expire le: 15/07/2024</div>
                                <div class="plan-status" id="currentPlanStatus">Statut: Actif</div>
                            </div>
                            <div class="plan-actions">
                                <button class="btn" id="renewPlanBtn">🔄 Renouveler</button>
                                <button class="btn" id="changePlanBtn">🔄 Changer de Plan</button>
                                <button class="btn" style="background: #ef4444;" id="cancelPlanBtn">❌ Annuler</button>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Insurance Tab -->
            <section id="insurance" class="tab-content">
                <div class="card">
                    <h2 class="card-title">🚗 Assurance Véhicule</h2>
                    <div class="grid">
                        <div>
                            <select id="vehicleType" class="form-input">
                                <option value="">Type de véhicule</option>
                                <option value="car">🚗 Voiture</option>
                                <option value="motorcycle">🏍️ Moto</option>
                                <option value="truck">🚚 Camion</option>
                            </select>
                            <input type="text" id="vehicleVin" class="form-input" placeholder="Immatriculation">
                            <input type="text" id="vehicleMake" class="form-input" placeholder="Marque">
                        </div>
                        <div>
                            <input type="text" id="vehicleModel" class="form-input" placeholder="Modèle">
                            <input type="number" id="vehicleYear" class="form-input" placeholder="Année">
                            <select id="vehicleCity" class="form-input">
                                <option value="">Ville</option>
                                <option value="casablanca">🏙️ Casablanca</option>
                                <option value="rabat">🏛️ Rabat</option>
                                <option value="marrakech">🕌 Marrakech</option>
                            </select>
                        </div>
                    </div>
                    <div style="text-align: center; margin-top: 2rem;">
                        <button class="btn" id="registerVehicleBtn">📝 Enregistrer Véhicule</button>
                        <div id="planRequiredMessage" style="background: rgba(239, 68, 68, 0.1); border: 1px solid #ef4444; border-radius: 8px; padding: 1rem; margin: 1rem 0; color: #ef4444;">
                            ⚠️ Vous devez d'abord souscrire à un plan d'assurance pour enregistrer un véhicule.
                        </div>
                    </div>
                </div>
            </section>

            <!-- Claims Tab -->
            <section id="claims" class="tab-content">
                <div class="card">
                    <h2 class="card-title">🔍 Gestion des Réclamations</h2>

                    <!-- New Claim Form -->
                    <div class="claim-section">
                        <h3 style="color: #8b5cf6; margin-bottom: 1rem;">📝 Nouvelle Réclamation</h3>
                        <div class="grid">
                            <div>
                                <select id="claimType" class="form-input">
                                    <option value="">Type de sinistre</option>
                                    <option value="accident">🚗 Accident de circulation</option>
                                    <option value="theft">🔒 Vol du véhicule</option>
                                    <option value="vandalism">💥 Vandalisme</option>
                                    <option value="fire">🔥 Incendie</option>
                                    <option value="natural">🌪️ Catastrophe naturelle</option>
                                </select>
                                <input type="date" id="claimDate" class="form-input" placeholder="Date du sinistre">
                                <input type="text" id="claimLocation" class="form-input" placeholder="Lieu du sinistre">
                            </div>
                            <div>
                                <textarea id="claimDescription" class="form-input" rows="4" placeholder="Description détaillée du sinistre..."></textarea>
                                <input type="number" id="estimatedAmount" class="form-input" placeholder="Montant estimé (MAD)" step="100">
                            </div>
                        </div>
                        <div style="text-align: center; margin-top: 1rem;">
                            <button class="btn" id="submitClaimBtn">📤 Soumettre Réclamation</button>
                        </div>
                    </div>

                    <!-- Claims History -->
                    <div class="claim-section" style="margin-top: 2rem;">
                        <h3 style="color: #8b5cf6; margin-bottom: 1rem;">📋 Historique des Réclamations</h3>
                        <div id="claimsHistory">
                            <!-- Sample claims will be populated here -->
                            <div class="claim-item">
                                <div class="claim-header">
                                    <span class="claim-id">#CLM-2024-001</span>
                                    <span class="claim-status status-pending">En cours</span>
                                </div>
                                <div class="claim-details">
                                    <div><strong>Type:</strong> Accident de circulation</div>
                                    <div><strong>Date:</strong> 15/06/2024</div>
                                    <div><strong>Montant:</strong> 15,000 MAD</div>
                                    <div><strong>Statut:</strong> Expertise en cours</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Dashboard Tab -->
            <section id="dashboard" class="tab-content">
                <div class="card">
                    <h2 class="card-title">📊 Dashboard Analytics</h2>
                    <div style="text-align: center; margin-bottom: 2rem;">
                        <button class="btn role-btn active" data-role="client">👤 Client</button>
                        <button class="btn role-btn" data-role="insurer">🏢 Assureur</button>
                        <button class="btn role-btn" data-role="admin">⚙️ Admin</button>
                    </div>
                    <div class="grid">
                        <div style="text-align: center; background: rgba(139, 92, 246, 0.1); padding: 1.5rem; border-radius: 12px; border: 1px solid rgba(139, 92, 246, 0.2);">
                            <div style="font-size: 2rem; margin-bottom: 0.5rem;">📋</div>
                            <div style="font-size: 1.5rem; font-weight: 600; color: #8b5cf6;" id="totalPolicies">1,247</div>
                            <div style="color: #9ca3af; font-size: 0.9rem;">Polices Actives</div>
                        </div>
                        <div style="text-align: center; background: rgba(16, 185, 129, 0.1); padding: 1.5rem; border-radius: 12px; border: 1px solid rgba(16, 185, 129, 0.2);">
                            <div style="font-size: 2rem; margin-bottom: 0.5rem;">💰</div>
                            <div style="font-size: 1.5rem; font-weight: 600; color: #10b981;" id="totalPremiums">2.85M MAD</div>
                            <div style="color: #9ca3af; font-size: 0.9rem;">Primes Collectées</div>
                        </div>
                        <div style="text-align: center; background: rgba(239, 68, 68, 0.1); padding: 1.5rem; border-radius: 12px; border: 1px solid rgba(239, 68, 68, 0.2);">
                            <div style="font-size: 2rem; margin-bottom: 0.5rem;">🔍</div>
                            <div style="font-size: 1.5rem; font-weight: 600; color: #ef4444;" id="totalClaims">23</div>
                            <div style="color: #9ca3af; font-size: 0.9rem;">Réclamations</div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Converter Tab -->
            <section id="converter" class="tab-content">
                <div class="card">
                    <h2 class="card-title">💱 Convertisseur ETH ⇄ MAD</h2>
                    <div class="grid">
                        <div>
                            <h3 style="color: #8b5cf6; margin-bottom: 1rem; font-size: 1.1rem;">Ethereum (ETH)</h3>
                            <input type="number" id="ethAmount" class="form-input" placeholder="0.000000" step="0.000001">
                        </div>
                        <div>
                            <h3 style="color: #8b5cf6; margin-bottom: 1rem; font-size: 1.1rem;">Dirham Marocain (MAD)</h3>
                            <input type="number" id="madAmount" class="form-input" placeholder="0.00" step="0.01">
                        </div>
                    </div>
                    <div style="text-align: center; margin: 2rem 0;">
                        <div style="font-size: 2rem; margin-bottom: 1rem; color: #8b5cf6;">⇄</div>
                        <div style="font-size: 1.3rem; font-weight: 600; color: #e5e7eb;" id="exchangeRate">1 ETH = 35,000.00 MAD</div>
                        <div style="color: #9ca3af; font-size: 0.9rem;" id="lastUpdate">Dernière mise à jour: maintenant</div>
                    </div>
                    <div style="text-align: center;">
                        <button class="btn" id="refreshRatesBtn">🔄 Actualiser Taux</button>
                        <button class="btn" id="swapCurrenciesBtn">⇄ Inverser</button>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/web3@4.2.0/dist/web3.min.js"></script>
    <script>
        // Application State
        const HimayaApp = {
            web3: null,
            account: null,
            connected: false,
            currentSession: null,
            currentTab: 'client-wallet',
            exchangeRate: 35000,
            currentPlan: null,
            planExpiry: null,
            vehicles: [],
            claims: []
        };

        // Insurance Plans Configuration
        const INSURANCE_PLANS = {
            basic: {
                name: 'Plan Basique',
                price: 0.05,
                priceMAD: 1750,
                features: ['Couverture accidents', 'Vol et vandalisme', 'Assistance 24h/7j', 'Franchise: 5,000 MAD'],
                duration: 30 // days
            },
            standard: {
                name: 'Plan Standard',
                price: 0.08,
                priceMAD: 2800,
                features: ['Couverture accidents', 'Vol et vandalisme', 'Assistance 24h/7j', 'Franchise: 2,500 MAD', 'Tous risques', 'Véhicule de remplacement (3 jours)'],
                duration: 30
            },
            premium: {
                name: 'Plan Premium',
                price: 0.12,
                priceMAD: 4200,
                features: ['Couverture accidents', 'Vol et vandalisme', 'Assistance 24h/7j', 'Franchise: 1,000 MAD', 'Tous risques', 'Véhicule de remplacement (7 jours)', 'Couverture internationale', 'Réparations premium'],
                duration: 30
            }
        };

        // Utility Functions
        function showNotification(message, type = 'info') {
            const existing = document.querySelectorAll('.notification');
            existing.forEach(n => n.remove());

            const notification = document.createElement('div');
            notification.className = 'notification';
            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 4000);
        }

        function updateStatus(element, connected, text) {
            const statusDot = document.getElementById(element);
            const statusText = document.getElementById(element.replace('Status', 'Text'));

            if (statusDot) {
                statusDot.className = `status-dot ${connected ? 'connected' : ''}`;
            }
            if (statusText) {
                statusText.textContent = text;
            }
        }

        // Session Management
        function initSessions() {
            const sessionCards = document.querySelectorAll('.session-card');

            sessionCards.forEach(card => {
                card.addEventListener('click', () => {
                    const sessionType = card.getAttribute('data-session');
                    switchToSession(sessionType);
                });
            });

            // Logout buttons
            document.getElementById('clientLogoutBtn').addEventListener('click', () => switchToSession(null));
            document.getElementById('insurerLogoutBtn').addEventListener('click', () => switchToSession(null));
            document.getElementById('adminLogoutBtn').addEventListener('click', () => switchToSession(null));
        }

        function switchToSession(sessionType) {
            // Hide session selection
            document.getElementById('sessionSelection').style.display = sessionType ? 'none' : 'block';

            // Hide all sessions
            document.getElementById('clientSession').style.display = 'none';
            document.getElementById('insurerSession').style.display = 'none';
            document.getElementById('adminSession').style.display = 'none';

            // Hide all navs
            document.getElementById('clientNav').style.display = 'none';
            document.getElementById('insurerNav').style.display = 'none';
            document.getElementById('adminNav').style.display = 'none';

            if (sessionType) {
                // Show selected session
                document.getElementById(`${sessionType}Session`).style.display = 'block';
                document.getElementById(`${sessionType}Nav`).style.display = 'flex';

                HimayaApp.currentSession = sessionType;

                // Set default tab for each session
                const defaultTabs = {
                    client: 'client-wallet',
                    insurer: 'insurer-dashboard',
                    admin: 'admin-overview'
                };

                switchTab(defaultTabs[sessionType]);
                showNotification(`🎉 Session ${sessionType} activée`);
            } else {
                HimayaApp.currentSession = null;
                showNotification('👋 Session fermée');
            }
        }

        // Tab Management
        function initTabs() {
            const navItems = document.querySelectorAll('.nav-btn');

            navItems.forEach(item => {
                item.addEventListener('click', (e) => {
                    const tabId = item.getAttribute('data-tab');
                    switchTab(tabId);
                });
            });
        }

        function switchTab(tabId) {
            if (!HimayaApp.currentSession) return;

            // Update nav buttons
            const currentNav = document.querySelector(`#${HimayaApp.currentSession}Nav`);
            if (currentNav) {
                currentNav.querySelectorAll('.nav-btn').forEach(btn => btn.classList.remove('active'));
                const targetNavBtn = currentNav.querySelector(`[data-tab="${tabId}"]`);
                if (targetNavBtn) {
                    targetNavBtn.classList.add('active');
                }
            }

            // Update tab contents
            const currentSession = document.getElementById(`${HimayaApp.currentSession}Session`);
            if (currentSession) {
                currentSession.querySelectorAll('.tab-content').forEach(tab => tab.classList.remove('active'));
                const targetTab = document.getElementById(tabId);
                if (targetTab) {
                    targetTab.classList.add('active');
                    HimayaApp.currentTab = tabId;
                }
            }
        }

        // Wallet Connection
        async function connectWallet() {
            if (!window.ethereum) {
                showNotification('❌ MetaMask non détecté');
                return;
            }

            try {
                showNotification('🔄 Connexion en cours...');

                const accounts = await window.ethereum.request({
                    method: 'eth_requestAccounts'
                });

                if (accounts.length > 0) {
                    HimayaApp.account = accounts[0];
                    HimayaApp.connected = true;
                    HimayaApp.web3 = new Web3(window.ethereum);

                    const balance = await HimayaApp.web3.eth.getBalance(HimayaApp.account);
                    const ethBalance = HimayaApp.web3.utils.fromWei(balance, 'ether');

                    document.getElementById('accountAddress').textContent =
                        `${HimayaApp.account.slice(0, 6)}...${HimayaApp.account.slice(-4)}`;
                    document.getElementById('accountBalance').textContent =
                        `${parseFloat(ethBalance).toFixed(4)} ETH`;

                    updateStatus('walletStatus', true, 'Wallet: Connecté ✅');
                    updateStatus('networkStatus', true, 'Réseau: Connecté ✅');

                    document.getElementById('connectWalletBtn').textContent = '✅ Connecté';
                    showNotification('🎉 Wallet connecté avec succès');

                    await loadBlockchainInfo();
                }
            } catch (error) {
                showNotification('❌ Échec de la connexion');
            }
        }

        // Load Blockchain Info
        async function loadBlockchainInfo() {
            if (!HimayaApp.web3) return;

            try {
                const latestBlock = await HimayaApp.web3.eth.getBlockNumber();
                const gasPrice = await HimayaApp.web3.eth.getGasPrice();
                const gasPriceGwei = HimayaApp.web3.utils.fromWei(gasPrice, 'gwei');

                document.getElementById('currentBlock').textContent = `Bloc: ${latestBlock}`;
                document.getElementById('gasPrice').textContent = `Gas: ${parseFloat(gasPriceGwei).toFixed(2)} gwei`;

                updateStatus('contractStatus', true, 'Contrats: Actifs ✅');
            } catch (error) {
                console.error('Failed to load blockchain info:', error);
            }
        }

        // Plan Management
        async function subscribeToPlan(planType) {
            if (!HimayaApp.connected) {
                showNotification('⚠️ Veuillez d\'abord connecter votre wallet');
                return;
            }

            const plan = INSURANCE_PLANS[planType];
            if (!plan) return;

            showNotification(`🔄 Souscription au ${plan.name}...`);

            try {
                // Simulate blockchain transaction
                await new Promise(resolve => setTimeout(resolve, 3000));

                HimayaApp.currentPlan = planType;
                HimayaApp.planExpiry = new Date(Date.now() + plan.duration * 24 * 60 * 60 * 1000);

                updatePlanStatus();
                showNotification(`🎉 Souscription au ${plan.name} réussie!`);

                // Hide plan required message
                document.getElementById('planRequiredMessage').style.display = 'none';

            } catch (error) {
                showNotification('❌ Échec de la souscription');
            }
        }

        function updatePlanStatus() {
            const statusDiv = document.getElementById('currentPlanStatus');
            if (HimayaApp.currentPlan) {
                const plan = INSURANCE_PLANS[HimayaApp.currentPlan];
                document.getElementById('currentPlanName').textContent = plan.name;
                document.getElementById('currentPlanExpiry').textContent =
                    `Expire le: ${HimayaApp.planExpiry.toLocaleDateString('fr-FR')}`;

                const daysLeft = Math.ceil((HimayaApp.planExpiry - new Date()) / (1000 * 60 * 60 * 24));
                const statusText = daysLeft > 0 ? 'Actif' : 'Expiré';
                const statusColor = daysLeft > 0 ? '#10b981' : '#ef4444';

                document.getElementById('currentPlanStatus').innerHTML =
                    `<span style="color: ${statusColor}">Statut: ${statusText} (${daysLeft} jours restants)</span>`;

                statusDiv.style.display = 'block';
            } else {
                statusDiv.style.display = 'none';
            }
        }

        async function renewPlan() {
            if (!HimayaApp.currentPlan) return;

            showNotification('🔄 Renouvellement en cours...');

            setTimeout(() => {
                const plan = INSURANCE_PLANS[HimayaApp.currentPlan];
                HimayaApp.planExpiry = new Date(Date.now() + plan.duration * 24 * 60 * 60 * 1000);
                updatePlanStatus();
                showNotification('🎉 Plan renouvelé avec succès');
            }, 2000);
        }

        async function cancelPlan() {
            if (!confirm('Êtes-vous sûr de vouloir annuler votre plan d\'assurance?')) return;

            showNotification('🔄 Annulation en cours...');

            setTimeout(() => {
                HimayaApp.currentPlan = null;
                HimayaApp.planExpiry = null;
                updatePlanStatus();
                document.getElementById('planRequiredMessage').style.display = 'block';
                showNotification('✅ Plan annulé');
            }, 1500);
        }

        // Vehicle Registration
        async function registerVehicle() {
            if (!HimayaApp.connected) {
                showNotification('⚠️ Veuillez d\'abord connecter votre wallet');
                return;
            }

            if (!HimayaApp.currentPlan) {
                showNotification('⚠️ Vous devez d\'abord souscrire à un plan d\'assurance');
                return;
            }

            const vehicleData = {
                type: document.getElementById('vehicleType').value,
                vin: document.getElementById('vehicleVin').value,
                make: document.getElementById('vehicleMake').value,
                model: document.getElementById('vehicleModel').value,
                year: document.getElementById('vehicleYear').value,
                city: document.getElementById('vehicleCity').value
            };

            if (!vehicleData.type || !vehicleData.vin || !vehicleData.make) {
                showNotification('⚠️ Veuillez remplir tous les champs');
                return;
            }

            showNotification('🔄 Enregistrement du véhicule...');

            setTimeout(() => {
                HimayaApp.vehicles.push({
                    id: Date.now(),
                    ...vehicleData,
                    registeredAt: new Date(),
                    plan: HimayaApp.currentPlan
                });

                showNotification('🎉 Véhicule enregistré avec succès');

                // Clear form
                document.getElementById('vehicleType').value = '';
                document.getElementById('vehicleVin').value = '';
                document.getElementById('vehicleMake').value = '';
                document.getElementById('vehicleModel').value = '';
                document.getElementById('vehicleYear').value = '';
                document.getElementById('vehicleCity').value = '';
            }, 2000);
        }

        // Claims Management
        async function submitClaim() {
            if (!HimayaApp.connected) {
                showNotification('⚠️ Veuillez d\'abord connecter votre wallet');
                return;
            }

            if (!HimayaApp.currentPlan) {
                showNotification('⚠️ Vous devez avoir un plan d\'assurance actif');
                return;
            }

            const claimData = {
                type: document.getElementById('claimType').value,
                date: document.getElementById('claimDate').value,
                location: document.getElementById('claimLocation').value,
                description: document.getElementById('claimDescription').value,
                amount: document.getElementById('estimatedAmount').value
            };

            if (!claimData.type || !claimData.date || !claimData.description) {
                showNotification('⚠️ Veuillez remplir tous les champs obligatoires');
                return;
            }

            showNotification('🔄 Soumission de la réclamation...');

            setTimeout(() => {
                const claimId = `CLM-${new Date().getFullYear()}-${String(HimayaApp.claims.length + 1).padStart(3, '0')}`;

                HimayaApp.claims.push({
                    id: claimId,
                    ...claimData,
                    submittedAt: new Date(),
                    status: 'pending',
                    plan: HimayaApp.currentPlan
                });

                showNotification('🎉 Réclamation soumise avec succès');

                // Clear form
                document.getElementById('claimType').value = '';
                document.getElementById('claimDate').value = '';
                document.getElementById('claimLocation').value = '';
                document.getElementById('claimDescription').value = '';
                document.getElementById('estimatedAmount').value = '';

                updateClaimsHistory();
            }, 2000);
        }

        function updateClaimsHistory() {
            const historyDiv = document.getElementById('claimsHistory');
            if (HimayaApp.claims.length === 0) {
                historyDiv.innerHTML = '<div style="text-align: center; color: #9ca3af; padding: 2rem;">Aucune réclamation trouvée</div>';
                return;
            }

            historyDiv.innerHTML = HimayaApp.claims.map(claim => `
                <div class="claim-item">
                    <div class="claim-header">
                        <span class="claim-id">#${claim.id}</span>
                        <span class="claim-status status-${claim.status}">
                            ${claim.status === 'pending' ? 'En cours' :
                              claim.status === 'approved' ? 'Approuvée' : 'Rejetée'}
                        </span>
                    </div>
                    <div class="claim-details">
                        <div><strong>Type:</strong> ${claim.type}</div>
                        <div><strong>Date:</strong> ${new Date(claim.date).toLocaleDateString('fr-FR')}</div>
                        <div><strong>Montant:</strong> ${claim.amount} MAD</div>
                        <div><strong>Statut:</strong> ${claim.status === 'pending' ? 'Expertise en cours' : 'Traité'}</div>
                    </div>
                </div>
            `).join('');
        }

        // Currency Conversion
        function convertETHToMAD(ethValue) {
            if (!ethValue || ethValue <= 0) {
                document.getElementById('madAmount').value = '';
                return;
            }
            const madValue = parseFloat(ethValue) * HimayaApp.exchangeRate;
            document.getElementById('madAmount').value = madValue.toFixed(2);
        }

        function convertMADToETH(madValue) {
            if (!madValue || madValue <= 0) {
                document.getElementById('ethAmount').value = '';
                return;
            }
            const ethValue = parseFloat(madValue) / HimayaApp.exchangeRate;
            document.getElementById('ethAmount').value = ethValue.toFixed(6);
        }

        function refreshExchangeRates() {
            showNotification('🔄 Actualisation des taux...');

            setTimeout(() => {
                const variation = (Math.random() - 0.5) * 2000;
                HimayaApp.exchangeRate = 35000 + variation;

                document.getElementById('exchangeRate').textContent =
                    `1 ETH = ${HimayaApp.exchangeRate.toLocaleString('fr-FR', {minimumFractionDigits: 2})} MAD`;

                document.getElementById('lastUpdate').textContent =
                    `Dernière mise à jour: ${new Date().toLocaleTimeString('fr-FR')}`;

                showNotification('✅ Taux mis à jour');
            }, 1500);
        }

        function swapCurrencies() {
            const ethInput = document.getElementById('ethAmount');
            const madInput = document.getElementById('madAmount');

            const ethValue = ethInput.value;
            const madValue = madInput.value;

            ethInput.value = madValue ? (parseFloat(madValue) / HimayaApp.exchangeRate).toFixed(6) : '';
            madInput.value = ethValue ? (parseFloat(ethValue) * HimayaApp.exchangeRate).toFixed(2) : '';

            showNotification('🔄 Devises inversées');
        }

        // Role Management
        function switchRole(role) {
            document.querySelectorAll('.role-btn').forEach(btn => {
                btn.classList.remove('active');
                if (btn.getAttribute('data-role') === role) {
                    btn.classList.add('active');
                }
            });

            const stats = {
                client: { totalPolicies: '3', totalPremiums: '3.6 ETH', totalClaims: '1' },
                insurer: { totalPolicies: '1,247', totalPremiums: '2.85M MAD', totalClaims: '23' },
                admin: { totalPolicies: '5,432', totalPremiums: '12.3M MAD', totalClaims: '89' }
            };

            const roleStats = stats[role];
            if (roleStats) {
                document.getElementById('totalPolicies').textContent = roleStats.totalPolicies;
                document.getElementById('totalPremiums').textContent = roleStats.totalPremiums;
                document.getElementById('totalClaims').textContent = roleStats.totalClaims;
            }

            showNotification(`🔄 Rôle changé: ${role}`);
        }

        // Event Listeners
        function initEventListeners() {
            document.getElementById('connectWalletBtn').addEventListener('click', connectWallet);
            document.getElementById('registerVehicleBtn').addEventListener('click', registerVehicle);

            document.getElementById('ethAmount').addEventListener('input', (e) => {
                convertETHToMAD(e.target.value);
            });

            document.getElementById('madAmount').addEventListener('input', (e) => {
                convertMADToETH(e.target.value);
            });

            document.getElementById('refreshRatesBtn').addEventListener('click', refreshExchangeRates);
            document.getElementById('swapCurrenciesBtn').addEventListener('click', swapCurrencies);

            document.querySelectorAll('.role-btn').forEach(btn => {
                btn.addEventListener('click', () => {
                    const role = btn.getAttribute('data-role');
                    switchRole(role);
                });
            });

            // Plan buttons
            document.querySelectorAll('.plan-btn').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    const planType = e.target.getAttribute('data-plan');
                    subscribeToPlan(planType);
                });
            });

            // Plan management buttons
            document.getElementById('renewPlanBtn').addEventListener('click', renewPlan);
            document.getElementById('changePlanBtn').addEventListener('click', () => {
                // Switch to plans tab
                document.querySelector('[data-tab="plans"]').click();
            });
            document.getElementById('cancelPlanBtn').addEventListener('click', cancelPlan);

            // Claims
            document.getElementById('submitClaimBtn').addEventListener('click', submitClaim);

            console.log('✅ All event listeners initialized');
        }

        // Initialize Application
        function initApp() {
            console.log('🚀 Himaya Blockchain Multi-Session DApp Loading...');

            initSessions();
            initTabs();
            initEventListeners();

            setTimeout(() => {
                showNotification('🛡️ Bienvenue sur Himaya Blockchain - Choisissez votre session');
            }, 1000);

            console.log('✅ Himaya Blockchain Multi-Session DApp initialized successfully');
        }

        // Initialize when DOM is loaded
        document.addEventListener('DOMContentLoaded', initApp);

        console.log('🛡️ HIMAYA BLOCKCHAIN - NOUVELLE INTERFACE RÉVOLUTIONNAIRE CHARGÉE!');
        console.log('🇲🇦 PRÊT POUR LE MARCHÉ MAROCAIN!');
    </script>
</body>
</html>
