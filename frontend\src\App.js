import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { Box } from '@mui/material';

import { useAuth } from './contexts/AuthContext';
import Navbar from './components/Layout/Navbar';
import Sidebar from './components/Layout/Sidebar';
import LoadingSpinner from './components/Common/LoadingSpinner';

// Pages
import Login from './pages/Auth/Login';
import Register from './pages/Auth/Register';
import Dashboard from './pages/Dashboard/Dashboard';
import VehicleList from './pages/Vehicles/VehicleList';
import VehicleRegister from './pages/Vehicles/VehicleRegister';
import VehicleDetail from './pages/Vehicles/VehicleDetail';
import PolicyList from './pages/Policies/PolicyList';
import PolicyDetail from './pages/Policies/PolicyDetail';
import ClaimList from './pages/Claims/ClaimList';
import ClaimSubmit from './pages/Claims/ClaimSubmit';
import ClaimDetail from './pages/Claims/ClaimDetail';
import Profile from './pages/Profile/Profile';

function App() {
  const { user, loading } = useAuth();

  if (loading) {
    return <LoadingSpinner />;
  }

  if (!user) {
    return (
      <Routes>
        <Route path="/login" element={<Login />} />
        <Route path="/register" element={<Register />} />
        <Route path="*" element={<Navigate to="/login" replace />} />
      </Routes>
    );
  }

  return (
    <Box sx={{ display: 'flex' }}>
      <Navbar />
      <Sidebar />
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: 3,
          mt: 8,
          ml: { sm: 30 }, // Account for sidebar width
        }}
      >
        <Routes>
          <Route path="/" element={<Dashboard />} />
          <Route path="/dashboard" element={<Dashboard />} />
          
          {/* Vehicle Routes */}
          <Route path="/vehicles" element={<VehicleList />} />
          <Route path="/vehicles/register" element={<VehicleRegister />} />
          <Route path="/vehicles/:id" element={<VehicleDetail />} />
          
          {/* Policy Routes */}
          <Route path="/policies" element={<PolicyList />} />
          <Route path="/policies/:id" element={<PolicyDetail />} />
          
          {/* Claim Routes */}
          <Route path="/claims" element={<ClaimList />} />
          <Route path="/claims/submit" element={<ClaimSubmit />} />
          <Route path="/claims/:id" element={<ClaimDetail />} />
          
          {/* Profile */}
          <Route path="/profile" element={<Profile />} />
          
          {/* Catch all */}
          <Route path="*" element={<Navigate to="/dashboard" replace />} />
        </Routes>
      </Box>
    </Box>
  );
}

export default App;
