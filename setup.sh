#!/bin/bash

echo "🚀 Setting up Vehicle Insurance Claims DApp..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed. Please install Docker first."
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    print_error "Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed. Please install Node.js (v16 or higher) first."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d 'v' -f 2 | cut -d '.' -f 1)
if [ "$NODE_VERSION" -lt 16 ]; then
    print_error "Node.js version 16 or higher is required. Current version: $NODE_VERSION"
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    print_error "npm is not installed. Please install npm first."
    exit 1
fi

print_status "All prerequisites are installed!"

# Create necessary directories
print_status "Creating necessary directories..."
mkdir -p blockchain/data/node1
mkdir -p blockchain/data/node2
mkdir -p backend/uploads
mkdir -p abi

# Check for genesis file
if [ ! -f blockchain/genesis.json ]; then
    print_warning "genesis.json not found. Creating a default genesis file..."
    cat > blockchain/genesis.json << 'EOF'
{
  "config": {
    "chainId": 1337,
    "homesteadBlock": 0,
    "eip150Block": 0,
    "eip155Block": 0,
    "eip158Block": 0,
    "byzantiumBlock": 0,
    "constantinopleBlock": 0,
    "petersburgBlock": 0,
    "istanbulBlock": 0,
    "berlinBlock": 0,
    "londonBlock": 0
  },
  "alloc": {
    "fe3b557e8fb62b89f4916b721be55ceb828dbd73": {
      "balance": "0x200000000000000000000000000000000000000000000000000000000000000"
    },
    "627306090abaB3A6e1400e9345bC60c78a8BEf57": {
      "balance": "0x200000000000000000000000000000000000000000000000000000000000000"
    },
    "f17f52151EbEF6C7334FAD080c5704D77216b732": {
      "balance": "0x200000000000000000000000000000000000000000000000000000000000000"
    }
  },
  "coinbase": "0x0000000000000000000000000000000000000000",
  "difficulty": "0x1",
  "extraData": "",
  "gasLimit": "0x8000000",
  "nonce": "0x0000000000000042",
  "mixhash": "0x0000000000000000000000000000000000000000000000000000000000000000",
  "parentHash": "0x0000000000000000000000000000000000000000000000000000000000000000",
  "timestamp": "0x00"
}
EOF
    print_status "Default genesis.json created."
fi

# Check for blockchain init script
if [ ! -f blockchain/init.sh ]; then
    print_warning "init.sh not found. Creating default initialization script..."
    cat > blockchain/init.sh << 'EOF'
#!/bin/sh

# Initialize Geth with genesis if datadir is empty
if [ ! -d /root/.ethereum/geth ]; then
    echo "Initializing geth with genesis block..."
    geth init --datadir /root/.ethereum /genesis.json
fi

# Start geth with appropriate flags
exec geth --datadir /root/.ethereum \
    --http --http.addr 0.0.0.0 --http.port 8545 --http.corsdomain "*" \
    --ws --ws.addr 0.0.0.0 --ws.port 8546 --ws.origins "*" \
    --http.api eth,net,web3,personal,miner,admin,debug,txpool \
    --ws.api eth,net,web3,personal,miner,admin,debug,txpool \
    --networkid 1337 \
    --mine --miner.threads 1 --miner.etherbase ****************************************** \
    --unlock ******************************************,******************************************,****************************************** \
    --password /password.txt \
    --allow-insecure-unlock \
    --rpc.allow-unprotected-txs \
    --nodiscover \
    --syncmode full
EOF
    chmod +x blockchain/init.sh
    print_status "Default init.sh created and made executable."
fi

# Check for password file
if [ ! -f blockchain/password.txt ]; then
    print_warning "password.txt not found. Creating empty password file..."
    touch blockchain/password.txt
    print_status "Empty password.txt file created. This is for development only."
fi

# Copy environment file
print_status "Setting up environment files..."
if [ ! -f backend/.env ]; then
    cp backend/.env.example backend/.env
    print_status "Created backend/.env file. Please review and update if needed."
else
    print_warning "backend/.env already exists. Skipping..."
fi

# Start Docker services
print_status "Starting Hyperledger Besu network and databases..."
docker-compose up -d

# Check if services are running
print_status "Checking if services are running..."
sleep 10

if ! docker ps | grep -q "geth-private-enterprise"; then
    print_error "Blockchain container failed to start. Checking logs..."
    docker logs geth-private-enterprise
    print_warning "Attempting to continue setup. You may need to restart services later."
fi

if ! docker ps | grep -q "insurance-mongodb"; then
    print_error "MongoDB container failed to start. Checking logs..."
    docker logs insurance-mongodb
    print_warning "Attempting to continue setup. You may need to restart services later."
fi

if ! docker ps | grep -q "insurance-redis"; then
    print_error "Redis container failed to start. Checking logs..."
    docker logs insurance-redis
    print_warning "Attempting to continue setup. You may need to restart services later."
fi

# Wait for blockchain to be ready
print_status "Waiting for blockchain to be ready..."
RETRY=0
MAX_RETRY=10
BLOCKCHAIN_READY=false

while [ $RETRY -lt $MAX_RETRY ]; do
    if curl -s -X POST -H "Content-Type: application/json" --data '{"jsonrpc":"2.0","method":"eth_blockNumber","params":[],"id":1}' http://localhost:8545 | grep -q "result"; then
        BLOCKCHAIN_READY=true
        break
    fi
    RETRY=$((RETRY+1))
    print_warning "Waiting for blockchain (attempt $RETRY/$MAX_RETRY)..."
    sleep 3
done

if [ "$BLOCKCHAIN_READY" = false ]; then
    print_error "Blockchain is not responding. Setup may not complete successfully."
    print_warning "Continuing with setup. You may need to restart services later."
fi

# Install blockchain dependencies
print_status "Installing blockchain dependencies..."
cd blockchain
npm install || { print_error "Failed to install blockchain dependencies"; exit 1; }
cd ..

# Install backend dependencies
print_status "Installing backend dependencies..."
cd backend
npm install || { print_error "Failed to install backend dependencies"; exit 1; }
cd ..

# Install frontend dependencies
print_status "Installing frontend dependencies..."
cd frontend
npm install || { print_error "Failed to install frontend dependencies"; exit 1; }
cd ..

# Deploy smart contracts if blockchain is ready
if [ "$BLOCKCHAIN_READY" = true ]; then
    print_status "Deploying smart contracts to blockchain network..."
    cd blockchain
    npm run deploy
    DEPLOY_STATUS=$?
    cd ..
    
    if [ $DEPLOY_STATUS -ne 0 ]; then
        print_error "Smart contract deployment failed."
        print_warning "You'll need to deploy contracts manually after fixing the issues."
    else
        print_status "Smart contracts deployed successfully!"
        
        # Update backend environment with contract addresses
        print_status "Updating backend configuration with deployed contract addresses..."
        if [ -f deployments.json ]; then
            VEHICLE_REGISTRY_ADDRESS=$(node -p "JSON.parse(require('fs').readFileSync('deployments.json', 'utf8')).contracts.VehicleRegistry.address")
            INSURANCE_POLICY_ADDRESS=$(node -p "JSON.parse(require('fs').readFileSync('deployments.json', 'utf8')).contracts.InsurancePolicy.address")
            CLAIM_MANAGER_ADDRESS=$(node -p "JSON.parse(require('fs').readFileSync('deployments.json', 'utf8')).contracts.ClaimManager.address")
            
            # Update .env file
            sed -i "s/VEHICLE_REGISTRY_ADDRESS=.*/VEHICLE_REGISTRY_ADDRESS=$VEHICLE_REGISTRY_ADDRESS/" backend/.env
            sed -i "s/INSURANCE_POLICY_ADDRESS=.*/INSURANCE_POLICY_ADDRESS=$INSURANCE_POLICY_ADDRESS/" backend/.env
            sed -i "s/CLAIM_MANAGER_ADDRESS=.*/CLAIM_MANAGER_ADDRESS=$CLAIM_MANAGER_ADDRESS/" backend/.env
            
            print_status "Contract addresses updated in backend/.env"
        else
            print_error "Deployment file not found. Contract deployment may have failed."
        fi
    fi
else
    print_warning "Skipping smart contract deployment because blockchain is not responding."
    print_warning "You'll need to deploy contracts manually after fixing the blockchain issues."
fi

print_status "Setup completed! 🎉"
echo ""
echo "📋 Next steps:"
echo "1. Start the backend server:"
echo "   cd backend && npm start"
echo ""
echo "2. In a new terminal, start the frontend:"
echo "   cd frontend && npm start"
echo ""
echo "3. Open your browser and go to: http://localhost:3000"
echo ""
echo "📊 Services running:"
echo "   - Blockchain: http://localhost:8545"
echo "   - MongoDB: localhost:27017"
echo "   - Redis: localhost:6379"
echo ""
echo "🔑 Test accounts (with empty passwords in blockchain/password.txt):"
echo "   - Deployer: ******************************************"
echo "   - Insurer 1: ******************************************"
echo "   - Insurer 2: ******************************************"
echo ""
echo "💡 For development, use these accounts with the private keys in backend/.env"
echo ""
echo "💡 To stop all services: docker-compose down"
echo ""
echo "📘 If you encounter any issues, please refer to the troubleshooting section in README.md"