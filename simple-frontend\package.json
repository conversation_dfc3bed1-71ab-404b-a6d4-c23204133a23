{"name": "himaya-blockchain-dapp", "version": "1.0.0", "description": "🇲🇦 Himaya Blockchain - Moroccan Vehicle Insurance DApp with comprehensive testing suite", "main": "app.js", "scripts": {"test": "node tests/cli-test-runner.js", "test:verbose": "node tests/cli-test-runner.js --verbose", "test:quick": "node tests/cli-test-runner.js --quick", "test:ui": "echo '🎨 Open http://localhost:8080/test-runner.html for UI tests'", "test:browser": "python -m http.server 8080 & sleep 2 && echo '🌐 Browser tests available at http://localhost:8080/test-runner.html'", "start": "python -m http.server 8080", "dev": "python -m http.server 8080", "serve": "python -m http.server 8080", "build": "echo '📦 Building Himaya Blockchain DApp...' && npm run test", "lint": "echo '🔍 Linting Himaya Blockchain code...'", "format": "echo '✨ Formatting Himaya Blockchain code...'", "validate": "npm run test && echo '✅ Himaya Blockchain validation complete'", "deploy": "node deploy-contract.js", "deploy-old": "echo '🚀 Deploying Himaya Blockchain DApp...' && npm run validate", "docs": "echo '📚 Generating documentation for Himaya Blockchain...'", "clean": "echo '🧹 Cleaning build artifacts...'", "install-deps": "echo '📦 Installing dependencies for Himaya Blockchain...'", "setup": "npm run install-deps && npm run test", "demo": "npm start & sleep 2 && echo '🛡️ Himaya Blockchain Demo: http://localhost:8080'", "test:all": "npm run test && npm run test:ui", "health": "node -e \"console.log('🛡️ Himaya Blockchain Health Check: ✅ OK')\"", "version": "echo '🇲🇦 Himaya Blockchain v1.0.0 - Moroccan Insurance DApp'"}, "keywords": ["blockchain", "insurance", "morocco", "web3", "dapp", "ethereum", "vehicle-insurance", "himaya", "moroccan", "mad-currency", "french", "arabic", "purple-theme", "modern-design"], "author": {"name": "Himaya Blockchain Team", "email": "<EMAIL>", "url": "https://himaya-blockchain.ma"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/himaya-blockchain/moroccan-insurance-dapp.git"}, "bugs": {"url": "https://github.com/himaya-blockchain/moroccan-insurance-dapp/issues"}, "homepage": "https://himaya-blockchain.ma", "engines": {"node": ">=14.0.0", "npm": ">=6.0.0"}, "dependencies": {"web3": "^4.2.0", "solc": "^0.8.21"}, "devDependencies": {}, "peerDependencies": {}, "optionalDependencies": {}, "config": {"port": 8080, "host": "localhost", "network": "private", "chain_id": 1337}, "himaya": {"version": "1.0.0", "name": "<PERSON>aya Blockchain", "arabic_name": "حماية بلوك تشين", "country": "Morocco", "currency": "MAD", "languages": ["fr", "en", "ar"], "theme": "purple", "features": ["vehicle-insurance", "blockchain-integration", "wallet-connectivity", "moroccan-localization", "mad-currency-support", "multi-language", "modern-ui", "role-based-access", "real-time-analytics", "comprehensive-testing"], "testing": {"ui_tests": "tests/ui-functionality-tests.js", "blockchain_tests": "tests/blockchain-integration-tests.js", "moroccan_tests": "tests/moroccan-features-tests.js", "test_runner": "tests/run-all-tests.js", "cli_runner": "tests/cli-test-runner.js", "browser_runner": "test-runner.html"}, "architecture": {"frontend": "Vanilla JavaScript + Modern CSS", "blockchain": "Ethereum + Web3.js", "styling": "Modern Material Design", "localization": "Multi-language support", "testing": "Comprehensive test suite"}, "compliance": {"morocco": "ACAPS ready", "accessibility": "WCAG 2.1 AA", "security": "Web3 best practices", "performance": "Optimized for mobile"}}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"], "files": ["index.html", "app.js", "css/", "js/", "tests/", "test-runner.html", "manifest.json", "sw.js"], "directories": {"test": "tests", "lib": "js", "doc": "docs"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/himaya-blockchain"}, "contributors": [{"name": "Himaya Development Team", "email": "<EMAIL>", "role": "Core Development"}], "maintainers": [{"name": "<PERSON>aya Blockchain", "email": "<EMAIL>"}]}