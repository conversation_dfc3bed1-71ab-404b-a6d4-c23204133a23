/**
 * Morocco Theme and Localization Styles
 * Moroccan market-specific styling with cultural elements
 */

/* Moroccan Color Palette */
:root {
    /* Moroccan Flag Colors */
    --morocco-red: #c1272d;
    --morocco-green: #006233;
    --morocco-gold: #ffd700;
    
    /* Traditional Moroccan Colors */
    --berber-blue: #1e40af;
    --sahara-orange: #f97316;
    --atlas-green: #059669;
    --medina-brown: #92400e;
    --mint-tea-green: #10b981;
    
    /* Currency Colors */
    --mad-primary: #006233;
    --mad-secondary: #10b981;
    --eth-primary: #627eea;
}

/* Currency Converter Styles */
.currency-converter {
    background: linear-gradient(135deg, var(--morocco-green) 0%, var(--mint-tea-green) 100%);
    border-radius: var(--radius-2xl);
    padding: 32px;
    margin: 24px 0;
    color: var(--white);
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-xl);
}

.currency-converter::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="moroccanPattern" width="20" height="20" patternUnits="userSpaceOnUse"><path d="M10,0 L20,10 L10,20 L0,10 Z" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23moroccanPattern)"/></svg>');
    opacity: 0.3;
}

.currency-converter > * {
    position: relative;
    z-index: 1;
}

.converter-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    border-bottom: 2px solid rgba(255, 255, 255, 0.2);
    padding-bottom: 16px;
}

.converter-header h3 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.converter-content {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: var(--radius-xl);
    padding: 24px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.converter-row {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 20px;
    align-items: end;
    margin-bottom: 20px;
}

.currency-input {
    position: relative;
}

.currency-input label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.9);
    font-size: 0.875rem;
}

.currency-input input {
    width: 100%;
    padding: 16px 50px 16px 16px;
    background: rgba(255, 255, 255, 0.9);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: var(--radius-lg);
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--gray-800);
    transition: all 0.3s ease;
}

.currency-input input:focus {
    outline: none;
    border-color: var(--morocco-gold);
    box-shadow: 0 0 0 3px rgba(255, 215, 0, 0.3);
    background: var(--white);
}

.currency-symbol {
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    font-weight: 700;
    color: var(--gray-600);
    font-size: 0.875rem;
    background: rgba(0, 0, 0, 0.1);
    padding: 4px 8px;
    border-radius: var(--radius-md);
}

.converter-arrow {
    font-size: 1.5rem;
    color: var(--morocco-gold);
    text-align: center;
    font-weight: bold;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.exchange-rate-info {
    background: rgba(0, 0, 0, 0.2);
    border-radius: var(--radius-lg);
    padding: 16px;
    text-align: center;
}

.exchange-rate-info p {
    margin: 4px 0;
    color: rgba(255, 255, 255, 0.9);
}

.rate-update {
    font-size: 0.875rem;
    opacity: 0.8;
}

/* Regulatory Notice */
.regulatory-notice {
    background: linear-gradient(135deg, var(--morocco-red) 0%, #dc2626 100%);
    border-radius: var(--radius-xl);
    padding: 24px;
    margin: 24px 0;
    color: var(--white);
    border: 2px solid rgba(255, 255, 255, 0.2);
    box-shadow: var(--shadow-lg);
}

.notice-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
    border-bottom: 2px solid rgba(255, 255, 255, 0.2);
    padding-bottom: 12px;
}

.notice-icon {
    font-size: 2rem;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.notice-header h4 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 700;
}

.notice-content p {
    margin: 8px 0;
    font-weight: 500;
}

/* Morocco Features */
.morocco-features {
    background: linear-gradient(135deg, var(--sahara-orange) 0%, #f97316 100%);
    border-radius: var(--radius-2xl);
    padding: 32px;
    margin: 32px 0;
    color: var(--white);
    position: relative;
    overflow: hidden;
}

.morocco-features::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="islamicPattern" width="30" height="30" patternUnits="userSpaceOnUse"><circle cx="15" cy="15" r="2" fill="rgba(255,255,255,0.1)"/><path d="M15,5 L20,15 L15,25 L10,15 Z" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23islamicPattern)"/></svg>');
    opacity: 0.4;
}

.morocco-features > * {
    position: relative;
    z-index: 1;
}

.morocco-features h3 {
    text-align: center;
    margin-bottom: 24px;
    font-size: 1.75rem;
    font-weight: 800;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.feature-card {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-xl);
    padding: 24px;
    text-align: center;
    transition: all 0.3s ease;
}

.feature-card:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: translateY(-4px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.feature-icon {
    font-size: 2.5rem;
    margin-bottom: 12px;
    display: block;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.feature-card h4 {
    margin: 0 0 8px 0;
    font-size: 1.1rem;
    font-weight: 700;
}

.feature-card p {
    margin: 0;
    opacity: 0.9;
    font-size: 0.9rem;
    line-height: 1.4;
}

/* Payment Methods */
.payment-methods {
    background: rgba(15, 23, 42, 0.8);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(148, 163, 184, 0.3);
    border-radius: var(--radius-xl);
    padding: 24px;
    margin: 20px 0;
}

.payment-methods h4 {
    color: var(--text-primary);
    margin-bottom: 16px;
    text-align: center;
    font-size: 1.2rem;
}

.payment-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
}

.payment-method {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(148, 163, 184, 0.2);
    border-radius: var(--radius-lg);
    color: var(--text-secondary);
    transition: all 0.3s ease;
}

.payment-method:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: var(--morocco-green);
    color: var(--text-primary);
}

.payment-icon {
    font-size: 1.5rem;
    flex-shrink: 0;
}

/* Dual Currency Display */
.price-dual {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
}

.price-eth {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--eth-primary);
}

.price-mad {
    font-size: 1rem;
    font-weight: 600;
    color: var(--mad-primary);
    background: rgba(0, 98, 51, 0.1);
    padding: 4px 8px;
    border-radius: var(--radius-md);
    border: 1px solid rgba(0, 98, 51, 0.2);
}

/* Moroccan Typography */
.arabic-support {
    font-family: 'Noto Sans Arabic', 'Inter', sans-serif;
    direction: rtl;
    text-align: right;
}

.french-text {
    font-family: 'Inter', 'Segoe UI', sans-serif;
}

/* Moroccan Cultural Elements */
.moroccan-pattern {
    background-image: 
        radial-gradient(circle at 25% 25%, var(--morocco-gold) 2px, transparent 2px),
        radial-gradient(circle at 75% 75%, var(--morocco-red) 2px, transparent 2px);
    background-size: 40px 40px;
    background-position: 0 0, 20px 20px;
}

.islamic-geometric {
    background: 
        linear-gradient(45deg, transparent 30%, var(--morocco-green) 30%, var(--morocco-green) 70%, transparent 70%),
        linear-gradient(-45deg, transparent 30%, var(--morocco-gold) 30%, var(--morocco-gold) 70%, transparent 70%);
    background-size: 20px 20px;
    opacity: 0.1;
}

/* Responsive Design for Morocco Features */
@media (max-width: 768px) {
    .converter-row {
        grid-template-columns: 1fr;
        gap: 16px;
    }
    
    .converter-arrow {
        transform: rotate(90deg);
        margin: 8px 0;
    }
    
    .currency-input input {
        padding: 14px 45px 14px 14px;
        font-size: 1rem;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }
    
    .payment-grid {
        grid-template-columns: 1fr;
    }
    
    .morocco-features,
    .currency-converter {
        padding: 24px;
        margin: 20px 0;
    }
}

/* RTL Support for Arabic */
[dir="rtl"] {
    .converter-row {
        direction: rtl;
    }
    
    .currency-symbol {
        right: auto;
        left: 16px;
    }
    
    .payment-method {
        flex-direction: row-reverse;
    }
}

/* Morocco Flag Animation */
.morocco-flag {
    display: inline-block;
    width: 30px;
    height: 20px;
    background: var(--morocco-red);
    position: relative;
    border-radius: 2px;
    margin-right: 8px;
}

.morocco-flag::before {
    content: '★';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--morocco-green);
    font-size: 12px;
    font-weight: bold;
}

/* Enhanced Tooltips for Morocco */
.tooltip.morocco-tooltip {
    background: linear-gradient(135deg, var(--morocco-green) 0%, var(--mint-tea-green) 100%);
    color: var(--white);
    border: 1px solid rgba(255, 255, 255, 0.2);
    font-weight: 500;
}

.tooltip.morocco-tooltip .tooltip-arrow {
    background: var(--morocco-green);
    border-color: rgba(255, 255, 255, 0.2);
}
