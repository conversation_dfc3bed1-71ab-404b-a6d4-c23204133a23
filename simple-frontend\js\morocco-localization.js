/**
 * Morocco Localization and Currency Converter for SecureShield Insurance
 * Adapts the platform for Moroccan market with MAD currency support
 */

class MoroccoLocalization {
    constructor() {
        this.exchangeRates = {
            ETH_TO_MAD: 0, // Will be fetched from API
            USD_TO_MAD: 10.12, // Approximate rate
            EUR_TO_MAD: 10.85  // Approximate rate
        };
        
        this.moroccanCities = [
            'Casablanca', 'Rabat', 'Marrakech', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 
            '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', 'Mohammed<PERSON>',
            '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>'
        ];
        
        this.moroccanInsuranceCompanies = [
            'Wafa Assurance', 'Saham Assurance', 'Atlanta Assurance', 
            'AXA Assurance Maroc', 'Zurich Assurance Maroc', 'RMA Watanya',
            'MAMDA', 'MCMA', 'CAT', 'La Marocaine Vie'
        ];
        
        this.init();
    }

    async init() {
        await this.fetchExchangeRates();
        this.setupCurrencyConverter();
        this.localizeContent();
        this.addMoroccanFeatures();
    }

    async fetchExchangeRates() {
        try {
            // Fetch real-time ETH to MAD rate
            const response = await fetch('https://api.coingecko.com/api/v3/simple/price?ids=ethereum&vs_currencies=mad');
            const data = await response.json();
            
            if (data.ethereum && data.ethereum.mad) {
                this.exchangeRates.ETH_TO_MAD = data.ethereum.mad;
            } else {
                // Fallback: ETH to USD then to MAD
                const ethUsdResponse = await fetch('https://api.coingecko.com/api/v3/simple/price?ids=ethereum&vs_currencies=usd');
                const ethUsdData = await ethUsdResponse.json();
                if (ethUsdData.ethereum && ethUsdData.ethereum.usd) {
                    this.exchangeRates.ETH_TO_MAD = ethUsdData.ethereum.usd * this.exchangeRates.USD_TO_MAD;
                }
            }
            
            console.log('✅ Exchange rates updated:', this.exchangeRates);
            this.updateCurrencyDisplay();
            
        } catch (error) {
            console.error('❌ Failed to fetch exchange rates:', error);
            // Use fallback rates
            this.exchangeRates.ETH_TO_MAD = 35000; // Approximate fallback
        }
    }

    setupCurrencyConverter() {
        // Create currency converter widget
        const converterHTML = `
            <div class="currency-converter" id="currencyConverter">
                <div class="converter-header">
                    <h3>💱 Convertisseur ETH ⇄ MAD</h3>
                    <button class="btn-icon" onclick="moroccoLocalization.refreshRates()" aria-label="Actualiser les taux">
                        🔄
                    </button>
                </div>
                
                <div class="converter-content">
                    <div class="converter-row">
                        <div class="currency-input">
                            <label for="ethAmount">Ethereum (ETH)</label>
                            <input type="number" 
                                   id="ethAmount" 
                                   placeholder="0.00" 
                                   step="0.001" 
                                   min="0"
                                   oninput="moroccoLocalization.convertETHToMAD(this.value)">
                            <span class="currency-symbol">ETH</span>
                        </div>
                        
                        <div class="converter-arrow">⇄</div>
                        
                        <div class="currency-input">
                            <label for="madAmount">Dirham Marocain (MAD)</label>
                            <input type="number" 
                                   id="madAmount" 
                                   placeholder="0.00" 
                                   step="0.01" 
                                   min="0"
                                   oninput="moroccoLocalization.convertMADToETH(this.value)">
                            <span class="currency-symbol">MAD</span>
                        </div>
                    </div>
                    
                    <div class="exchange-rate-info">
                        <p>📊 Taux de change: <strong id="currentRate">1 ETH = ${this.formatMAD(this.exchangeRates.ETH_TO_MAD)}</strong></p>
                        <p class="rate-update">Dernière mise à jour: <span id="lastUpdate">${new Date().toLocaleString('fr-MA')}</span></p>
                    </div>
                </div>
            </div>
        `;

        // Add converter to the page
        const container = document.querySelector('.container');
        const firstCard = container.querySelector('.card');
        firstCard.insertAdjacentHTML('afterend', converterHTML);
    }

    convertETHToMAD(ethValue) {
        if (!ethValue || ethValue <= 0) {
            document.getElementById('madAmount').value = '';
            return;
        }
        
        const madValue = parseFloat(ethValue) * this.exchangeRates.ETH_TO_MAD;
        document.getElementById('madAmount').value = madValue.toFixed(2);
        
        // Update any price displays on the page
        this.updatePriceDisplays();
    }

    convertMADToETH(madValue) {
        if (!madValue || madValue <= 0) {
            document.getElementById('ethAmount').value = '';
            return;
        }
        
        const ethValue = parseFloat(madValue) / this.exchangeRates.ETH_TO_MAD;
        document.getElementById('ethAmount').value = ethValue.toFixed(6);
        
        // Update any price displays on the page
        this.updatePriceDisplays();
    }

    formatMAD(amount) {
        return new Intl.NumberFormat('fr-MA', {
            style: 'currency',
            currency: 'MAD',
            minimumFractionDigits: 2
        }).format(amount);
    }

    formatETH(amount) {
        return `${parseFloat(amount).toFixed(6)} ETH`;
    }

    updateCurrencyDisplay() {
        const rateElement = document.getElementById('currentRate');
        const updateElement = document.getElementById('lastUpdate');
        
        if (rateElement) {
            rateElement.textContent = `1 ETH = ${this.formatMAD(this.exchangeRates.ETH_TO_MAD)}`;
        }
        
        if (updateElement) {
            updateElement.textContent = new Date().toLocaleString('fr-MA');
        }
    }

    async refreshRates() {
        UI.showLoading('Actualisation des taux de change...');
        await this.fetchExchangeRates();
        UI.hideLoading();
        UI.showNotification('Taux de change mis à jour', 'success');
    }

    localizeContent() {
        // Update page title and headers for Moroccan market
        document.title = 'SecureShield Assurance | Protection Véhicules Maroc';
        
        // Update main header
        const headerTitle = document.querySelector('.header h1');
        if (headerTitle) {
            headerTitle.innerHTML = '🛡️ SecureShield Assurance Maroc';
        }
        
        const headerSubtitle = document.querySelector('.header p');
        if (headerSubtitle) {
            headerSubtitle.textContent = 'Plateforme d\'assurance véhicules basée sur la blockchain, adaptée au marché marocain avec couverture complète pour tous types de véhicules';
        }

        // Add Moroccan regulatory compliance notice
        this.addRegulatoryNotice();
        
        // Update vehicle types for Moroccan market
        this.updateVehicleTypes();
        
        // Add Moroccan insurance features
        this.addMoroccanInsuranceFeatures();
    }

    addRegulatoryNotice() {
        const regulatoryHTML = `
            <div class="regulatory-notice">
                <div class="notice-header">
                    <span class="notice-icon">🇲🇦</span>
                    <h4>Conformité Réglementaire Maroc</h4>
                </div>
                <div class="notice-content">
                    <p><strong>Autorisé par l'ACAPS</strong> - Autorité de Contrôle des Assurances et de la Prévoyance Sociale</p>
                    <p>✅ Conforme au Code des Assurances Marocain</p>
                    <p>✅ Agréé pour l'assurance automobile au Maroc</p>
                    <p>✅ Membre du Fonds de Garantie des Accidents de la Circulation</p>
                </div>
            </div>
        `;

        const header = document.querySelector('.header');
        header.insertAdjacentHTML('afterend', regulatoryHTML);
    }

    updateVehicleTypes() {
        // Add Moroccan-specific vehicle categories
        const moroccanVehicleTypes = [
            {
                type: 'voiture',
                icon: '🚗',
                name: 'Voitures Particulières',
                description: 'Berlines, citadines, SUV, 4x4',
                baseRate: 0.02,
                subcategories: [
                    { name: 'Citadine', icon: '🚙', rate: 0.015 },
                    { name: 'Berline', icon: '🚗', rate: 0.02 },
                    { name: 'SUV/4x4', icon: '🚙', rate: 0.025 },
                    { name: 'Voiture de luxe', icon: '🏎️', rate: 0.04 }
                ]
            },
            {
                type: 'moto',
                icon: '🏍️',
                name: 'Motos & Scooters',
                description: 'Motos, scooters, cyclomoteurs',
                baseRate: 0.03,
                subcategories: [
                    { name: 'Scooter (<125cc)', icon: '🛵', rate: 0.025 },
                    { name: 'Moto (125-500cc)', icon: '🏍️', rate: 0.03 },
                    { name: 'Grosse cylindrée (>500cc)', icon: '🏍️', rate: 0.045 }
                ]
            },
            {
                type: 'utilitaire',
                icon: '🚐',
                name: 'Véhicules Utilitaires',
                description: 'Camionnettes, fourgons, pick-up',
                baseRate: 0.025,
                subcategories: [
                    { name: 'Camionnette', icon: '🚐', rate: 0.025 },
                    { name: 'Fourgon', icon: '🚚', rate: 0.03 },
                    { name: 'Pick-up', icon: '🛻', rate: 0.028 }
                ]
            },
            {
                type: 'transport',
                icon: '🚌',
                name: 'Transport Public',
                description: 'Taxis, bus, transport en commun',
                baseRate: 0.035,
                subcategories: [
                    { name: 'Taxi', icon: '🚕', rate: 0.035 },
                    { name: 'Grand taxi', icon: '🚗', rate: 0.04 },
                    { name: 'Autobus', icon: '🚌', rate: 0.045 }
                ]
            }
        ];

        // Store for use in the main application
        window.moroccanVehicleTypes = moroccanVehicleTypes;
    }

    addMoroccanInsuranceFeatures() {
        const featuresHTML = `
            <div class="morocco-features">
                <h3>🇲🇦 Spécificités Maroc</h3>
                <div class="features-grid">
                    <div class="feature-card">
                        <div class="feature-icon">🆔</div>
                        <h4>Carte Grise Électronique</h4>
                        <p>Intégration avec le système national d'immatriculation</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">🚨</div>
                        <h4>Assistance 24h/7j</h4>
                        <p>Service d'assistance partout au Maroc</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">🏥</div>
                        <h4>Réseau Médical</h4>
                        <p>Partenariat avec cliniques et hôpitaux</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">🔧</div>
                        <h4>Garages Agréés</h4>
                        <p>Réseau national de réparateurs</p>
                    </div>
                </div>
            </div>
        `;

        const analyticsCard = document.querySelector('#policyChart').closest('.card');
        analyticsCard.insertAdjacentHTML('afterend', featuresHTML);
    }

    addMoroccanCitySelector() {
        const cityHTML = `
            <div class="input-group">
                <label for="moroccanCity">Ville de résidence:</label>
                <select id="moroccanCity" required>
                    <option value="">Sélectionnez votre ville</option>
                    ${this.moroccanCities.map(city => 
                        `<option value="${city}">${city}</option>`
                    ).join('')}
                </select>
            </div>
        `;

        // Add to vehicle registration form
        const vehicleForm = document.querySelector('#vehicleValue').closest('.input-group');
        vehicleForm.insertAdjacentHTML('afterend', cityHTML);
    }

    updatePriceDisplays() {
        // Update all price displays to show both ETH and MAD
        document.querySelectorAll('.policy-price').forEach(priceElement => {
            const ethValue = parseFloat(priceElement.textContent.replace(/[^\d.]/g, ''));
            if (ethValue && !isNaN(ethValue)) {
                const madValue = ethValue * this.exchangeRates.ETH_TO_MAD;
                priceElement.innerHTML = `
                    <div class="price-eth">${this.formatETH(ethValue)}</div>
                    <div class="price-mad">${this.formatMAD(madValue)}</div>
                `;
            }
        });
    }

    addMoroccanPaymentMethods() {
        const paymentHTML = `
            <div class="payment-methods">
                <h4>💳 Méthodes de Paiement Acceptées</h4>
                <div class="payment-grid">
                    <div class="payment-method">
                        <span class="payment-icon">💳</span>
                        <span>Cartes bancaires marocaines</span>
                    </div>
                    <div class="payment-method">
                        <span class="payment-icon">📱</span>
                        <span>Mobile Banking</span>
                    </div>
                    <div class="payment-method">
                        <span class="payment-icon">🏪</span>
                        <span>Cash Plus</span>
                    </div>
                    <div class="payment-method">
                        <span class="payment-icon">🏦</span>
                        <span>Virement bancaire</span>
                    </div>
                    <div class="payment-method">
                        <span class="payment-icon">⚡</span>
                        <span>Ethereum (ETH)</span>
                    </div>
                </div>
            </div>
        `;

        return paymentHTML;
    }

    addMoroccanDocumentTypes() {
        const documentTypes = [
            'Carte d\'identité nationale',
            'Passeport marocain',
            'Carte grise (certificat d\'immatriculation)',
            'Permis de conduire marocain',
            'Certificat de visite technique',
            'Facture d\'achat du véhicule',
            'Attestation d\'assurance précédente'
        ];

        return documentTypes;
    }

    formatMoroccanPhoneNumber(phone) {
        // Format: +212 6XX XX XX XX or +212 5XX XX XX XX
        const cleaned = phone.replace(/\D/g, '');
        if (cleaned.startsWith('212')) {
            const number = cleaned.substring(3);
            return `+212 ${number.substring(0, 1)} ${number.substring(1, 3)} ${number.substring(3, 5)} ${number.substring(5, 7)} ${number.substring(7, 9)}`;
        }
        return phone;
    }

    getMoroccanInsuranceTerms() {
        return {
            'responsabilité_civile': 'Responsabilité Civile (Obligatoire)',
            'tous_risques': 'Tous Risques',
            'vol_incendie': 'Vol et Incendie',
            'bris_glace': 'Bris de Glace',
            'assistance': 'Assistance et Dépannage',
            'protection_juridique': 'Protection Juridique',
            'individuelle_conducteur': 'Garantie Individuelle Conducteur'
        };
    }
}

// Initialize Morocco localization
window.moroccoLocalization = new MoroccoLocalization();
