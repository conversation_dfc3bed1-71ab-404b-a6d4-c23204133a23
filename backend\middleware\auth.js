const jwt = require('jsonwebtoken');
const { Web3 } = require('web3');
const User = require('../models/User');

const web3 = new Web3();

// Helper to validate JWT token
const validateToken = async (token) => {
  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const user = await User.findById(decoded.id).select('-password');
    
    if (!user || !user.isActive) {
      return { valid: false, error: 'User not found or inactive' };
    }
    
    return { valid: true, user };
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return { valid: false, error: 'Invalid token' };
    }
    if (error.name === 'TokenExpiredError') {
      return { valid: false, error: 'Token expired' };
    }
    return { valid: false, error: 'Token validation failed' };
  }
};

// Helper to verify wallet signature
const verifyWalletSignature = (message, signature, address) => {
  try {
    const recoveredAddress = web3.eth.accounts.recover(message, signature);
    return recoveredAddress.toLowerCase() === address.toLowerCase();
  } catch (error) {
    console.error('Signature verification error:', error);
    return false;
  }
};

// Main authentication middleware
const auth = async (req, res, next) => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');
    
    if (!token) {
      return res.status(401).json({ 
        error: 'Access denied. No token provided.',
        code: 'AUTH001'
      });
    }
    
    const validation = await validateToken(token);
    
    if (!validation.valid) {
      return res.status(401).json({ 
        error: validation.error,
        code: 'AUTH002'
      });
    }
    
    // Check IP against user's trusted IPs if enabled
    if (process.env.ENABLE_IP_CHECK === 'true' && validation.user.trustedIPs?.length) {
      const clientIP = req.headers['x-forwarded-for'] || req.ip;
      if (!validation.user.trustedIPs.includes(clientIP)) {
        return res.status(401).json({ 
          error: 'Access denied from this IP address',
          code: 'AUTH003'
        });
      }
    }
    
    req.user = validation.user;
    next();
  } catch (error) {
    console.error('Authentication error:', error);
    res.status(500).json({ 
      error: 'Server error during authentication',
      code: 'AUTH999'
    });
  }
};

// Role-based authorization middleware
const authorize = (...roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({ 
        error: 'Authentication required',
        code: 'AUTH004'
      });
    }
    
    if (!roles.includes(req.user.role)) {
      return res.status(403).json({ 
        error: 'Access denied. Insufficient permissions',
        code: 'AUTH005',
        required: roles,
        current: req.user.role
      });
    }
    
    next();
  };
};

// Optional authentication middleware
const optionalAuth = async (req, res, next) => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');
    
    if (token) {
      const validation = await validateToken(token);
      if (validation.valid) {
        req.user = validation.user;
      }
    }
    
    next();
  } catch (error) {
    // Continue without authentication for optional auth
    next();
  }
};

// Wallet signature verification middleware
const verifyWallet = async (req, res, next) => {
  try {
    const { walletAddress, signature, message } = req.body;
    
    if (!walletAddress || !signature || !message) {
      return res.status(400).json({ 
        error: 'Missing wallet authentication parameters',
        code: 'AUTH006'
      });
    }
    
    const isValid = verifyWalletSignature(message, signature, walletAddress);
    
    if (!isValid) {
      return res.status(401).json({ 
        error: 'Invalid wallet signature',
        code: 'AUTH007'
      });
    }
    
    next();
  } catch (error) {
    console.error('Wallet verification error:', error);
    res.status(500).json({ 
      error: 'Server error during wallet verification',
      code: 'AUTH998'
    });
  }
};

module.exports = {
  auth,
  authorize,
  optionalAuth,
  verifyWallet,
  verifyWalletSignature
};
