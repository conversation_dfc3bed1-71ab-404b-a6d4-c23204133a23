@echo off
echo ========================================
echo HIMAYA ETH TRANSFER SCRIPT
echo ========================================
echo.
echo Target Wallet: ******************************************
echo Amount: 20 ETH
echo.

REM Check if Node.js is available
where node >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] Node.js is not installed!
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

echo [✓] Node.js found

REM Check if Gana<PERSON> is running
echo [INFO] Checking if Ganache is running...
curl -s -X POST -H "Content-Type: application/json" --data "{\"jsonrpc\":\"2.0\",\"method\":\"eth_accounts\",\"params\":[],\"id\":1}" http://localhost:8545 >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] Ganache is not running!
    echo.
    echo Please start Ganache first:
    echo 1. Download Ganache Desktop from: https://trufflesuite.com/ganache/
    echo 2. Create new workspace with:
    echo    - Port: 8545
    echo    - Chain ID: 1337
    echo    - Accounts: 10
    echo.
    echo OR run: npx ganache --deterministic --accounts 10 --host 0.0.0.0 --port 8545 --chain.chainId 1337
    echo.
    pause
    exit /b 1
)

echo [✓] Ganache is running

REM Install web3 if not present
if not exist node_modules\web3 (
    echo [INFO] Installing Web3...
    npm install web3
)

echo [✓] Dependencies ready
echo.
echo [INFO] Sending 20 ETH to wallet...
echo.

REM Run the transfer script
node send_eth.js

echo.
echo ========================================
echo TRANSFER COMPLETE
echo ========================================
echo.
echo Next steps:
echo 1. Import wallet in MetaMask
echo 2. Add Ganache network:
echo    - Network Name: Himaya Local
echo    - RPC URL: http://localhost:8545
echo    - Chain ID: 1337
echo    - Currency Symbol: ETH
echo 3. Test Himaya DApp with real balance
echo.
pause
