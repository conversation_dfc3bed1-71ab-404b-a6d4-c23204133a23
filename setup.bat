@echo off
echo 🚀 Setting up Vehicle Insurance Claims DApp with Private Ethereum Network...

REM Check if Docker is installed
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker is not installed. Please install Docker Desktop first.
    pause
    exit /b 1
)

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed. Please install Node.js v16 or higher first.
    pause
    exit /b 1
)

REM Check if npm is installed
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm is not installed. Please install npm first.
    pause
    exit /b 1
)

echo ✅ All prerequisites are installed!

REM Create necessary directories
echo 📁 Creating necessary directories...
if not exist "blockchain\data\node1" mkdir blockchain\data\node1
if not exist "blockchain\data\node2" mkdir blockchain\data\node2
if not exist "backend\uploads" mkdir backend\uploads

REM Copy environment file
echo ⚙️ Setting up environment files...
if not exist "backend\.env" (
    copy "backend\.env.example" "backend\.env"
    echo ✅ Created backend\.env file. Please review and update if needed.
) else (
    echo ⚠️ backend\.env already exists. Skipping...
)

REM Start Docker services
echo 🐳 Starting Hyperledger Besu network and databases...
docker-compose up -d

REM Wait for services to be ready
echo ⏳ Waiting for services to start...
timeout /t 30 /nobreak >nul

REM Install blockchain dependencies
echo 📦 Installing blockchain dependencies...
cd blockchain
call npm install
cd ..

REM Install backend dependencies
echo 📦 Installing backend dependencies...
cd backend
call npm install
cd ..

REM Install frontend dependencies
echo 📦 Installing frontend dependencies...
cd frontend
call npm install
cd ..

REM Deploy smart contracts
echo 🔗 Deploying smart contracts to Besu network...
cd blockchain
call npm run deploy
cd ..

echo ✅ Setup completed successfully! 🎉
echo.
echo 📋 Next steps:
echo 1. Start the backend server:
echo    cd backend ^&^& npm start
echo.
echo 2. In a new terminal, start the frontend:
echo    cd frontend ^&^& npm start
echo.
echo 3. Open your browser and go to: http://localhost:3000
echo.
echo 📊 Services running:
echo    - Besu Node 1: http://localhost:8545
echo    - Besu Node 2: http://localhost:8547
echo    - MongoDB: localhost:27017
echo    - Redis: localhost:6379
echo.
echo 🔑 Test accounts (with private keys in .env):
echo    - Deployer: 0xfe3b557e8fb62b89f4916b721be55ceb828dbd73
echo    - Insurer 1: 0x627306090abaB3A6e1400e9345bC60c78a8BEf57
echo    - Insurer 2: 0xf17f52151EbEF6C7334FAD080c5704D77216b732
echo.
echo 💡 To stop all services: docker-compose down
echo.
pause
