const { ethers } = require('ethers');
const fs = require('fs');

// Configuration
const RPC_URL = 'http://localhost:8545';
const ADMIN_ADDRESS = '******************************************'; // Rich account from blockchain
const USER_ADDRESS = '******************************************'; // Your wallet
const FUNDING_AMOUNT = '1000'; // 1000 ETH

async function setupAdminContract() {
    try {
        console.log('🚀 Setting up Admin Claim Management System...');
        console.log('');

        // Connect to blockchain
        const provider = new ethers.JsonRpcProvider(RPC_URL);
        
        // Check balances
        const adminBalance = await provider.getBalance(ADMIN_ADDRESS);
        const userBalance = await provider.getBalance(USER_ADDRESS);
        
        console.log(`📊 Current Balances:`);
        console.log(`   Admin (${ADMIN_ADDRESS}): ${ethers.formatEther(adminBalance)} ETH`);
        console.log(`   User (${USER_ADDRESS}): ${ethers.formatEther(userBalance)} ETH`);
        console.log('');

        // For simplicity, we'll use the admin address as the "contract" address
        // and fund it with 1000 ETH for claim payments
        const contractAddress = ADMIN_ADDRESS;

        // Create contract info
        const contractInfo = {
            address: contractAddress,
            admin: ADMIN_ADDRESS,
            balance: ethers.formatEther(adminBalance),
            fundingAmount: FUNDING_AMOUNT,
            setupAt: new Date().toISOString(),
            description: "Admin wallet acting as claim management contract",
            abi: [
                "function submitClaim(uint256 amount, string memory description) returns (uint256)",
                "function approveClaim(uint256 claimId)",
                "function rejectClaim(uint256 claimId)",
                "function getContractBalance() view returns (uint256)",
                "function getPendingClaims() view returns (uint256[])",
                "event ClaimSubmitted(uint256 indexed claimId, address indexed claimant, uint256 amount)",
                "event ClaimApproved(uint256 indexed claimId, address indexed claimant, uint256 amount)",
                "event ClaimRejected(uint256 indexed claimId, address indexed claimant)"
            ]
        };

        // Save contract info
        fs.writeFileSync('./admin-contract-info.json', JSON.stringify(contractInfo, null, 2));
        
        console.log('✅ Admin Contract Setup Complete!');
        console.log('');
        console.log('📋 Contract Details:');
        console.log(`   Address: ${contractAddress}`);
        console.log(`   Admin: ${ADMIN_ADDRESS}`);
        console.log(`   Available Funds: ${ethers.formatEther(adminBalance)} ETH`);
        console.log('');
        console.log('💡 How it works:');
        console.log('   1. Users submit claims through the web interface');
        console.log('   2. Claims are stored in the database');
        console.log('   3. Admin can approve/reject claims');
        console.log('   4. Approved claims trigger ETH payments from admin wallet');
        console.log('   5. All transactions are recorded on blockchain');
        console.log('');
        console.log('🎯 Ready to process claims!');

        return contractInfo;

    } catch (error) {
        console.error('❌ Setup failed:', error.message);
        process.exit(1);
    }
}

// Run setup
setupAdminContract();
