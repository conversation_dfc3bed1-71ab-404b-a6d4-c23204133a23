/**
 * Service Worker for SecureShield Insurance
 * Provides offline support, caching, and background sync
 */

const CACHE_NAME = 'secureshield-v1.0.0';
const STATIC_CACHE = 'secureshield-static-v1';
const DYNAMIC_CACHE = 'secureshield-dynamic-v1';

// Files to cache for offline use
const STATIC_FILES = [
    '/',
    '/index.html',
    '/app.js',
    '/js/web3-integration.js',
    '/js/charts.js',
    '/js/components.js',
    '/css/styles.css',
    '/manifest.json',
    // Add more static assets as needed
];

// API endpoints to cache
const API_CACHE_PATTERNS = [
    /\/api\/vehicles/,
    /\/api\/policies/,
    /\/api\/claims/,
];

// Install event - cache static files
self.addEventListener('install', (event) => {
    console.log('🔧 Service Worker installing...');
    
    event.waitUntil(
        caches.open(STATIC_CACHE)
            .then((cache) => {
                console.log('📦 Caching static files...');
                return cache.addAll(STATIC_FILES);
            })
            .then(() => {
                console.log('✅ Static files cached successfully');
                return self.skipWaiting();
            })
            .catch((error) => {
                console.error('❌ Failed to cache static files:', error);
            })
    );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
    console.log('🚀 Service Worker activating...');
    
    event.waitUntil(
        caches.keys()
            .then((cacheNames) => {
                return Promise.all(
                    cacheNames.map((cacheName) => {
                        if (cacheName !== STATIC_CACHE && cacheName !== DYNAMIC_CACHE) {
                            console.log('🗑️ Deleting old cache:', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            })
            .then(() => {
                console.log('✅ Service Worker activated');
                return self.clients.claim();
            })
    );
});

// Fetch event - serve cached content when offline
self.addEventListener('fetch', (event) => {
    const { request } = event;
    const url = new URL(request.url);
    
    // Handle different types of requests
    if (request.method === 'GET') {
        if (isStaticFile(request.url)) {
            // Static files - cache first strategy
            event.respondWith(cacheFirst(request));
        } else if (isAPIRequest(request.url)) {
            // API requests - network first strategy
            event.respondWith(networkFirst(request));
        } else {
            // Other requests - stale while revalidate
            event.respondWith(staleWhileRevalidate(request));
        }
    } else if (request.method === 'POST') {
        // Handle POST requests (form submissions, etc.)
        event.respondWith(handlePostRequest(request));
    }
});

// Cache first strategy - for static files
async function cacheFirst(request) {
    try {
        const cachedResponse = await caches.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }
        
        const networkResponse = await fetch(request);
        if (networkResponse.ok) {
            const cache = await caches.open(STATIC_CACHE);
            cache.put(request, networkResponse.clone());
        }
        return networkResponse;
        
    } catch (error) {
        console.error('❌ Cache first strategy failed:', error);
        return new Response('Offline - Content not available', {
            status: 503,
            statusText: 'Service Unavailable'
        });
    }
}

// Network first strategy - for API requests
async function networkFirst(request) {
    try {
        const networkResponse = await fetch(request);
        if (networkResponse.ok) {
            const cache = await caches.open(DYNAMIC_CACHE);
            cache.put(request, networkResponse.clone());
        }
        return networkResponse;
        
    } catch (error) {
        console.log('🌐 Network failed, trying cache...');
        const cachedResponse = await caches.match(request);
        
        if (cachedResponse) {
            // Add offline indicator to cached response
            const response = cachedResponse.clone();
            response.headers.set('X-Served-By', 'ServiceWorker-Cache');
            return response;
        }
        
        return new Response(JSON.stringify({
            error: 'Offline - Data not available',
            offline: true,
            timestamp: new Date().toISOString()
        }), {
            status: 503,
            statusText: 'Service Unavailable',
            headers: {
                'Content-Type': 'application/json',
                'X-Served-By': 'ServiceWorker-Offline'
            }
        });
    }
}

// Stale while revalidate strategy
async function staleWhileRevalidate(request) {
    const cache = await caches.open(DYNAMIC_CACHE);
    const cachedResponse = await cache.match(request);
    
    const fetchPromise = fetch(request).then((networkResponse) => {
        if (networkResponse.ok) {
            cache.put(request, networkResponse.clone());
        }
        return networkResponse;
    }).catch(() => cachedResponse);
    
    return cachedResponse || fetchPromise;
}

// Handle POST requests (for form submissions)
async function handlePostRequest(request) {
    try {
        return await fetch(request);
    } catch (error) {
        // Store failed requests for background sync
        await storeFailedRequest(request);
        
        return new Response(JSON.stringify({
            success: false,
            message: 'Request queued for when connection is restored',
            queued: true,
            timestamp: new Date().toISOString()
        }), {
            status: 202,
            statusText: 'Accepted',
            headers: {
                'Content-Type': 'application/json'
            }
        });
    }
}

// Store failed requests for background sync
async function storeFailedRequest(request) {
    try {
        const requestData = {
            url: request.url,
            method: request.method,
            headers: Object.fromEntries(request.headers.entries()),
            body: await request.text(),
            timestamp: new Date().toISOString()
        };
        
        // Store in IndexedDB for background sync
        const db = await openDB();
        const transaction = db.transaction(['failed_requests'], 'readwrite');
        const store = transaction.objectStore('failed_requests');
        await store.add(requestData);
        
        console.log('📝 Stored failed request for background sync');
        
    } catch (error) {
        console.error('❌ Failed to store request for background sync:', error);
    }
}

// Background sync event
self.addEventListener('sync', (event) => {
    if (event.tag === 'background-sync') {
        console.log('🔄 Background sync triggered');
        event.waitUntil(syncFailedRequests());
    }
});

// Sync failed requests when connection is restored
async function syncFailedRequests() {
    try {
        const db = await openDB();
        const transaction = db.transaction(['failed_requests'], 'readwrite');
        const store = transaction.objectStore('failed_requests');
        const requests = await store.getAll();
        
        for (const requestData of requests) {
            try {
                const response = await fetch(requestData.url, {
                    method: requestData.method,
                    headers: requestData.headers,
                    body: requestData.body
                });
                
                if (response.ok) {
                    await store.delete(requestData.id);
                    console.log('✅ Synced failed request:', requestData.url);
                }
            } catch (error) {
                console.error('❌ Failed to sync request:', error);
            }
        }
        
    } catch (error) {
        console.error('❌ Background sync failed:', error);
    }
}

// Push notification event
self.addEventListener('push', (event) => {
    if (event.data) {
        const data = event.data.json();
        const options = {
            body: data.body,
            icon: '/icons/icon-192x192.png',
            badge: '/icons/badge-72x72.png',
            vibrate: [100, 50, 100],
            data: data.data,
            actions: [
                {
                    action: 'view',
                    title: 'View Details',
                    icon: '/icons/view-icon.png'
                },
                {
                    action: 'dismiss',
                    title: 'Dismiss',
                    icon: '/icons/dismiss-icon.png'
                }
            ]
        };
        
        event.waitUntil(
            self.registration.showNotification(data.title, options)
        );
    }
});

// Notification click event
self.addEventListener('notificationclick', (event) => {
    event.notification.close();
    
    if (event.action === 'view') {
        event.waitUntil(
            clients.openWindow(event.notification.data.url || '/')
        );
    }
});

// Helper functions
function isStaticFile(url) {
    return STATIC_FILES.some(file => url.includes(file)) ||
           url.includes('.css') ||
           url.includes('.js') ||
           url.includes('.png') ||
           url.includes('.jpg') ||
           url.includes('.svg');
}

function isAPIRequest(url) {
    return API_CACHE_PATTERNS.some(pattern => pattern.test(url));
}

// IndexedDB helper
function openDB() {
    return new Promise((resolve, reject) => {
        const request = indexedDB.open('SecureShieldDB', 1);
        
        request.onerror = () => reject(request.error);
        request.onsuccess = () => resolve(request.result);
        
        request.onupgradeneeded = (event) => {
            const db = event.target.result;
            if (!db.objectStoreNames.contains('failed_requests')) {
                const store = db.createObjectStore('failed_requests', {
                    keyPath: 'id',
                    autoIncrement: true
                });
                store.createIndex('timestamp', 'timestamp');
            }
        };
    });
}

console.log('🛡️ SecureShield Service Worker loaded');
