{"address": "******************************************", "admin": "******************************************", "balance": "115792089237316195423570985008687907853269984665640564039257.584007913129614887", "fundingAmount": "1000", "setupAt": "2025-06-06T16:04:45.633Z", "description": "Admin wallet acting as claim management contract", "abi": ["function submitClaim(uint256 amount, string memory description) returns (uint256)", "function approveClaim(uint256 claimId)", "function rejectClaim(uint256 claimId)", "function getContractBalance() view returns (uint256)", "function getPendingClaims() view returns (uint256[])", "event ClaimSubmitted(uint256 indexed claimId, address indexed claimant, uint256 amount)", "event ClaimApproved(uint256 indexed claimId, address indexed claimant, uint256 amount)", "event ClaimRejected(uint256 indexed claimId, address indexed claimant)"]}