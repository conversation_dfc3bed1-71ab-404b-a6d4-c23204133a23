#!/bin/sh

echo "🔒 Starting PRIVATE Enterprise Ethereum Network..."
echo "🛡️ Starting PRIVATE Geth node with enterprise security..."

# Start Geth with PRIVATE network configuration (dev mode for simplicity)
exec geth \
    --dev \
    --dev.period=5 \
    --http \
    --http.addr=0.0.0.0 \
    --http.port=8545 \
    --http.corsdomain="http://localhost:8080,http://127.0.0.1:8080" \
    --http.api=web3,eth,net,personal \
    --http.vhosts="localhost,127.0.0.1" \
    --ws \
    --ws.addr=0.0.0.0 \
    --ws.port=8546 \
    --ws.origins="http://localhost:8080,http://127.0.0.1:8080" \
    --ws.api=web3,eth,net,personal \
    --verbosity=2 \
    --cache=512 \
    --rpc.gascap=50000000 \
    --rpc.txfeecap=100
