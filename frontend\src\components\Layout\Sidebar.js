import React from 'react';
import {
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Divider,
  Box,
  Typography,
} from '@mui/material';
import {
  Dashboard,
  DirectionsCar,
  Policy,
  Assignment,
  Add,
  Person,
  AdminPanelSettings,
} from '@mui/icons-material';
import { useNavigate, useLocation } from 'react-router-dom';

import { useAuth } from '../../contexts/AuthContext';

const drawerWidth = 240;

const Sidebar = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user } = useAuth();

  const menuItems = [
    {
      text: 'Dashboard',
      icon: <Dashboard />,
      path: '/dashboard',
      roles: ['policyholder', 'agent', 'admin'],
    },
    {
      text: 'My Vehicles',
      icon: <DirectionsCar />,
      path: '/vehicles',
      roles: ['policyholder', 'agent', 'admin'],
    },
    {
      text: 'Register Vehicle',
      icon: <Add />,
      path: '/vehicles/register',
      roles: ['policyholder'],
    },
    {
      text: 'My Policies',
      icon: <Policy />,
      path: '/policies',
      roles: ['policyholder', 'agent', 'admin'],
    },
    {
      text: 'My Claims',
      icon: <Assignment />,
      path: '/claims',
      roles: ['policyholder', 'agent', 'admin'],
    },
    {
      text: 'Submit Claim',
      icon: <Add />,
      path: '/claims/submit',
      roles: ['policyholder'],
    },
  ];

  const adminItems = [
    {
      text: 'All Vehicles',
      icon: <DirectionsCar />,
      path: '/admin/vehicles',
      roles: ['admin'],
    },
    {
      text: 'All Policies',
      icon: <Policy />,
      path: '/admin/policies',
      roles: ['admin'],
    },
    {
      text: 'All Claims',
      icon: <Assignment />,
      path: '/admin/claims',
      roles: ['admin'],
    },
    {
      text: 'User Management',
      icon: <Person />,
      path: '/admin/users',
      roles: ['admin'],
    },
  ];

  const agentItems = [
    {
      text: 'Review Claims',
      icon: <Assignment />,
      path: '/agent/claims',
      roles: ['agent'],
    },
    {
      text: 'Manage Policies',
      icon: <Policy />,
      path: '/agent/policies',
      roles: ['agent'],
    },
  ];

  const handleNavigation = (path) => {
    navigate(path);
  };

  const isSelected = (path) => {
    return location.pathname === path;
  };

  const filterItemsByRole = (items) => {
    return items.filter(item => item.roles.includes(user?.role));
  };

  return (
    <Drawer
      variant="permanent"
      sx={{
        width: drawerWidth,
        flexShrink: 0,
        [`& .MuiDrawer-paper`]: {
          width: drawerWidth,
          boxSizing: 'border-box',
          mt: 8, // Account for navbar height
        },
      }}
    >
      <Box sx={{ overflow: 'auto' }}>
        {/* Main Navigation */}
        <List>
          {filterItemsByRole(menuItems).map((item) => (
            <ListItem key={item.text} disablePadding>
              <ListItemButton
                selected={isSelected(item.path)}
                onClick={() => handleNavigation(item.path)}
              >
                <ListItemIcon>{item.icon}</ListItemIcon>
                <ListItemText primary={item.text} />
              </ListItemButton>
            </ListItem>
          ))}
        </List>

        {/* Agent Section */}
        {user?.role === 'agent' && (
          <>
            <Divider />
            <Box sx={{ p: 2 }}>
              <Typography variant="subtitle2" color="text.secondary">
                Agent Tools
              </Typography>
            </Box>
            <List>
              {filterItemsByRole(agentItems).map((item) => (
                <ListItem key={item.text} disablePadding>
                  <ListItemButton
                    selected={isSelected(item.path)}
                    onClick={() => handleNavigation(item.path)}
                  >
                    <ListItemIcon>{item.icon}</ListItemIcon>
                    <ListItemText primary={item.text} />
                  </ListItemButton>
                </ListItem>
              ))}
            </List>
          </>
        )}

        {/* Admin Section */}
        {user?.role === 'admin' && (
          <>
            <Divider />
            <Box sx={{ p: 2 }}>
              <Typography variant="subtitle2" color="text.secondary">
                <AdminPanelSettings sx={{ fontSize: 16, mr: 1 }} />
                Administration
              </Typography>
            </Box>
            <List>
              {filterItemsByRole(adminItems).map((item) => (
                <ListItem key={item.text} disablePadding>
                  <ListItemButton
                    selected={isSelected(item.path)}
                    onClick={() => handleNavigation(item.path)}
                  >
                    <ListItemIcon>{item.icon}</ListItemIcon>
                    <ListItemText primary={item.text} />
                  </ListItemButton>
                </ListItem>
              ))}
            </List>
          </>
        )}

        <Divider />
        
        {/* Profile */}
        <List>
          <ListItem disablePadding>
            <ListItemButton
              selected={isSelected('/profile')}
              onClick={() => handleNavigation('/profile')}
            >
              <ListItemIcon>
                <Person />
              </ListItemIcon>
              <ListItemText primary="Profile" />
            </ListItemButton>
          </ListItem>
        </List>
      </Box>
    </Drawer>
  );
};

export default Sidebar;
