<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔐 Himaya - Client Vérifié</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 50%, #1a1a1a 100%);
            color: #e5e5e5;
            min-height: 100vh;
            padding: 2rem;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: rgba(139, 92, 246, 0.1);
            border: 1px solid #8b5cf6;
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .header-info h1 {
            font-size: 2.5rem;
            color: #8b5cf6;
            margin-bottom: 0.5rem;
        }
        
        .header-info p {
            color: #9ca3af;
            font-size: 1.1rem;
        }
        
        .header-actions {
            display: flex;
            gap: 1rem;
        }
        
        .card {
            background: #2d3748;
            border: 1px solid #4a5568;
            border-radius: 16px;
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .card-title {
            font-size: 1.8rem;
            font-weight: 600;
            margin-bottom: 1.5rem;
            color: #f7fafc;
        }
        
        .form-input {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #4a5568;
            border-radius: 8px;
            background: #374151;
            color: #e5e7eb;
            margin-bottom: 1rem;
            transition: all 0.2s ease;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #8b5cf6;
            box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
        }
        
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }
        
        .btn {
            background: #8b5cf6;
            border: 1px solid #8b5cf6;
            border-radius: 8px;
            padding: 0.75rem 1.5rem;
            color: white;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn:hover {
            background: #7c3aed;
            transform: translateY(-1px);
        }
        
        .btn-secondary {
            background: #374151;
            border-color: #4b5563;
        }
        
        .btn-secondary:hover {
            background: #4b5563;
        }
        
        .notification {
            position: fixed;
            top: 2rem;
            right: 2rem;
            background: #374151;
            color: #e5e7eb;
            padding: 1rem 1.5rem;
            border-radius: 8px;
            border: 1px solid #8b5cf6;
            z-index: 1000;
            animation: slideIn 0.3s ease;
        }
        
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        
        .wallet-status {
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid #10b981;
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 2rem;
        }
        
        .plan-card {
            background: rgba(139, 92, 246, 0.1);
            border: 1px solid #8b5cf6;
            border-radius: 12px;
            padding: 1.5rem;
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .plan-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(139, 92, 246, 0.2);
        }
        
        .vehicle-item {
            background: #374151;
            padding: 1.5rem;
            border-radius: 12px;
            margin-bottom: 1rem;
            border: 1px solid #8b5cf6;
        }
        
        @media (max-width: 768px) {
            .header {
                flex-direction: column;
                gap: 1rem;
                text-align: center;
            }
            .grid { grid-template-columns: 1fr; }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="header-info">
                <h1>🔐 Dashboard Client Vérifié</h1>
                <p>Himaya Blockchain Insurance</p>
            </div>
            <div class="header-actions">
                <button class="btn btn-secondary" onclick="window.location.href='verification-status.html'">🔙 Retour</button>
                <button class="btn" id="connectWalletBtn">🦊 Connecter Wallet</button>
            </div>
        </div>

        <!-- Wallet Status -->
        <div class="wallet-status">
            <h3 style="color: #10b981; margin-bottom: 1rem;">🛡️ Statut de Vérification</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                <div>
                    <strong style="color: #8b5cf6;">Wallet:</strong><br>
                    <span id="walletAddress" style="font-family: monospace;">Non connecté</span>
                </div>
                <div>
                    <strong style="color: #8b5cf6;">Solde:</strong><br>
                    <span id="walletBalance">0.0000 ETH</span>
                </div>
                <div>
                    <strong style="color: #8b5cf6;">Statut:</strong><br>
                    <span id="walletStatus">Déconnecté</span>
                </div>
                <div>
                    <strong style="color: #8b5cf6;">Réseau:</strong><br>
                    <span id="walletNetwork">Non connecté</span>
                </div>
            </div>
        </div>

        <!-- Insurance Plans -->
        <div class="card">
            <h2 class="card-title">📋 Plans d'Assurance Disponibles</h2>
            <div class="grid">
                <div class="plan-card">
                    <h3 style="color: #8b5cf6; margin-bottom: 1rem;">🥉 Plan Basique</h3>
                    <div style="font-size: 1.5rem; font-weight: 600; margin: 1rem 0;">0.05 ETH/mois</div>
                    <ul style="text-align: left; margin: 1rem 0; color: #e5e7eb;">
                        <li>✅ Couverture accidents</li>
                        <li>✅ Vol et vandalisme</li>
                        <li>✅ Assistance 24h/7j</li>
                        <li>✅ Limite: 5 ETH</li>
                    </ul>
                    <button class="btn" onclick="subscribeToPlan('basic')">Souscrire</button>
                </div>
                <div class="plan-card" style="background: rgba(16, 185, 129, 0.1); border-color: #10b981;">
                    <h3 style="color: #10b981; margin-bottom: 1rem;">🥈 Plan Standard</h3>
                    <div style="font-size: 1.5rem; font-weight: 600; margin: 1rem 0;">0.08 ETH/mois</div>
                    <ul style="text-align: left; margin: 1rem 0; color: #e5e7eb;">
                        <li>✅ Tout du plan basique</li>
                        <li>✅ Tous risques</li>
                        <li>✅ Véhicule de remplacement</li>
                        <li>✅ Limite: 10 ETH</li>
                    </ul>
                    <button class="btn" onclick="subscribeToPlan('standard')" style="background: #10b981;">Souscrire</button>
                </div>
                <div class="plan-card" style="background: rgba(245, 158, 11, 0.1); border-color: #f59e0b;">
                    <h3 style="color: #f59e0b; margin-bottom: 1rem;">🥇 Plan Premium</h3>
                    <div style="font-size: 1.5rem; font-weight: 600; margin: 1rem 0;">0.12 ETH/mois</div>
                    <ul style="text-align: left; margin: 1rem 0; color: #e5e7eb;">
                        <li>✅ Tout du plan standard</li>
                        <li>✅ Couverture internationale</li>
                        <li>✅ Réparations premium</li>
                        <li>✅ Limite: 20 ETH</li>
                    </ul>
                    <button class="btn" onclick="subscribeToPlan('premium')" style="background: #f59e0b;">Souscrire</button>
                </div>
            </div>
        </div>

        <!-- Vehicle Registration -->
        <div class="card">
            <h2 class="card-title">🚗 Enregistrer un Véhicule</h2>
            <div class="grid">
                <div>
                    <input type="text" id="vehiclePlate" class="form-input" placeholder="Immatriculation (ex: 123456-A-07)">
                    <input type="text" id="vehicleMake" class="form-input" placeholder="Marque (ex: Toyota)">
                    <input type="text" id="vehicleModel" class="form-input" placeholder="Modèle (ex: Camry)">
                </div>
                <div>
                    <input type="number" id="vehicleYear" class="form-input" placeholder="Année (ex: 2023)" min="1990" max="2024">
                    <select id="vehicleType" class="form-input">
                        <option value="">Type de véhicule</option>
                        <option value="car">🚗 Voiture</option>
                        <option value="motorcycle">🏍️ Moto</option>
                        <option value="truck">🚚 Camion</option>
                        <option value="van">🚐 Fourgon</option>
                    </select>
                    <button class="btn" onclick="registerVehicle()">📝 Enregistrer sur Blockchain</button>
                </div>
            </div>
        </div>

        <!-- My Vehicles -->
        <div class="card">
            <h2 class="card-title">🚗 Mes Véhicules Assurés</h2>
            <div id="vehiclesList">
                <p style="text-align: center; color: #9ca3af; padding: 2rem;">Aucun véhicule enregistré sur la blockchain</p>
            </div>
        </div>

        <!-- Submit Claim -->
        <div class="card">
            <h2 class="card-title">🔍 Soumettre une Réclamation</h2>
            <div class="grid">
                <div>
                    <select id="claimType" class="form-input">
                        <option value="">Type de sinistre</option>
                        <option value="accident">🚗 Accident de circulation</option>
                        <option value="theft">🔒 Vol du véhicule</option>
                        <option value="vandalism">💥 Vandalisme</option>
                        <option value="fire">🔥 Incendie</option>
                        <option value="natural">🌪️ Catastrophe naturelle</option>
                    </select>
                    <input type="date" id="claimDate" class="form-input">
                    <input type="number" id="claimAmount" class="form-input" placeholder="Montant estimé (ETH)" step="0.001">
                </div>
                <div>
                    <textarea id="claimDescription" class="form-input" rows="4" placeholder="Description détaillée du sinistre..."></textarea>
                    <input type="file" id="claimFiles" class="form-input" multiple accept=".pdf,.jpg,.jpeg,.png,.doc,.docx">
                    <button class="btn" onclick="submitClaim()">📤 Soumettre sur Blockchain</button>
                </div>
            </div>
        </div>

        <!-- My Claims -->
        <div class="card">
            <h2 class="card-title">📋 Mes Réclamations</h2>
            <div id="claimsList">
                <p style="text-align: center; color: #9ca3af; padding: 2rem;">Aucune réclamation trouvée</p>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/web3@4.2.0/dist/web3.min.js"></script>
    <script>
        // Application State
        const ClientApp = {
            web3: null,
            account: null,
            contract: null,
            connected: false,
            verificationData: null
        };

        // Smart Contract Configuration - REAL DEPLOYED CONTRACTS
        const CONTRACT_CONFIG = {
            VehicleRegistry: {
                address: '0x880EC53Af800b5Cd051531672EF4fc4De233bD5d',
                        "inputs": [
                        {"type": "string", "name": "_licensePlate"},
                        {"type": "string", "name": "_make"},
                        {"type": "string", "name": "_model"},
                        {"type": "uint256", "name": "_year"},
                        {"type": "string", "name": "_city"}
                    ],
                    "name": "registerVehicle",
                    "outputs": [{"type": "uint256"}],
                    "type": "function"
                },
                {
                    "inputs": [{"type": "uint256", "name": "_vehicleId"}],
                    "name": "getVehicle",
                    "outputs": [{"type": "tuple", "components": [
                        {"type": "uint256", "name": "id"},
                        {"type": "string", "name": "licensePlate"},
                        {"type": "string", "name": "make"},
                        {"type": "string", "name": "model"},
                        {"type": "uint256", "name": "year"},
                        {"type": "address", "name": "owner"},
                        {"type": "string", "name": "city"},
                        {"type": "bool", "name": "isActive"}
                    ]}],
                    "type": "function"
                }
            ]
            },
            InsurancePolicy: {
                address: '0x3Dc2cd8F2E345951508427872d8ac9f635fBe0EC',
                abi: [
                {
                    "inputs": [
                        {"type": "uint256", "name": "_vehicleId"},
                        {"type": "address", "name": "_policyholder"},
                        {"type": "uint8", "name": "_coverageType"},
                        {"type": "uint256", "name": "_premiumAmount"},
                        {"type": "uint256", "name": "_coverageAmount"},
                        {"type": "uint256", "name": "_deductible"},
                        {"type": "uint256", "name": "_durationInDays"},
                        {"type": "string", "name": "_policyNumber"}
                    ],
                    "name": "createPolicy",
                    "outputs": [{"type": "uint256"}],
                    "type": "function"
                }
            ]
            },
            ClaimManager: {
                address: '0xe213D8b68cA3d01e51a6dBA669De59AC9A8359eE',
                abi: [
                {
                    "inputs": [
                        {"type": "uint256", "name": "_policyId"},
                        {"type": "string", "name": "_description"},
                        {"type": "uint256", "name": "_claimedAmount"},
                        {"type": "string[]", "name": "_evidenceHashes"}
                    ],
                    "name": "submitClaim",
                    "outputs": [{"type": "uint256"}],
                    "type": "function"
                }
            ]
            }
        };

        // Utility Functions
        function showNotification(message) {
            const existing = document.querySelectorAll('.notification');
            existing.forEach(n => n.remove());

            const notification = document.createElement('div');
            notification.className = 'notification';
            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 4000);
        }

        // Wallet Connection
        async function connectWallet() {
            if (!window.ethereum) {
                showNotification('❌ MetaMask non détecté. Veuillez installer MetaMask.');
                return;
            }

            try {
                showNotification('🔄 Connexion au wallet...');
                
                const accounts = await window.ethereum.request({
                    method: 'eth_requestAccounts'
                });

                if (accounts.length > 0) {
                    ClientApp.account = accounts[0];
                    ClientApp.web3 = new Web3(window.ethereum);
                    ClientApp.connected = true;

                    // Initialize contracts
                    ClientApp.contracts = {
                        VehicleRegistry: new ClientApp.web3.eth.Contract(
                            CONTRACT_CONFIG.VehicleRegistry.abi,
                            CONTRACT_CONFIG.VehicleRegistry.address
                        ),
                        InsurancePolicy: new ClientApp.web3.eth.Contract(
                            CONTRACT_CONFIG.InsurancePolicy.abi,
                            CONTRACT_CONFIG.InsurancePolicy.address
                        ),
                        ClaimManager: new ClientApp.web3.eth.Contract(
                            CONTRACT_CONFIG.ClaimManager.abi,
                            CONTRACT_CONFIG.ClaimManager.address
                        )
                    };

                    const balance = await ClientApp.web3.eth.getBalance(ClientApp.account);
                    const ethBalance = ClientApp.web3.utils.fromWei(balance, 'ether');

                    // Update UI
                    document.getElementById('walletAddress').textContent = 
                        `${ClientApp.account.slice(0, 6)}...${ClientApp.account.slice(-4)}`;
                    document.getElementById('walletBalance').textContent = 
                        `${parseFloat(ethBalance).toFixed(4)} ETH`;
                    document.getElementById('walletNetwork').textContent = 'Himaya Network';
                    document.getElementById('walletStatus').textContent = 'Connecté ✅';

                    showNotification('🎉 Wallet connecté avec succès!');
                    await loadUserData();
                }
            } catch (error) {
                console.error('Wallet connection failed:', error);
                showNotification('❌ Échec de la connexion au wallet');
            }
        }

        // Insurance Functions
        async function subscribeToPlan(planType) {
            if (!ClientApp.connected) {
                showNotification('⚠️ Veuillez d\'abord connecter votre wallet');
                return;
            }

            try {
                showNotification('🔄 Souscription au plan avec transfert de fonds...');

                const planPrices = { basic: '0.05', standard: '0.08', premium: '0.12' };
                const planCoverage = { basic: '5', standard: '10', premium: '20' };
                const planDuration = { basic: 30, standard: 30, premium: 30 }; // 30 days

                const premiumWei = ClientApp.web3.utils.toWei(planPrices[planType], 'ether');
                const coverageWei = ClientApp.web3.utils.toWei(planCoverage[planType], 'ether');

                const balance = await ClientApp.web3.eth.getBalance(ClientApp.account);
                const balanceEth = parseFloat(ClientApp.web3.utils.fromWei(balance, 'ether'));
                const requiredEth = parseFloat(planPrices[planType]);

                if (balanceEth < requiredEth) {
                    showNotification(`❌ Solde insuffisant. Requis: ${requiredEth} ETH`);
                    return;
                }

                // Create policy on blockchain
                const policyNumber = `POL-${planType.toUpperCase()}-${Date.now()}`;
                const vehicleId = 1; // Default vehicle ID for demo
                const coverageType = planType === 'basic' ? 1 : planType === 'standard' ? 2 : 3;
                const deductible = ClientApp.web3.utils.toWei('0.1', 'ether');

                const tx = await ClientApp.contracts.InsurancePolicy.methods.createPolicy(
                    vehicleId,
                    ClientApp.account,
                    coverageType,
                    premiumWei,
                    coverageWei,
                    deductible,
                    planDuration[planType],
                    policyNumber
                ).send({
                    from: ClientApp.account,
                    gas: 800000
                });

                showNotification(`🎉 Plan ${planType} souscrit! Police créée: ${policyNumber}`);
                console.log('Transaction hash:', tx.transactionHash);

            } catch (error) {
                console.error('Plan subscription failed:', error);
                showNotification('❌ Échec de la souscription au plan');
            }
        }

        async function registerVehicle() {
            if (!ClientApp.connected) {
                showNotification('⚠️ Veuillez d\'abord connecter votre wallet');
                return;
            }

            const vehiclePlate = document.getElementById('vehiclePlate').value.trim();
            const vehicleMake = document.getElementById('vehicleMake').value.trim();
            const vehicleModel = document.getElementById('vehicleModel').value.trim();
            const vehicleYear = parseInt(document.getElementById('vehicleYear').value);
            const vehicleType = document.getElementById('vehicleType').value;

            if (!vehiclePlate || !vehicleMake || !vehicleModel || !vehicleYear || !vehicleType) {
                showNotification('⚠️ Veuillez remplir tous les champs');
                return;
            }

            try {
                showNotification('🔄 Enregistrement du véhicule sur la blockchain...');

                const tx = await ClientApp.contracts.VehicleRegistry.methods.registerVehicle(
                    vehiclePlate,
                    vehicleMake,
                    vehicleModel,
                    vehicleYear,
                    'Casablanca'
                ).send({
                    from: ClientApp.account,
                    gas: 500000
                });

                // Clear form
                document.getElementById('vehiclePlate').value = '';
                document.getElementById('vehicleMake').value = '';
                document.getElementById('vehicleModel').value = '';
                document.getElementById('vehicleYear').value = '';
                document.getElementById('vehicleType').value = '';

                showNotification('🎉 Véhicule enregistré sur la blockchain!');
                console.log('Transaction hash:', tx.transactionHash);
                
                await loadUserVehicles();
                
            } catch (error) {
                console.error('Vehicle registration failed:', error);
                showNotification('❌ Échec de l\'enregistrement du véhicule');
            }
        }

        async function submitClaim() {
            if (!ClientApp.connected) {
                showNotification('⚠️ Veuillez d\'abord connecter votre wallet');
                return;
            }

            const claimType = document.getElementById('claimType').value;
            const claimDate = document.getElementById('claimDate').value;
            const claimAmount = document.getElementById('claimAmount').value;
            const claimDescription = document.getElementById('claimDescription').value;
            const claimFiles = document.getElementById('claimFiles').files;

            if (!claimType || !claimDate || !claimAmount || !claimDescription) {
                showNotification('⚠️ Veuillez remplir tous les champs');
                return;
            }

            try {
                showNotification('🔄 Soumission de la réclamation sur la blockchain...');

                const claimId = `CLM-${Date.now()}`;
                const vehicleId = 'VEHICLE-001'; // In real app, user would select

                // Process uploaded files
                const fileHashes = [];
                for (let i = 0; i < claimFiles.length; i++) {
                    const file = claimFiles[i];
                    const fileHash = `file_${Date.now()}_${i}`;
                    fileHashes.push(fileHash);
                }

                // Submit claim to blockchain
                const amountWei = ClientApp.web3.utils.toWei(claimAmount, 'ether');
                const policyId = 1; // Default policy ID for demo

                const tx = await ClientApp.contracts.ClaimManager.methods.submitClaim(
                    policyId,
                    claimDescription,
                    amountWei,
                    fileHashes
                ).send({
                    from: ClientApp.account,
                    gas: 600000
                });

                // Create claim object for local storage
                const claimData = {
                    claimId: claimId,
                    claimant: ClientApp.account,
                    claimType: claimType,
                    amount: claimAmount,
                    description: claimDescription,
                    submittedAt: new Date().toISOString(),
                    vehicleId: vehicleId,
                    fileHashes: fileHashes,
                    status: 'pending',
                    transactionHash: tx.transactionHash
                };

                // Store claim data for insurer to see
                const existingClaims = JSON.parse(localStorage.getItem('himayaClaims') || '[]');
                existingClaims.push(claimData);
                localStorage.setItem('himayaClaims', JSON.stringify(existingClaims));

                // Clear form
                document.getElementById('claimType').value = '';
                document.getElementById('claimDate').value = '';
                document.getElementById('claimAmount').value = '';
                document.getElementById('claimDescription').value = '';
                document.getElementById('claimFiles').value = '';

                showNotification('🎉 Réclamation soumise sur la blockchain!');
                console.log('Claim submitted:', claimData);

                loadUserClaims();

            } catch (error) {
                console.error('Claim submission failed:', error);
                showNotification('❌ Échec de la soumission de la réclamation');
            }
        }

        // Data Loading Functions
        async function loadUserData() {
            if (!ClientApp.connected) return;
            
            try {
                await Promise.all([
                    loadUserVehicles(),
                    loadUserClaims()
                ]);
            } catch (error) {
                console.error('Failed to load user data:', error);
            }
        }

        async function loadUserVehicles() {
            // Simulate loading vehicles from blockchain
            const vehiclesList = document.getElementById('vehiclesList');
            vehiclesList.innerHTML = '<p style="text-align: center; color: #9ca3af; padding: 2rem;">Chargement des véhicules depuis la blockchain...</p>';
            
            // In real implementation, this would call contract methods
            setTimeout(() => {
                vehiclesList.innerHTML = '<p style="text-align: center; color: #9ca3af; padding: 2rem;">Aucun véhicule enregistré sur la blockchain</p>';
            }, 2000);
        }

        async function loadUserClaims() {
            const claimsList = document.getElementById('claimsList');
            claimsList.innerHTML = '<p style="text-align: center; color: #9ca3af; padding: 2rem;">Chargement des réclamations depuis la blockchain...</p>';

            setTimeout(() => {
                // Load real claims from localStorage
                const storedClaims = localStorage.getItem('himayaClaims');
                if (!storedClaims || !ClientApp.account) {
                    claimsList.innerHTML = '<p style="text-align: center; color: #9ca3af; padding: 2rem;">Aucune réclamation trouvée</p>';
                    return;
                }

                const allClaims = JSON.parse(storedClaims);
                const userClaims = allClaims.filter(claim => claim.claimant === ClientApp.account);

                if (userClaims.length === 0) {
                    claimsList.innerHTML = '<p style="text-align: center; color: #9ca3af; padding: 2rem;">Aucune réclamation trouvée</p>';
                    return;
                }

                claimsList.innerHTML = userClaims.map(claim => `
                    <div style="background: #374151; padding: 1.5rem; border-radius: 12px; margin-bottom: 1rem; border: 1px solid #4a5568;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                            <strong style="color: #8b5cf6; font-size: 1.1rem;">#${claim.claimId}</strong>
                            <span style="background: ${getStatusColor(claim.status)}; color: white; padding: 0.25rem 0.75rem; border-radius: 12px; font-size: 0.8rem;">
                                ${getStatusText(claim.status)}
                            </span>
                        </div>
                        <div style="color: #e5e7eb; margin-bottom: 1rem;">
                            <div><strong>Type:</strong> ${claim.claimType}</div>
                            <div><strong>Montant:</strong> ${claim.amount} ETH</div>
                            <div><strong>Date:</strong> ${new Date(claim.submittedAt).toLocaleDateString()}</div>
                        </div>
                        <div style="color: #9ca3af; margin-bottom: 1rem; padding: 0.75rem; background: rgba(0,0,0,0.3); border-radius: 6px;">
                            ${claim.description}
                        </div>
                        <div style="color: #8b5cf6; font-size: 0.9rem;">
                            📎 Documents joints: ${claim.fileHashes.length}
                        </div>
                    </div>
                `).join('');
            }, 1000);
        }

        function getStatusColor(status) {
            switch(status) {
                case 'pending': return '#f59e0b';
                case 'approved': return '#10b981';
                case 'rejected': return '#ef4444';
                default: return '#6b7280';
            }
        }

        function getStatusText(status) {
            switch(status) {
                case 'pending': return 'En attente';
                case 'approved': return 'Approuvée';
                case 'rejected': return 'Rejetée';
                default: return 'Inconnu';
            }
        }

        // Event Listeners
        document.addEventListener('DOMContentLoaded', () => {
            // Load verification data
            const stored = localStorage.getItem('himayaRegistration');
            if (stored) {
                ClientApp.verificationData = JSON.parse(stored);
                if (ClientApp.verificationData.status !== 'approved') {
                    showNotification('⚠️ Vérification requise');
                    setTimeout(() => {
                        window.location.href = 'verification-status.html';
                    }, 2000);
                    return;
                }
            } else {
                showNotification('⚠️ Aucune vérification trouvée');
                setTimeout(() => {
                    window.location.href = 'client-registration.html';
                }, 2000);
                return;
            }

            // Connect wallet button
            document.getElementById('connectWalletBtn').addEventListener('click', connectWallet);

            console.log('🔐 Himaya Client App loaded');
        });
    </script>
</body>
</html>
