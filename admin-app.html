<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Himaya - Administration</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #0f172a;
            min-height: 100vh;
            color: #e2e8f0;
        }

        .header {
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            border-bottom: 1px solid rgba(148, 163, 184, 0.2);
            color: white;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: bold;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: #1e293b;
            border: 1px solid rgba(148, 163, 184, 0.2);
            padding: 1.5rem;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #8b5cf6;
        }

        .stat-label {
            color: #94a3b8;
            margin-top: 0.5rem;
        }

        .card {
            background: #1e293b;
            border: 1px solid rgba(148, 163, 184, 0.2);
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            margin-bottom: 2rem;
        }

        .card-title {
            font-size: 1.3rem;
            font-weight: bold;
            color: #f1f5f9;
            margin-bottom: 1rem;
        }

        .verification-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            border-bottom: 1px solid #e2e8f0;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        .verification-item:hover {
            background-color: #f7fafc;
        }

        .user-info h4 {
            color: #4a5568;
            margin-bottom: 0.25rem;
        }

        .user-info p {
            color: #718096;
            font-size: 0.9rem;
        }

        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .status-pending {
            background: #feebc8;
            color: #c05621;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin: 0.25rem;
        }

        .btn-success {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 2rem;
            border-radius: 15px;
            width: 90%;
            max-width: 800px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: #000;
        }

        .document-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }

        .document-item {
            text-align: center;
            padding: 1rem;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .document-item:hover {
            border-color: #667eea;
            background-color: #f0f4ff;
        }

        .document-icon {
            font-size: 3rem;
            margin-bottom: 0.5rem;
        }

        .detail-row {
            display: flex;
            justify-content: space-between;
            padding: 0.5rem 0;
            border-bottom: 1px solid #e2e8f0;
        }

        .detail-label {
            font-weight: 600;
            color: #4a5568;
        }

        .detail-value {
            color: #718096;
        }

        .wallet-info {
            background: #e6fffa;
            border: 2px solid #38b2ac;
            border-radius: 10px;
            padding: 1.5rem;
            margin-top: 1rem;
        }

        .wallet-address {
            font-family: monospace;
            background: #2d3748;
            color: #68d391;
            padding: 0.5rem;
            border-radius: 5px;
            word-break: break-all;
            margin: 0.5rem 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo">HIMAYA ⚙️ Administration</div>
        <div>
            <a href="main.html" class="btn" style="background: rgba(255,255,255,0.2); color: white;">Déconnexion</a>
        </div>
    </div>

    <div class="container">
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">8</div>
                <div class="stat-label">Vérifications en Attente</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">156</div>
                <div class="stat-label">Utilisateurs Vérifiés</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">342</div>
                <div class="stat-label">Véhicules Enregistrés</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">98.5%</div>
                <div class="stat-label">Taux d'Approbation</div>
            </div>
        </div>

        <div class="card">
            <div class="card-title">👥 Demandes de Vérification en Attente</div>
            <div id="verificationList">
                <!-- Real registration data will be loaded here -->
            </div>
        </div>
    </div>

    <!-- Verification Modal -->
    <div id="verificationModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal()">&times;</span>
            <h2>👤 Vérification d'Identité</h2>
            
            <div id="userDetails">
                <div class="detail-row">
                    <span class="detail-label">ID de Vérification:</span>
                    <span class="detail-value" id="verificationId">VER-12345678</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Nom Complet:</span>
                    <span class="detail-value" id="fullName">Ahmed Ben Ali</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">CIN:</span>
                    <span class="detail-value" id="cinNumber">AB123456</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Date de Naissance:</span>
                    <span class="detail-value" id="birthDate">15/03/1992</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Email:</span>
                    <span class="detail-value" id="email"><EMAIL></span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Téléphone:</span>
                    <span class="detail-value" id="phone">+212 661-234567</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Adresse:</span>
                    <span class="detail-value" id="address">Rue Hassan II, Quartier Maarif, Casablanca</span>
                </div>
            </div>

            <div style="margin: 2rem 0;">
                <h4>📄 Documents Soumis:</h4>
                <div class="document-grid">
                    <div class="document-item" onclick="viewDocument('cin-front')">
                        <div class="document-icon">🆔</div>
                        <h5>CIN Recto</h5>
                        <p>Cliquer pour voir</p>
                    </div>
                    <div class="document-item" onclick="viewDocument('cin-back')">
                        <div class="document-icon">🆔</div>
                        <h5>CIN Verso</h5>
                        <p>Cliquer pour voir</p>
                    </div>
                    <div class="document-item" onclick="viewDocument('selfie')">
                        <div class="document-icon">🤳</div>
                        <h5>Photo Selfie</h5>
                        <p>Cliquer pour voir</p>
                    </div>
                </div>
            </div>

            <div style="margin: 2rem 0;">
                <h4>✅ Étapes de Vérification:</h4>
                <div style="margin: 1rem 0;">
                    <label style="display: flex; align-items: center; margin-bottom: 0.5rem;">
                        <input type="checkbox" id="docQuality" style="margin-right: 0.5rem;">
                        Documents de qualité suffisante et lisibles
                    </label>
                    <label style="display: flex; align-items: center; margin-bottom: 0.5rem;">
                        <input type="checkbox" id="photoMatch" style="margin-right: 0.5rem;">
                        Photo selfie correspond à la photo CIN
                    </label>
                    <label style="display: flex; align-items: center; margin-bottom: 0.5rem;">
                        <input type="checkbox" id="infoConsistent" style="margin-right: 0.5rem;">
                        Informations cohérentes et authentiques
                    </label>
                    <label style="display: flex; align-items: center; margin-bottom: 0.5rem;">
                        <input type="checkbox" id="cinValid" style="margin-right: 0.5rem;">
                        Numéro CIN valide et vérifiable
                    </label>
                </div>
            </div>

            <div id="walletGeneration" style="display: none;">
                <div class="wallet-info">
                    <h4>🔐 Portefeuille Généré</h4>
                    <p><strong>Adresse:</strong></p>
                    <div class="wallet-address" id="generatedAddress">******************************************</div>
                    <p style="margin-top: 1rem;"><strong>Clé Privée:</strong></p>
                    <div class="wallet-address" id="privateKey">0x1234567890abcdef...</div>
                    <p style="margin-top: 1rem; color: #c53030; font-weight: bold;">
                        ⚠️ Clé privée à communiquer de manière sécurisée au client
                    </p>
                </div>
            </div>

            <div style="margin-top: 2rem; text-align: center;">
                <button class="btn btn-success" onclick="approveVerification()">✅ Approuver et Générer Portefeuille</button>
                <button class="btn btn-danger" onclick="rejectVerification()">❌ Rejeter</button>
            </div>
        </div>
    </div>

    <script>
        let currentVerificationId = '';

        // Load real registration data
        function loadVerificationRequests() {
            const registrationData = localStorage.getItem('registrationData');
            const verificationList = document.getElementById('verificationList');

            if (registrationData) {
                const data = JSON.parse(registrationData);
                if (data.status === 'pending') {
                    const submissionDate = new Date(data.submissionDate).toLocaleDateString('fr-FR', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit'
                    });

                    verificationList.innerHTML = `
                        <div class="verification-item" onclick="openVerificationModal('${data.verificationId}')">
                            <div class="user-info">
                                <h4>${data.firstName} ${data.lastName}</h4>
                                <p>CIN: ${data.cin} | Soumis le ${submissionDate}</p>
                                <small>Email: ${data.email} | Tél: ${data.phone}</small>
                            </div>
                            <span class="status-badge status-pending">En Attente</span>
                        </div>
                    `;
                } else {
                    verificationList.innerHTML = `
                        <div style="text-align: center; padding: 2rem; color: #718096;">
                            <div style="font-size: 3rem; margin-bottom: 1rem;">✅</div>
                            <p>Aucune demande en attente</p>
                            <p style="font-size: 0.9rem;">Toutes les vérifications sont à jour</p>
                        </div>
                    `;
                }
            } else {
                verificationList.innerHTML = `
                    <div style="text-align: center; padding: 2rem; color: #718096;">
                        <div style="font-size: 3rem; margin-bottom: 1rem;">📋</div>
                        <p>Aucune demande de vérification</p>
                        <p style="font-size: 0.9rem;">Les nouvelles inscriptions apparaîtront ici</p>
                    </div>
                `;
            }
        }

        function openVerificationModal(verificationId) {
            currentVerificationId = verificationId;

            // Get real registration data
            const registrationData = localStorage.getItem('registrationData');
            if (registrationData) {
                const data = JSON.parse(registrationData);
                if (data.verificationId === verificationId) {
                    document.getElementById('verificationId').textContent = verificationId;
                    document.getElementById('fullName').textContent = `${data.firstName} ${data.lastName}`;
                    document.getElementById('cinNumber').textContent = data.cin;
                    document.getElementById('birthDate').textContent = data.birthDate;
                    document.getElementById('email').textContent = data.email;
                    document.getElementById('phone').textContent = data.phone;
                    document.getElementById('address').textContent = data.address;
                }
            }

            // Reset checkboxes
            document.querySelectorAll('input[type="checkbox"]').forEach(cb => cb.checked = false);
            document.getElementById('walletGeneration').style.display = 'none';

            document.getElementById('verificationModal').style.display = 'block';
        }

        function closeModal() {
            document.getElementById('verificationModal').style.display = 'none';
        }

        function viewDocument(docType) {
            const docNames = {
                'cin-front': 'CIN Recto',
                'cin-back': 'CIN Verso',
                'selfie': 'Photo Selfie'
            };
            
            alert(`📄 Visualisation du document: ${docNames[docType]}\n\n(Dans une vraie application, ceci ouvrirait une visionneuse d'image)`);
        }

        function approveVerification() {
            // Check if all verification steps are completed
            const checkboxes = document.querySelectorAll('input[type="checkbox"]');
            const allChecked = Array.from(checkboxes).every(cb => cb.checked);
            
            if (!allChecked) {
                alert('⚠️ Veuillez compléter toutes les étapes de vérification avant d\'approuver.');
                return;
            }

            if (confirm('Approuver cette vérification et générer un portefeuille?')) {
                // Generate wallet address and private key (demo)
                const walletAddress = '0x' + Math.random().toString(16).substr(2, 40);
                const privateKey = '0x' + Math.random().toString(16).substr(2, 64);
                
                document.getElementById('generatedAddress').textContent = walletAddress;
                document.getElementById('privateKey').textContent = privateKey;
                document.getElementById('walletGeneration').style.display = 'block';
                
                // Update localStorage for demo
                const registrationData = localStorage.getItem('registrationData');
                if (registrationData) {
                    const data = JSON.parse(registrationData);
                    if (data.verificationId === currentVerificationId) {
                        data.status = 'approved';
                        data.walletAddress = walletAddress;
                        data.privateKey = privateKey;
                        data.approvalDate = new Date().toISOString();
                        localStorage.setItem('registrationData', JSON.stringify(data));
                    }
                }
                
                alert('✅ Vérification approuvée avec succès!\nPortefeuille généré et enregistré sur la blockchain.');
                
                // Remove from pending list and update UI
                setTimeout(() => {
                    closeModal();
                    loadVerificationRequests(); // Refresh the list
                    updateStats();
                }, 2000);
            }
        }

        function rejectVerification() {
            const reason = prompt('Raison du rejet:');
            if (reason) {
                alert('❌ Vérification rejetée.\nRaison: ' + reason + '\n\nLe client sera notifié par email.');
                closeModal();
                // Update UI to remove from pending list
            }
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            if (event.target.classList.contains('modal')) {
                event.target.style.display = 'none';
            }
        }

        // Update statistics
        function updateStats() {
            const registrationData = localStorage.getItem('registrationData');
            let pendingCount = 0;
            let verifiedCount = 156; // Base count

            if (registrationData) {
                const data = JSON.parse(registrationData);
                if (data.status === 'pending') {
                    pendingCount = 1;
                } else if (data.status === 'approved') {
                    verifiedCount += 1;
                }
            }

            document.querySelector('.stat-card .stat-number').textContent = pendingCount;
            document.querySelectorAll('.stat-card .stat-number')[1].textContent = verifiedCount;
        }

        // Initialize on page load
        window.addEventListener('load', function() {
            loadVerificationRequests();
            updateStats();
        });

        // Auto-check verification steps for demo
        document.querySelectorAll('input[type="checkbox"]').forEach((checkbox, index) => {
            checkbox.addEventListener('change', function() {
                // Auto-check subsequent boxes for demo purposes
                if (this.checked && index < 3) {
                    setTimeout(() => {
                        const nextCheckbox = document.querySelectorAll('input[type="checkbox"]')[index + 1];
                        if (nextCheckbox && !nextCheckbox.checked) {
                            nextCheckbox.checked = true;
                        }
                    }, 500);
                }
            });
        });
    </script>
</body>
</html>
