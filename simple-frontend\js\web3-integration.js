/**
 * Web3 Integration Layer for SecureShield Insurance
 * Handles smart contract interactions and wallet authentication
 */

class Web3Integration {
    constructor() {
        this.web3 = null;
        this.account = null;
        this.contracts = {};
        this.networkId = null;
        this.isConnected = false;
        
        // Contract addresses (will be set after deployment)
        this.contractAddresses = {
            vehicleRegistry: process.env.VEHICLE_REGISTRY_ADDRESS || '0x...',
            insurancePolicy: process.env.INSURANCE_POLICY_ADDRESS || '0x...',
            claimManager: process.env.CLAIM_MANAGER_ADDRESS || '0x...'
        };
        
        this.init();
    }

    async init() {
        try {
            // Check if Web3 is available
            if (typeof window.ethereum !== 'undefined') {
                this.web3 = new Web3(window.ethereum);
                await this.setupEventListeners();
                console.log('✅ Web3 initialized successfully');
            } else {
                throw new Error('MetaMask not detected');
            }
        } catch (error) {
            console.error('❌ Web3 initialization failed:', error);
            this.showError('Please install MetaMask to use this application');
        }
    }

    async connectWallet() {
        try {
            this.showLoading('Connecting wallet...');
            
            // Request account access
            const accounts = await window.ethereum.request({
                method: 'eth_requestAccounts'
            });
            
            if (accounts.length === 0) {
                throw new Error('No accounts found');
            }
            
            this.account = accounts[0];
            this.networkId = await this.web3.eth.net.getId();
            this.isConnected = true;
            
            // Load contracts
            await this.loadContracts();
            
            // Update UI
            this.updateConnectionStatus();
            this.showSuccess(`Connected to ${this.account.substring(0, 6)}...${this.account.substring(38)}`);
            
            // Store connection state
            localStorage.setItem('walletConnected', 'true');
            localStorage.setItem('connectedAccount', this.account);
            
            return this.account;
            
        } catch (error) {
            console.error('❌ Wallet connection failed:', error);
            this.showError(`Connection failed: ${error.message}`);
            throw error;
        } finally {
            this.hideLoading();
        }
    }

    async loadContracts() {
        try {
            // Load contract ABIs (these would be imported from compiled contracts)
            const vehicleRegistryABI = await this.loadContractABI('VehicleRegistry');
            const insurancePolicyABI = await this.loadContractABI('InsurancePolicy');
            const claimManagerABI = await this.loadContractABI('ClaimManager');
            
            // Initialize contract instances
            this.contracts.vehicleRegistry = new this.web3.eth.Contract(
                vehicleRegistryABI,
                this.contractAddresses.vehicleRegistry
            );
            
            this.contracts.insurancePolicy = new this.web3.eth.Contract(
                insurancePolicyABI,
                this.contractAddresses.insurancePolicy
            );
            
            this.contracts.claimManager = new this.web3.eth.Contract(
                claimManagerABI,
                this.contractAddresses.claimManager
            );
            
            console.log('✅ Smart contracts loaded successfully');
            
        } catch (error) {
            console.error('❌ Failed to load contracts:', error);
            throw error;
        }
    }

    async loadContractABI(contractName) {
        try {
            // In a real implementation, these would be loaded from compiled contract artifacts
            // For now, we'll use simplified ABIs
            const abis = {
                VehicleRegistry: [
                    {
                        "inputs": [
                            {"name": "_vin", "type": "string"},
                            {"name": "_make", "type": "string"},
                            {"name": "_model", "type": "string"},
                            {"name": "_year", "type": "uint256"},
                            {"name": "_value", "type": "uint256"}
                        ],
                        "name": "registerVehicle",
                        "outputs": [{"name": "", "type": "uint256"}],
                        "type": "function"
                    },
                    {
                        "inputs": [{"name": "_vehicleId", "type": "uint256"}],
                        "name": "getVehicle",
                        "outputs": [
                            {"name": "id", "type": "uint256"},
                            {"name": "vin", "type": "string"},
                            {"name": "make", "type": "string"},
                            {"name": "model", "type": "string"},
                            {"name": "year", "type": "uint256"},
                            {"name": "owner", "type": "address"}
                        ],
                        "type": "function"
                    }
                ],
                InsurancePolicy: [
                    {
                        "inputs": [
                            {"name": "_vehicleId", "type": "uint256"},
                            {"name": "_coverageType", "type": "uint8"},
                            {"name": "_premiumAmount", "type": "uint256"},
                            {"name": "_coverageAmount", "type": "uint256"},
                            {"name": "_deductible", "type": "uint256"},
                            {"name": "_durationInDays", "type": "uint256"}
                        ],
                        "name": "createPolicy",
                        "outputs": [{"name": "", "type": "uint256"}],
                        "type": "function"
                    }
                ],
                ClaimManager: [
                    {
                        "inputs": [
                            {"name": "_policyId", "type": "uint256"},
                            {"name": "_claimType", "type": "uint8"},
                            {"name": "_description", "type": "string"},
                            {"name": "_claimedAmount", "type": "uint256"},
                            {"name": "_location", "type": "string"}
                        ],
                        "name": "submitClaim",
                        "outputs": [{"name": "", "type": "uint256"}],
                        "type": "function"
                    }
                ]
            };
            
            return abis[contractName] || [];
            
        } catch (error) {
            console.error(`❌ Failed to load ABI for ${contractName}:`, error);
            return [];
        }
    }

    async registerVehicle(vehicleData) {
        try {
            this.validateConnection();
            this.showLoading('Registering vehicle on blockchain...');
            
            const { vin, make, model, year, value } = vehicleData;
            
            // Estimate gas
            const gasEstimate = await this.contracts.vehicleRegistry.methods
                .registerVehicle(vin, make, model, year, this.web3.utils.toWei(value.toString(), 'ether'))
                .estimateGas({ from: this.account });
            
            // Send transaction
            const result = await this.contracts.vehicleRegistry.methods
                .registerVehicle(vin, make, model, year, this.web3.utils.toWei(value.toString(), 'ether'))
                .send({
                    from: this.account,
                    gas: Math.floor(gasEstimate * 1.2) // Add 20% buffer
                });
            
            this.showSuccess(`Vehicle registered! Transaction: ${result.transactionHash}`);
            return result;
            
        } catch (error) {
            console.error('❌ Vehicle registration failed:', error);
            this.showError(`Registration failed: ${error.message}`);
            throw error;
        } finally {
            this.hideLoading();
        }
    }

    async createPolicy(policyData) {
        try {
            this.validateConnection();
            this.showLoading('Creating insurance policy...');
            
            const { vehicleId, coverageType, premiumAmount, coverageAmount, deductible, duration } = policyData;
            
            // Convert coverage type to enum index
            const coverageTypes = ['Liability', 'Comprehensive', 'Collision', 'Full'];
            const coverageIndex = coverageTypes.indexOf(coverageType);
            
            const result = await this.contracts.insurancePolicy.methods
                .createPolicy(
                    vehicleId,
                    coverageIndex,
                    this.web3.utils.toWei(premiumAmount.toString(), 'ether'),
                    this.web3.utils.toWei(coverageAmount.toString(), 'ether'),
                    this.web3.utils.toWei(deductible.toString(), 'ether'),
                    duration
                )
                .send({ from: this.account });
            
            this.showSuccess(`Policy created! Transaction: ${result.transactionHash}`);
            return result;
            
        } catch (error) {
            console.error('❌ Policy creation failed:', error);
            this.showError(`Policy creation failed: ${error.message}`);
            throw error;
        } finally {
            this.hideLoading();
        }
    }

    async submitClaim(claimData) {
        try {
            this.validateConnection();
            this.showLoading('Submitting insurance claim...');
            
            const { policyId, claimType, description, claimedAmount, location } = claimData;
            
            // Convert claim type to enum index
            const claimTypes = ['Collision', 'Theft', 'Vandalism', 'NaturalDisaster', 'Fire', 'Other'];
            const claimIndex = claimTypes.indexOf(claimType);
            
            const result = await this.contracts.claimManager.methods
                .submitClaim(
                    policyId,
                    claimIndex,
                    description,
                    this.web3.utils.toWei(claimedAmount.toString(), 'ether'),
                    location
                )
                .send({ from: this.account });
            
            this.showSuccess(`Claim submitted! Transaction: ${result.transactionHash}`);
            return result;
            
        } catch (error) {
            console.error('❌ Claim submission failed:', error);
            this.showError(`Claim submission failed: ${error.message}`);
            throw error;
        } finally {
            this.hideLoading();
        }
    }

    validateConnection() {
        if (!this.isConnected || !this.account) {
            throw new Error('Wallet not connected. Please connect your wallet first.');
        }
    }

    async setupEventListeners() {
        // Listen for account changes
        window.ethereum.on('accountsChanged', (accounts) => {
            if (accounts.length === 0) {
                this.disconnect();
            } else {
                this.account = accounts[0];
                this.updateConnectionStatus();
            }
        });

        // Listen for network changes
        window.ethereum.on('chainChanged', (chainId) => {
            window.location.reload(); // Reload page on network change
        });
    }

    disconnect() {
        this.account = null;
        this.isConnected = false;
        localStorage.removeItem('walletConnected');
        localStorage.removeItem('connectedAccount');
        this.updateConnectionStatus();
        this.showInfo('Wallet disconnected');
    }

    updateConnectionStatus() {
        const statusElement = document.getElementById('walletStatus');
        const connectButton = document.getElementById('connectWallet');
        
        if (this.isConnected && this.account) {
            if (statusElement) {
                statusElement.textContent = `Connected: ${this.account.substring(0, 6)}...${this.account.substring(38)}`;
                statusElement.className = 'status-item connected';
            }
            if (connectButton) {
                connectButton.textContent = 'Disconnect Wallet';
                connectButton.onclick = () => this.disconnect();
            }
        } else {
            if (statusElement) {
                statusElement.textContent = 'Not Connected';
                statusElement.className = 'status-item disconnected';
            }
            if (connectButton) {
                connectButton.textContent = 'Connect MetaMask Wallet';
                connectButton.onclick = () => this.connectWallet();
            }
        }
    }

    // UI Helper Methods
    showLoading(message) {
        // Implementation will be added to main app
        console.log(`🔄 ${message}`);
    }

    hideLoading() {
        // Implementation will be added to main app
        console.log('✅ Loading complete');
    }

    showSuccess(message) {
        // Implementation will be added to main app
        console.log(`✅ ${message}`);
    }

    showError(message) {
        // Implementation will be added to main app
        console.error(`❌ ${message}`);
    }

    showInfo(message) {
        // Implementation will be added to main app
        console.log(`ℹ️ ${message}`);
    }
}

// Export for use in main application
window.Web3Integration = Web3Integration;
