import React, { useState } from 'react';
import {
  Container,
  Typography,
  Paper,
  TextField,
  Button,
  Box,
  Grid,
  Alert,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material';
import { DirectionsCar } from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';

const VehicleRegister = () => {
  const navigate = useNavigate();
  
  const [formData, setFormData] = useState({
    vin: '',
    make: '',
    model: '',
    year: new Date().getFullYear(),
    color: '',
    engineNumber: '',
    fuelType: 'gasoline',
    transmission: 'manual',
    mileage: '',
    purchaseDate: '',
    purchasePrice: '',
    currentValue: '',
    notes: '',
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setSuccess('');

    try {
      // This would call the API to register the vehicle
      console.log('Registering vehicle:', formData);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      setSuccess('Vehicle registered successfully!');
      setTimeout(() => {
        navigate('/vehicles');
      }, 2000);
      
    } catch (error) {
      setError('Failed to register vehicle. Please try again.');
    }
    
    setLoading(false);
  };

  const currentYear = new Date().getFullYear();
  const years = Array.from({ length: currentYear - 1900 + 1 }, (_, i) => currentYear - i);

  return (
    <Container maxWidth="md">
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Register Vehicle
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Register your vehicle on the blockchain to enable insurance policies and claims.
        </Typography>
      </Box>

      <Paper sx={{ p: 4 }}>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}
        
        {success && (
          <Alert severity="success" sx={{ mb: 2 }}>
            {success}
          </Alert>
        )}

        <Box component="form" onSubmit={handleSubmit}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <TextField
                required
                fullWidth
                name="vin"
                label="Vehicle Identification Number (VIN)"
                value={formData.vin}
                onChange={handleChange}
                inputProps={{ maxLength: 17 }}
                helperText="17-character VIN number"
              />
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <TextField
                required
                fullWidth
                name="make"
                label="Make"
                value={formData.make}
                onChange={handleChange}
                placeholder="e.g., Toyota, Honda, Ford"
              />
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <TextField
                required
                fullWidth
                name="model"
                label="Model"
                value={formData.model}
                onChange={handleChange}
                placeholder="e.g., Camry, Civic, F-150"
              />
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth required>
                <InputLabel>Year</InputLabel>
                <Select
                  name="year"
                  value={formData.year}
                  label="Year"
                  onChange={handleChange}
                >
                  {years.slice(0, 30).map((year) => (
                    <MenuItem key={year} value={year}>
                      {year}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <TextField
                required
                fullWidth
                name="color"
                label="Color"
                value={formData.color}
                onChange={handleChange}
                placeholder="e.g., Red, Blue, White"
              />
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                name="engineNumber"
                label="Engine Number"
                value={formData.engineNumber}
                onChange={handleChange}
              />
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Fuel Type</InputLabel>
                <Select
                  name="fuelType"
                  value={formData.fuelType}
                  label="Fuel Type"
                  onChange={handleChange}
                >
                  <MenuItem value="gasoline">Gasoline</MenuItem>
                  <MenuItem value="diesel">Diesel</MenuItem>
                  <MenuItem value="electric">Electric</MenuItem>
                  <MenuItem value="hybrid">Hybrid</MenuItem>
                  <MenuItem value="other">Other</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Transmission</InputLabel>
                <Select
                  name="transmission"
                  value={formData.transmission}
                  label="Transmission"
                  onChange={handleChange}
                >
                  <MenuItem value="manual">Manual</MenuItem>
                  <MenuItem value="automatic">Automatic</MenuItem>
                  <MenuItem value="cvt">CVT</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                name="mileage"
                label="Current Mileage"
                type="number"
                value={formData.mileage}
                onChange={handleChange}
                inputProps={{ min: 0 }}
              />
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                name="purchaseDate"
                label="Purchase Date"
                type="date"
                value={formData.purchaseDate}
                onChange={handleChange}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                name="purchasePrice"
                label="Purchase Price ($)"
                type="number"
                value={formData.purchasePrice}
                onChange={handleChange}
                inputProps={{ min: 0, step: 0.01 }}
              />
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                name="currentValue"
                label="Current Value ($)"
                type="number"
                value={formData.currentValue}
                onChange={handleChange}
                inputProps={{ min: 0, step: 0.01 }}
              />
            </Grid>
            
            <Grid item xs={12}>
              <TextField
                fullWidth
                name="notes"
                label="Additional Notes"
                multiline
                rows={3}
                value={formData.notes}
                onChange={handleChange}
                placeholder="Any additional information about the vehicle"
              />
            </Grid>
          </Grid>

          <Box sx={{ mt: 3, display: 'flex', gap: 2 }}>
            <Button
              type="submit"
              variant="contained"
              disabled={loading}
              startIcon={<DirectionsCar />}
            >
              {loading ? 'Registering...' : 'Register Vehicle'}
            </Button>
            <Button
              variant="outlined"
              onClick={() => navigate('/vehicles')}
              disabled={loading}
            >
              Cancel
            </Button>
          </Box>
        </Box>
      </Paper>
    </Container>
  );
};

export default VehicleRegister;
