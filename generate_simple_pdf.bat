@echo off
echo Generating Professional PDF Report (Simple Version)...

REM Try to use wkhtmltopdf if available
where wkhtmltopdf >nul 2>nul
if %ERRORLEVEL% EQU 0 (
    echo Using wkhtmltopdf...
    pandoc "HIMAYA_PROFESSIONAL_REPORT.md" -t html5 --standalone --toc --css=report-style.css -o temp_report.html
    wkhtmltopdf --page-size A4 --margin-top 1in --margin-bottom 1in --margin-left 1in --margin-right 1in temp_report.html "HIMAYA_PROFESSIONAL_REPORT.pdf"
    del temp_report.html
    goto :success
)

REM Try to use Chrome/Chromium for PDF generation
where chrome >nul 2>nul
if %ERRORLEVEL% EQU 0 (
    echo Using Chrome for PDF generation...
    pandoc "HIMAYA_PROFESSIONAL_REPORT.md" -t html5 --standalone --toc --css=report-style.css -o temp_report.html
    chrome --headless --disable-gpu --print-to-pdf="HIMAYA_PROFESSIONAL_REPORT.pdf" temp_report.html
    del temp_report.html
    goto :success
)

REM Fallback: Generate HTML version
echo Generating HTML version...
pandoc "HIMAYA_PROFESSIONAL_REPORT.md" ^
    -t html5 ^
    --standalone ^
    --toc ^
    --toc-depth=3 ^
    --number-sections ^
    --highlight-style=tango ^
    --css=report-style.css ^
    -o "HIMAYA_PROFESSIONAL_REPORT.html"

echo HTML version generated: HIMAYA_PROFESSIONAL_REPORT.html
echo You can open this in your browser and print to PDF
start "" "HIMAYA_PROFESSIONAL_REPORT.html"
goto :end

:success
echo PDF generated successfully: HIMAYA_PROFESSIONAL_REPORT.pdf
start "" "HIMAYA_PROFESSIONAL_REPORT.pdf"

:end
pause
