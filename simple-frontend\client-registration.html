<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🆔 Himaya - Inscription Client</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 50%, #1a1a1a 100%);
            color: #e5e5e5;
            min-height: 100vh;
            padding: 2rem;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 3rem;
            background: rgba(139, 92, 246, 0.1);
            border: 1px solid #8b5cf6;
            border-radius: 20px;
            padding: 2rem;
        }
        
        .logo {
            font-size: 4rem;
            margin-bottom: 1rem;
        }
        
        .title {
            font-size: 2.5rem;
            font-weight: 700;
            color: #8b5cf6;
            margin-bottom: 1rem;
        }
        
        .subtitle {
            font-size: 1.2rem;
            color: #9ca3af;
        }
        
        .card {
            background: #2d3748;
            border: 1px solid #4a5568;
            border-radius: 16px;
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .card-title {
            font-size: 1.8rem;
            font-weight: 600;
            margin-bottom: 1.5rem;
            color: #f7fafc;
        }
        
        .form-input {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #4a5568;
            border-radius: 8px;
            background: #374151;
            color: #e5e7eb;
            margin-bottom: 1rem;
            transition: all 0.2s ease;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #8b5cf6;
            box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
        }
        
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }
        
        .document-upload-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }
        
        .document-upload-item {
            text-align: center;
        }
        
        .document-preview {
            width: 100%;
            height: 200px;
            border: 2px dashed #4a5568;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
            cursor: pointer;
            overflow: hidden;
        }
        
        .document-preview:hover {
            border-color: #8b5cf6;
            background: rgba(139, 92, 246, 0.05);
        }
        
        .document-preview.has-image {
            border-color: #10b981;
            border-style: solid;
        }
        
        .upload-placeholder {
            text-align: center;
            color: #9ca3af;
        }
        
        .upload-btn {
            background: #8b5cf6;
            border: none;
            border-radius: 8px;
            padding: 0.75rem 1.5rem;
            color: white;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            width: 100%;
        }
        
        .upload-btn:hover {
            background: #7c3aed;
            transform: translateY(-1px);
        }
        
        .upload-btn.uploaded {
            background: #10b981;
        }
        
        .btn {
            background: #8b5cf6;
            border: 1px solid #8b5cf6;
            border-radius: 8px;
            padding: 1rem 2rem;
            color: white;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-block;
            font-size: 1.1rem;
        }
        
        .btn:hover {
            background: #7c3aed;
            transform: translateY(-1px);
        }
        
        .btn-large {
            padding: 1.5rem 3rem;
            font-size: 1.2rem;
        }
        
        .notification {
            position: fixed;
            top: 2rem;
            right: 2rem;
            background: #374151;
            color: #e5e7eb;
            padding: 1rem 1.5rem;
            border-radius: 8px;
            border: 1px solid #8b5cf6;
            z-index: 1000;
            animation: slideIn 0.3s ease;
        }
        
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        
        .security-info {
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid #10b981;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .process-steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }
        
        .step {
            text-align: center;
            padding: 1rem;
            background: #374151;
            border-radius: 12px;
        }
        
        .step-number {
            background: #8b5cf6;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-weight: 600;
        }
        
        @media (max-width: 768px) {
            .grid { grid-template-columns: 1fr; }
            .document-upload-grid { grid-template-columns: 1fr; }
            .title { font-size: 2rem; }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="logo">🛡️</div>
            <h1 class="title">HIMAYA BLOCKCHAIN</h1>
            <p class="subtitle">Inscription & Vérification d'Identité</p>
        </div>

        <!-- Security Information -->
        <div class="security-info">
            <h3 style="color: #10b981; margin-bottom: 1rem;">🔐 Processus de Vérification Sécurisé</h3>
            <p style="color: #e5e7eb; margin-bottom: 1rem;">
                Pour garantir la sécurité de tous nos clients, nous vérifions l'identité de chaque utilisateur avant de lui donner accès à la plateforme.
            </p>
            <div class="process-steps">
                <div class="step">
                    <div class="step-number">1</div>
                    <h4 style="color: #8b5cf6;">Documents</h4>
                    <p style="color: #9ca3af; font-size: 0.9rem;">Téléchargez vos documents d'identité</p>
                </div>
                <div class="step">
                    <div class="step-number">2</div>
                    <h4 style="color: #8b5cf6;">Vérification</h4>
                    <p style="color: #9ca3af; font-size: 0.9rem;">Notre équipe vérifie vos informations</p>
                </div>
                <div class="step">
                    <div class="step-number">3</div>
                    <h4 style="color: #8b5cf6;">Wallet</h4>
                    <p style="color: #9ca3af; font-size: 0.9rem;">Réception de vos identifiants sécurisés</p>
                </div>
                <div class="step">
                    <div class="step-number">4</div>
                    <h4 style="color: #8b5cf6;">Accès</h4>
                    <p style="color: #9ca3af; font-size: 0.9rem;">Utilisation complète de la plateforme</p>
                </div>
            </div>
        </div>

        <!-- Personal Information -->
        <div class="card">
            <h2 class="card-title">👤 Informations Personnelles</h2>
            <div class="grid">
                <div>
                    <input type="text" id="fullName" class="form-input" placeholder="Nom complet *" required>
                    <input type="text" id="cinNumber" class="form-input" placeholder="Numéro CIN *" required>
                    <input type="date" id="birthDate" class="form-input" required>
                </div>
                <div>
                    <input type="tel" id="phoneNumber" class="form-input" placeholder="Numéro de téléphone *" required>
                    <input type="email" id="emailAddress" class="form-input" placeholder="Adresse email *" required>
                    <input type="text" id="address" class="form-input" placeholder="Adresse complète *" required>
                </div>
            </div>
        </div>

        <!-- Document Upload -->
        <div class="card">
            <h2 class="card-title">📄 Documents d'Identité</h2>
            <div class="document-upload-grid">
                <!-- CIN Front -->
                <div class="document-upload-item">
                    <div class="document-preview" id="cinFrontPreview">
                        <div class="upload-placeholder">
                            <div style="font-size: 3rem; margin-bottom: 1rem;">🆔</div>
                            <div style="font-weight: 600; margin-bottom: 0.5rem;">CIN - Recto</div>
                            <div style="font-size: 0.9rem; color: #9ca3af;">Cliquez pour télécharger</div>
                        </div>
                    </div>
                    <input type="file" id="cinFrontFile" accept="image/*" style="display: none;">
                    <button class="upload-btn" onclick="document.getElementById('cinFrontFile').click()">
                        📤 Télécharger Recto CIN
                    </button>
                </div>

                <!-- CIN Back -->
                <div class="document-upload-item">
                    <div class="document-preview" id="cinBackPreview">
                        <div class="upload-placeholder">
                            <div style="font-size: 3rem; margin-bottom: 1rem;">🆔</div>
                            <div style="font-weight: 600; margin-bottom: 0.5rem;">CIN - Verso</div>
                            <div style="font-size: 0.9rem; color: #9ca3af;">Cliquez pour télécharger</div>
                        </div>
                    </div>
                    <input type="file" id="cinBackFile" accept="image/*" style="display: none;">
                    <button class="upload-btn" onclick="document.getElementById('cinBackFile').click()">
                        📤 Télécharger Verso CIN
                    </button>
                </div>

                <!-- Selfie -->
                <div class="document-upload-item">
                    <div class="document-preview" id="selfiePreview">
                        <div class="upload-placeholder">
                            <div style="font-size: 3rem; margin-bottom: 1rem;">🤳</div>
                            <div style="font-weight: 600; margin-bottom: 0.5rem;">Photo Selfie</div>
                            <div style="font-size: 0.9rem; color: #9ca3af;">Visage visible et net</div>
                        </div>
                    </div>
                    <input type="file" id="selfieFile" accept="image/*" style="display: none;">
                    <button class="upload-btn" onclick="document.getElementById('selfieFile').click()">
                        📤 Télécharger Selfie
                    </button>
                </div>
            </div>

            <!-- Upload Guidelines -->
            <div style="background: rgba(245, 158, 11, 0.1); border: 1px solid #f59e0b; border-radius: 8px; padding: 1rem;">
                <h4 style="color: #f59e0b; margin-bottom: 0.5rem;">📋 Instructions de Téléchargement</h4>
                <ul style="color: #e5e7eb; margin: 0; padding-left: 1.5rem;">
                    <li>Photos claires et nettes (min 1MB, max 10MB)</li>
                    <li>Format: JPG, PNG ou PDF</li>
                    <li>CIN entièrement visible sans reflets</li>
                    <li>Selfie avec visage bien éclairé</li>
                    <li>Tous les textes doivent être lisibles</li>
                </ul>
            </div>
        </div>

        <!-- Contact Preferences -->
        <div class="card">
            <h2 class="card-title">📞 Préférences de Contact</h2>
            <div style="background: #374151; padding: 1.5rem; border-radius: 12px;">
                <h3 style="color: #8b5cf6; margin-bottom: 1rem;">Comment souhaitez-vous être contacté ?</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-bottom: 1.5rem;">
                    <label style="display: flex; align-items: center; gap: 0.5rem; cursor: pointer;">
                        <input type="checkbox" id="contactEmail" checked style="margin: 0;">
                        <span>📧 Email</span>
                    </label>
                    <label style="display: flex; align-items: center; gap: 0.5rem; cursor: pointer;">
                        <input type="checkbox" id="contactPhone" checked style="margin: 0;">
                        <span>📱 SMS/Appel</span>
                    </label>
                    <label style="display: flex; align-items: center; gap: 0.5rem; cursor: pointer;">
                        <input type="checkbox" id="contactWhatsapp" style="margin: 0;">
                        <span>💬 WhatsApp</span>
                    </label>
                </div>
                
                <div>
                    <label style="color: #8b5cf6; font-weight: 600; display: block; margin-bottom: 0.5rem;">Créneaux de disponibilité:</label>
                    <select id="availabilityTime" class="form-input">
                        <option value="morning">🌅 Matin (8h-12h)</option>
                        <option value="afternoon">☀️ Après-midi (12h-17h)</option>
                        <option value="evening">🌆 Soir (17h-20h)</option>
                        <option value="anytime">🕐 N'importe quand</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Submit Button -->
        <div class="card" style="text-align: center;">
            <div style="background: rgba(139, 92, 246, 0.1); border: 1px solid #8b5cf6; border-radius: 12px; padding: 1.5rem; margin-bottom: 2rem;">
                <h3 style="color: #8b5cf6; margin-bottom: 1rem;">🔄 Après Soumission</h3>
                <div style="color: #e5e7eb; margin-bottom: 1rem;">
                    Notre équipe vérifiera vos documents sous 24-48h. Vous recevrez vos identifiants de wallet sécurisé par email une fois approuvé.
                </div>
                <div style="font-size: 0.9rem; color: #9ca3af;">
                    ⏱️ Temps de traitement: 1-2 jours ouvrables<br>
                    🔐 Wallet MetaMask fourni après approbation
                </div>
            </div>
            
            <button class="btn btn-large" onclick="submitRegistration()">
                🚀 Soumettre ma Demande de Vérification
            </button>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/web3@4.2.0/dist/web3.min.js"></script>
    <script>
        // Application State
        const RegistrationApp = {
            documents: {},
            personalInfo: {},
            contactPrefs: {}
        };

        // Utility Functions
        function showNotification(message) {
            const existing = document.querySelectorAll('.notification');
            existing.forEach(n => n.remove());

            const notification = document.createElement('div');
            notification.className = 'notification';
            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 4000);
        }

        // Document Upload Functions
        function initializeDocumentUploads() {
            const fileInputs = ['cinFrontFile', 'cinBackFile', 'selfieFile'];
            
            fileInputs.forEach(inputId => {
                const input = document.getElementById(inputId);
                if (input) {
                    input.addEventListener('change', (e) => handleDocumentUpload(e, inputId));
                }
            });
        }

        function handleDocumentUpload(event, inputId) {
            const file = event.target.files[0];
            if (!file) return;

            // Validate file
            if (file.size > 10 * 1024 * 1024) {
                showNotification('❌ Fichier trop volumineux (max 10MB)');
                return;
            }

            if (!file.type.startsWith('image/')) {
                showNotification('❌ Seules les images sont acceptées');
                return;
            }

            // Preview the image
            const reader = new FileReader();
            reader.onload = (e) => {
                const previewId = inputId.replace('File', 'Preview');
                const preview = document.getElementById(previewId);
                const uploadBtn = preview.nextElementSibling;
                
                preview.innerHTML = `<img src="${e.target.result}" style="width: 100%; height: 100%; object-fit: cover; border-radius: 10px;">`;
                preview.classList.add('has-image');
                uploadBtn.textContent = '✅ ' + uploadBtn.textContent.replace('📤 ', '');
                uploadBtn.classList.add('uploaded');
                
                // Store file data
                const docType = inputId.replace('File', '');
                RegistrationApp.documents[docType] = {
                    file: file,
                    dataUrl: e.target.result,
                    uploaded: true
                };
                
                showNotification(`✅ ${getDocumentName(docType)} téléchargé avec succès`);
            };
            reader.readAsDataURL(file);
        }

        function getDocumentName(docType) {
            const names = {
                cinFront: 'CIN Recto',
                cinBack: 'CIN Verso',
                selfie: 'Photo Selfie'
            };
            return names[docType] || docType;
        }

        // Registration Submission
        async function submitRegistration() {
            // Validate personal information
            const personalInfo = {
                fullName: document.getElementById('fullName').value.trim(),
                cinNumber: document.getElementById('cinNumber').value.trim(),
                birthDate: document.getElementById('birthDate').value,
                phoneNumber: document.getElementById('phoneNumber').value.trim(),
                emailAddress: document.getElementById('emailAddress').value.trim(),
                address: document.getElementById('address').value.trim()
            };

            // Validate required fields
            const requiredFields = ['fullName', 'cinNumber', 'birthDate', 'phoneNumber', 'emailAddress', 'address'];
            for (const field of requiredFields) {
                if (!personalInfo[field]) {
                    showNotification(`⚠️ Veuillez remplir le champ: ${getFieldName(field)}`);
                    return;
                }
            }

            // Validate documents
            const requiredDocs = ['cinFront', 'cinBack', 'selfie'];
            for (const doc of requiredDocs) {
                if (!RegistrationApp.documents[doc]?.uploaded) {
                    showNotification(`⚠️ Veuillez télécharger: ${getDocumentName(doc)}`);
                    return;
                }
            }

            // Collect contact preferences
            const contactPrefs = {
                email: document.getElementById('contactEmail').checked,
                phone: document.getElementById('contactPhone').checked,
                whatsapp: document.getElementById('contactWhatsapp').checked,
                availabilityTime: document.getElementById('availabilityTime').value
            };

            try {
                showNotification('🔄 Soumission de votre demande de vérification...');

                // Generate submission ID
                const submissionId = `VER-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

                // Store registration data
                const registrationData = {
                    submissionId: submissionId,
                    personalInfo: personalInfo,
                    contactPrefs: contactPrefs,
                    documents: RegistrationApp.documents,
                    status: 'pending',
                    submittedAt: new Date().toISOString()
                };

                // Store in localStorage (in real app, this would go to backend/blockchain)
                localStorage.setItem('himayaRegistration', JSON.stringify(registrationData));

                showNotification('🎉 Demande de vérification soumise avec succès!');
                
                // Redirect to status page
                setTimeout(() => {
                    window.location.href = 'verification-status.html';
                }, 2000);

            } catch (error) {
                console.error('Registration submission failed:', error);
                showNotification('❌ Échec de la soumission de la demande');
            }
        }

        function getFieldName(field) {
            const names = {
                fullName: 'Nom complet',
                cinNumber: 'Numéro CIN',
                birthDate: 'Date de naissance',
                phoneNumber: 'Numéro de téléphone',
                emailAddress: 'Adresse email',
                address: 'Adresse complète'
            };
            return names[field] || field;
        }

        // Initialize app
        document.addEventListener('DOMContentLoaded', () => {
            initializeDocumentUploads();
            console.log('🆔 Himaya Client Registration App loaded');
        });
    </script>
</body>
</html>
