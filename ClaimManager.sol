// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

interface IInsurancePolicy {
    function isPolicyActive(uint256 _policyId) external view returns (bool);
    function getPolicy(uint256 _policyId) external view returns (
        uint256 id,
        uint256 vehicleId,
        address policyholder,
        uint8 coverageType,
        uint256 premiumAmount,
        uint256 coverageAmount,
        uint256 deductible,
        uint256 startDate,
        uint256 endDate,
        bool isActive,
        string memory policyNumber
    );
}

contract ClaimManager {
    enum ClaimStatus { Pending, UnderReview, Approved, Rejected, Paid }
    
    struct Claim {
        uint256 id;
        uint256 policyId;
        address claimant;
        string description;
        uint256 claimedAmount;
        ClaimStatus status;
        uint256 submissionDate;
        uint256 reviewDate;
        uint256 paymentDate;
        string[] evidenceHashes;
        string rejectionReason;
    }
    
    mapping(uint256 => Claim) public claims;
    mapping(address => uint256[]) public claimantClaims;
    mapping(uint256 => uint256[]) public policyClaims; // policyId => claimIds
    
    uint256 public nextClaimId = 1;
    address public insurancePolicyContract;
    address public insurerAddress;
    
    event ClaimSubmitted(
        uint256 indexed claimId,
        uint256 indexed policyId,
        address indexed claimant,
        uint256 amount,
        string description
    );
    
    event ClaimStatusUpdated(
        uint256 indexed claimId,
        ClaimStatus status,
        address updatedBy
    );
    
    event ClaimApproved(
        uint256 indexed claimId,
        address indexed approver,
        uint256 approvedAmount
    );
    
    event ClaimRejected(
        uint256 indexed claimId,
        address indexed rejector,
        string reason
    );
    
    event ClaimPaid(
        uint256 indexed claimId,
        address indexed claimant,
        uint256 amount
    );
    
    modifier onlyInsurer() {
        require(msg.sender == insurerAddress, "Only insurer can call this");
        _;
    }
    
    modifier onlyClaimant(uint256 _claimId) {
        require(claims[_claimId].claimant == msg.sender, "Not claim owner");
        _;
    }
    
    constructor(address _insurancePolicyContract, address _insurerAddress) {
        insurancePolicyContract = _insurancePolicyContract;
        insurerAddress = _insurerAddress;
    }
    
    function submitClaim(
        uint256 _policyId,
        string memory _description,
        uint256 _claimedAmount,
        string[] memory _evidenceHashes
    ) external returns (uint256) {
        // Validate policy exists and is active
        require(
            IInsurancePolicy(insurancePolicyContract).isPolicyActive(_policyId),
            "Policy not active"
        );
        
        // Get policy details to validate claimant
        (, , address policyholder, , , uint256 coverageAmount, uint256 deductible, , , ,) = 
            IInsurancePolicy(insurancePolicyContract).getPolicy(_policyId);
        
        require(policyholder == msg.sender, "Not policy holder");
        require(_claimedAmount > 0, "Invalid claim amount");
        require(_claimedAmount <= coverageAmount, "Claim exceeds coverage");
        require(_claimedAmount >= deductible, "Claim below deductible");
        require(bytes(_description).length > 0, "Description required");
        
        uint256 claimId = nextClaimId++;
        
        claims[claimId] = Claim({
            id: claimId,
            policyId: _policyId,
            claimant: msg.sender,
            description: _description,
            claimedAmount: _claimedAmount,
            status: ClaimStatus.Pending,
            submissionDate: block.timestamp,
            reviewDate: 0,
            paymentDate: 0,
            evidenceHashes: _evidenceHashes,
            rejectionReason: ""
        });
        
        claimantClaims[msg.sender].push(claimId);
        policyClaims[_policyId].push(claimId);
        
        emit ClaimSubmitted(claimId, _policyId, msg.sender, _claimedAmount, _description);
        return claimId;
    }
    
    function reviewClaim(uint256 _claimId) 
        external 
        onlyInsurer 
    {
        Claim storage claim = claims[_claimId];
        require(claim.status == ClaimStatus.Pending, "Claim not pending");
        
        claim.status = ClaimStatus.UnderReview;
        claim.reviewDate = block.timestamp;
        
        emit ClaimStatusUpdated(_claimId, ClaimStatus.UnderReview, msg.sender);
    }
    
    function approveClaim(uint256 _claimId) 
        external 
        onlyInsurer 
    {
        Claim storage claim = claims[_claimId];
        require(
            claim.status == ClaimStatus.Pending || claim.status == ClaimStatus.UnderReview,
            "Invalid claim status"
        );
        
        claim.status = ClaimStatus.Approved;
        claim.reviewDate = block.timestamp;
        
        emit ClaimApproved(_claimId, msg.sender, claim.claimedAmount);
        emit ClaimStatusUpdated(_claimId, ClaimStatus.Approved, msg.sender);
    }
    
    function rejectClaim(uint256 _claimId, string memory _reason) 
        external 
        onlyInsurer 
    {
        Claim storage claim = claims[_claimId];
        require(
            claim.status == ClaimStatus.Pending || claim.status == ClaimStatus.UnderReview,
            "Invalid claim status"
        );
        
        claim.status = ClaimStatus.Rejected;
        claim.rejectionReason = _reason;
        claim.reviewDate = block.timestamp;
        
        emit ClaimRejected(_claimId, msg.sender, _reason);
        emit ClaimStatusUpdated(_claimId, ClaimStatus.Rejected, msg.sender);
    }
    
    function payClaim(uint256 _claimId) 
        external 
        payable 
        onlyInsurer 
    {
        Claim storage claim = claims[_claimId];
        require(claim.status == ClaimStatus.Approved, "Claim not approved");
        require(msg.value == claim.claimedAmount, "Incorrect payment amount");
        
        claim.status = ClaimStatus.Paid;
        claim.paymentDate = block.timestamp;
        
        // Transfer funds to claimant
        payable(claim.claimant).transfer(claim.claimedAmount);
        
        emit ClaimPaid(_claimId, claim.claimant, claim.claimedAmount);
        emit ClaimStatusUpdated(_claimId, ClaimStatus.Paid, msg.sender);
    }
    
    function getClaim(uint256 _claimId) 
        external 
        view 
        returns (
            uint256 id,
            uint256 policyId,
            address claimant,
            string memory description,
            uint256 claimedAmount,
            ClaimStatus status,
            uint256 submissionDate,
            uint256 reviewDate,
            uint256 paymentDate,
            string[] memory evidenceHashes,
            string memory rejectionReason
        ) 
    {
        Claim memory claim = claims[_claimId];
        return (
            claim.id,
            claim.policyId,
            claim.claimant,
            claim.description,
            claim.claimedAmount,
            claim.status,
            claim.submissionDate,
            claim.reviewDate,
            claim.paymentDate,
            claim.evidenceHashes,
            claim.rejectionReason
        );
    }
    
    function getClaimantClaims(address _claimant) 
        external 
        view 
        returns (uint256[] memory) 
    {
        return claimantClaims[_claimant];
    }
    
    function getPolicyClaims(uint256 _policyId) 
        external 
        view 
        returns (uint256[] memory) 
    {
        return policyClaims[_policyId];
    }
    
    function getTotalClaims() external view returns (uint256) {
        return nextClaimId - 1;
    }
}
