@echo off
echo ========================================
echo HIMAYA VEHICLE INSURANCE DAPP
echo ========================================
echo.

REM Check if Docker is installed
where docker >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] Docker is not installed or not in PATH.
    echo Please install Docker Desktop from: https://www.docker.com/products/docker-desktop
    goto :error
)

REM Check if Docker services are running
echo [1/5] Checking Docker services...
docker ps | findstr "geth-private-enterprise" >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo [INFO] Starting blockchain and database services...
    docker-compose up -d

    REM Wait for services to start
    echo [INFO] Waiting for services to start...
    timeout /t 10 /nobreak >nul

    REM Check again if services started properly
    docker ps | findstr "geth-private-enterprise" >nul 2>&1
    if %ERRORLEVEL% NEQ 0 (
        echo [WARNING] Blockchain container failed to start.
        echo Please check Docker logs with: docker logs geth-private-enterprise
    ) else (
        echo [✓] Blockchain services started successfully.
    )
) else (
    echo [✓] Blockchain services are already running.
)

REM Check if deployments.json exists (smart contracts deployed)
echo [2/5] Checking smart contract deployment...
if not exist deployments.json (
    echo [INFO] Smart contracts are not deployed. Attempting to deploy...
    cd blockchain
    call npm run deploy
    if %ERRORLEVEL% NEQ 0 (
        echo [ERROR] Smart contract deployment failed.
        echo Please check the deployment script and try again.
        cd ..
        goto :continue_anyway
    )
    cd ..

    echo [✓] Smart contracts deployed successfully!

    REM Extract contract addresses using Node.js
    echo [INFO] Updating backend configuration with contract addresses...
    node -e "const fs=require('fs'); const path=require('path'); const deployments=JSON.parse(fs.readFileSync('deployments.json', 'utf8')); const envFile=fs.readFileSync('backend/.env', 'utf8').split('\n'); const newEnv=envFile.map(line => { if(line.startsWith('VEHICLE_REGISTRY_ADDRESS=')) return 'VEHICLE_REGISTRY_ADDRESS=' + deployments.contracts.VehicleRegistry.address; if(line.startsWith('INSURANCE_POLICY_ADDRESS=')) return 'INSURANCE_POLICY_ADDRESS=' + deployments.contracts.InsurancePolicy.address; if(line.startsWith('CLAIM_MANAGER_ADDRESS=')) return 'CLAIM_MANAGER_ADDRESS=' + deployments.contracts.ClaimManager.address; return line; }); fs.writeFileSync('backend/.env', newEnv.join('\n'), 'utf8');"

    echo [✓] Contract addresses updated in backend/.env
) else (
    echo [✓] Smart contracts are already deployed.
)

:continue_anyway

REM Check if backend dependencies are installed
echo [3/5] Checking backend dependencies...
if not exist backend\node_modules (
    echo [INFO] Installing backend dependencies...
    cd backend
    call npm install
    if %ERRORLEVEL% NEQ 0 (
        echo [ERROR] Failed to install backend dependencies.
        cd ..
        goto :error
    )
    cd ..
    echo [✓] Backend dependencies installed.
) else (
    echo [✓] Backend dependencies already installed.
)

REM Check if frontend dependencies are installed
echo [4/5] Checking frontend dependencies...
if not exist frontend\node_modules (
    echo [INFO] Installing frontend dependencies...
    cd frontend
    call npm install
    if %ERRORLEVEL% NEQ 0 (
        echo [ERROR] Failed to install frontend dependencies.
        cd ..
        goto :error
    )
    cd ..
    echo [✓] Frontend dependencies installed.
) else (
    echo [✓] Frontend dependencies already installed.
)

REM Start servers
echo [5/5] Starting application servers...

REM Start backend server
echo [INFO] Starting backend server on port 3001...
start "Backend Server" cmd /c "cd backend && npm start"

REM Wait for backend to start
echo [INFO] Waiting for backend to start...
timeout /t 5 /nobreak >nul

REM Start frontend server
echo [INFO] Starting frontend server on port 3000...
start "Frontend Server" cmd /c "cd frontend && npm start"

REM Wait a moment for frontend to start
timeout /t 3 >nul
echo.
echo ========================================
echo APPLICATION READY! 🚀
echo ========================================
echo.
echo 🌐 Web Interface: http://localhost:3000
echo 🔌 API Endpoint: http://localhost:3001
echo ⛓️ Blockchain: http://localhost:8545
echo 💾 MongoDB: localhost:27017
echo 🔄 Redis: localhost:6379
echo.
echo DEMO ACCOUNTS:
echo - Deployer: 0xfe3b557e8fb62b89f4916b721be55ceb828dbd73
echo - Insurer 1: 0x627306090abaB3A6e1400e9345bC60c78a8BEf57
echo - Insurer 2: 0xf17f52151EbEF6C7334FAD080c5704D77216b732
echo.
echo Opening application in your browser...
timeout /t 2 >nul
start http://localhost:3000

echo.
echo To stop the application, close the terminal windows or press Ctrl+C in each.
echo To stop the blockchain and database services, run: docker-compose down
echo.
goto :end

:error
echo.
echo [ERROR] Setup failed. Please check the error messages above.
echo For troubleshooting, refer to the README.md file.
exit /b 1

:end
echo Press any key to close this window...
pause >nul
