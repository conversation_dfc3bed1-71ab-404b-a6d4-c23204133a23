@echo off
echo ========================================
echo HIMAYA VEHICLE INSURANCE DAPP SETUP
echo ========================================
echo.

REM Check if Node.js is installed
where node >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] Node.js is not installed!
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

echo [✓] Node.js found
node --version

REM Check if npm is available
where npm >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] npm is not available!
    pause
    exit /b 1
)

echo [✓] npm found
npm --version

echo.
echo Installing required packages...
echo.

REM Install global packages
echo Installing Truffle...
npm install -g truffle

echo Installing Ganache CLI...
npm install -g ganache-cli

echo Installing http-server...
npm install -g http-server

REM Initialize npm project
echo.
echo Initializing project...
npm init -y

REM Install local dependencies
echo Installing local dependencies...
npm install web3 express cors body-parser multer

echo.
echo ========================================
echo SETUP COMPLETE!
echo ========================================
echo.
echo Next steps:
echo 1. Start Ganache: ganache-cli --deterministic --accounts 10 --host 0.0.0.0 --port 8545
echo 2. Deploy contracts: truffle migrate --reset
echo 3. Start web server: http-server -p 8080
echo 4. Open browser: http://localhost:8080/main.html
echo.
echo For MetaMask setup:
echo - Network: http://localhost:8545
echo - Chain ID: 1337
echo - Import accounts from Ganache output
echo.
pause
