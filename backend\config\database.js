const mongoose = require('mongoose');
const EventEmitter = require('events');

class DatabaseEventEmitter extends EventEmitter {}
const dbEvents = new DatabaseEventEmitter();

// Database connection options
const options = {
  useNewUrlParser: true,
  useUnifiedTopology: true,
  maxPoolSize: 100,
  minPoolSize: 10,
  serverSelectionTimeoutMS: 15000,
  socketTimeoutMS: 45000,
  connectTimeoutMS: 15000,
  heartbeatFrequencyMS: 10000,
  retryWrites: true,
  w: 'majority',
  wtimeout: 5000,
  ssl: process.env.NODE_ENV === 'production',
  authSource: 'admin',
  autoIndex: process.env.NODE_ENV !== 'production',
  family: 4
};

// Retry strategy configuration
const maxRetries = 5;
const initialRetryDelay = 1000; // 1 second

// Helper to implement exponential backoff
const getRetryDelay = (retryCount) => {
  return Math.min(initialRetryDelay * Math.pow(2, retryCount), 30000); // Max 30 seconds
};

// Connect to MongoDB with retry mechanism
const connectWithRetry = async (retryCount = 0) => {
  try {
    await mongoose.connect(process.env.MONGODB_URI, options);
    console.log('MongoDB connected successfully');
    
    // Reset retry count on successful connection
    if (retryCount > 0) {
      console.log(`Successfully reconnected after ${retryCount} attempts`);
    }
    
    dbEvents.emit('connected');
  } catch (error) {
    console.error('MongoDB connection error:', error);
    
    if (retryCount < maxRetries) {
      const delay = getRetryDelay(retryCount);
      console.log(`Retrying connection in ${delay}ms... (Attempt ${retryCount + 1}/${maxRetries})`);
      
      setTimeout(() => {
        connectWithRetry(retryCount + 1);
      }, delay);
    } else {
      console.error('Failed to connect to MongoDB after maximum retries');
      dbEvents.emit('maxRetriesExceeded');
      process.exit(1);
    }
  }
};

// Set up connection monitoring
mongoose.connection.on('connected', () => {
  console.log('Mongoose connection established');
  dbEvents.emit('connected');
});

mongoose.connection.on('error', (err) => {
  console.error('Mongoose connection error:', err);
  dbEvents.emit('error', err);
});

mongoose.connection.on('disconnected', () => {
  console.log('Mongoose connection disconnected');
  dbEvents.emit('disconnected');
});

mongoose.connection.on('reconnected', () => {
  console.log('Mongoose connection reestablished');
  dbEvents.emit('reconnected');
});

// Clean up connection on process termination
process.on('SIGINT', async () => {
  try {
    await mongoose.connection.close();
    console.log('Mongoose connection closed through app termination');
    process.exit(0);
  } catch (error) {
    console.error('Error closing Mongoose connection:', error);
    process.exit(1);
  }
});

// Export connection function and event emitter
module.exports = {
  connectDB: connectWithRetry,
  dbEvents
};