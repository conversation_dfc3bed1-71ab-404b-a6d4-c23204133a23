const mongoose = require('mongoose');

const connectDB = async () => {
  try {
    const mongoURI = process.env.MONGODB_URI || '******************************************************************************************************';
    
    console.log('🔄 Connecting to MongoDB...');
    
    const options = {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      maxPoolSize: 100, // Increased for better scalability
      serverSelectionTimeoutMS: 15000, // 15 seconds timeout for selection
      socketTimeoutMS: 45000, // 45 seconds for socket timeout
      connectTimeoutMS: 15000, // 15 seconds for connection timeout
      heartbeatFrequencyMS: 10000, // 10 seconds heartbeat
      retryWrites: true,
      retryReads: true,
    };

    const conn = await mongoose.connect(mongoURI, options);
    
    console.log(`✅ MongoDB Connected: ${conn.connection.host}`);
    
    // Handle connection events
    mongoose.connection.on('error', (err) => {
      console.error('❌ MongoDB connection error:', err);
      // Attempt to reconnect
      console.log('🔄 Attempting to reconnect to MongoDB...');
    });
    
    mongoose.connection.on('disconnected', () => {
      console.log('⚠️ MongoDB disconnected');
      // Only attempt to reconnect if the disconnection wasn't intentional
      if (mongoose.connection.readyState !== 0) {
        console.log('🔄 Attempting to reconnect to MongoDB...');
        retryConnection();
      }
    });
    
    mongoose.connection.on('reconnected', () => {
      console.log('✅ MongoDB reconnected');
    });
    
    // Graceful shutdown
    process.on('SIGINT', async () => {
      try {
        await mongoose.connection.close();
        console.log('📴 MongoDB connection closed through app termination');
        process.exit(0);
      } catch (err) {
        console.error('❌ Error during MongoDB connection close:', err);
        process.exit(1);
      }
    });
    
    return conn;
    
  } catch (error) {
    console.error('❌ MongoDB connection failed:', error.message);
    
    // Don't exit the process on connection failure, try to operate in degraded mode
    console.log('⚠️ Application will continue without database connection');
    console.log('⚠️ Some features may not work properly');
    
    // Retry connection
    retryConnection();
    
    return null;
  }
};

// Function to retry connection
const retryConnection = (retries = 10, interval = 5000) => {
  let attempts = 0;
  
  const attemptReconnect = async () => {
    try {
      if (mongoose.connection.readyState !== 1) { // Not connected
        attempts++;
        console.log(`🔄 MongoDB reconnection attempt ${attempts}/${retries}...`);
        
        const mongoURI = process.env.MONGODB_URI || '******************************************************************************************************';
        await mongoose.connect(mongoURI, {
          useNewUrlParser: true,
          useUnifiedTopology: true,
          serverSelectionTimeoutMS: 10000,
          maxPoolSize: 100,
          retryWrites: true
        });
        
        console.log('✅ MongoDB reconnected successfully');
        return;
      }
    } catch (error) {
      console.error(`❌ MongoDB reconnection attempt ${attempts} failed:`, error.message);
      
      if (attempts < retries) {
        const backoffInterval = Math.min(interval * Math.pow(1.5, attempts - 1), 30000); // Exponential backoff capped at 30s
        console.log(`⏳ Waiting ${backoffInterval/1000} seconds before next attempt...`);
        setTimeout(attemptReconnect, backoffInterval);
      } else {
        console.error(`❌ Failed to reconnect to MongoDB after ${retries} attempts`);
        console.log('⚠️ Application will continue in degraded mode');
        // Emit an event that can be used to notify monitoring systems
        process.emit('mongodb:reconnection_failed');
      }
    }
  };
  
  // Start the first reconnection attempt
  setTimeout(attemptReconnect, interval);
};

module.exports = connectDB;