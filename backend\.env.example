# Server Configuration
PORT=3001
NODE_ENV=development

# Blockchain Configuration
ETHEREUM_RPC_URL=http://localhost:8545
ETHEREUM_WS_URL=ws://localhost:8546
CHAIN_ID=1337

# Contract Addresses (will be populated after deployment)
VEHICLE_REGISTRY_ADDRESS=
INSURANCE_POLICY_ADDRESS=
CLAIM_MANAGER_ADDRESS=

# Private Keys (for server-side operations)
DEPLOYER_PRIVATE_KEY=0xc87509a1c067bbde78beb793e6fa76530b6382a4c0241e5e4a9ec0a0f44dc0d3
INSURER1_PRIVATE_KEY=0xae6ae8e5ccbfb04590405997ee2d52d2b330726137b875053c36d94e974d162f
INSURER2_PRIVATE_KEY=0x0dbbe8e4ae425a6d2687f1a7e3ba17bc98c673636790f1b8ad91193c05875ef1

# Database Configuration
MONGODB_URI=**************************************************************************

# Redis Configuration
REDIS_URL=redis://localhost:6379

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=24h

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_PATH=./uploads

# IPFS Configuration (optional)
IPFS_API_URL=http://localhost:5001

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# CORS Configuration
CORS_ORIGIN=http://localhost:3000
