<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🛡️ Himaya Insurance - Blockchain Platform</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            padding: 2rem 0;
            color: white;
        }

        .logo {
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        .title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .status-bar {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 1.5rem;
            margin: 2rem 0;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }

        .status-item {
            text-align: center;
            padding: 1rem;
        }

        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #ef4444;
            margin: 0 auto 0.5rem;
            transition: all 0.3s ease;
        }

        .status-dot.connected {
            background: #10b981;
            box-shadow: 0 0 10px rgba(16, 185, 129, 0.5);
        }

        .nav-tabs {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin: 2rem 0;
            flex-wrap: wrap;
        }

        .tab-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 25px;
            padding: 1rem 2rem;
            color: white;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .tab-btn:hover, .tab-btn.active {
            background: rgba(255, 255, 255, 0.9);
            color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        .tab-content {
            display: none;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 2rem;
            margin: 2rem 0;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .tab-content.active {
            display: block;
            animation: fadeIn 0.5s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin: 1rem 0;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(102, 126, 234, 0.1);
        }

        .card-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #667eea;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #374151;
        }

        .form-input {
            width: 100%;
            padding: 1rem;
            border: 2px solid #e5e7eb;
            border-radius: 10px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 1rem 2rem;
            color: white;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1rem;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #10b981;
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 10px;
            font-weight: 600;
            z-index: 1000;
            animation: slideIn 0.3s ease;
            box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
        }

        .notification.error {
            background: #ef4444;
        }

        .notification.warning {
            background: #f59e0b;
        }

        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }

        .data-table th,
        .data-table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
        }

        .data-table th {
            background: #f8fafc;
            font-weight: 600;
            color: #374151;
        }

        .data-table tr:hover {
            background: #f8fafc;
        }

        .badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .badge.success {
            background: #d1fae5;
            color: #065f46;
        }

        .badge.warning {
            background: #fef3c7;
            color: #92400e;
        }

        .badge.error {
            background: #fee2e2;
            color: #991b1b;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .wallet-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .wallet-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .account-info {
            flex: 1;
        }

        .account-address {
            font-family: monospace;
            background: rgba(255, 255, 255, 0.2);
            padding: 0.5rem 1rem;
            border-radius: 8px;
            margin-top: 0.5rem;
        }

        .balance {
            font-size: 1.5rem;
            font-weight: 700;
            margin-top: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="logo">🛡️</div>
            <h1 class="title">Himaya Insurance</h1>
            <p class="subtitle">Blockchain-Powered Vehicle Insurance Platform</p>
        </div>

        <!-- Status Bar -->
        <div class="status-bar">
            <div class="status-item">
                <div class="status-dot" id="walletStatus"></div>
                <div>Wallet</div>
                <div id="walletText">Disconnected</div>
            </div>
            <div class="status-item">
                <div class="status-dot" id="blockchainStatus"></div>
                <div>Blockchain</div>
                <div id="blockchainText">Disconnected</div>
            </div>
            <div class="status-item">
                <div class="status-dot" id="databaseStatus"></div>
                <div>Database</div>
                <div id="databaseText">Disconnected</div>
            </div>
            <div class="status-item">
                <div class="status-dot connected"></div>
                <div>System</div>
                <div>Ready</div>
            </div>
        </div>

        <!-- Wallet Section -->
        <div class="wallet-section" id="walletSection" style="display: none;">
            <div class="wallet-info">
                <div class="account-info">
                    <h3><i class="fas fa-wallet"></i> Connected Wallet</h3>
                    <div class="account-address" id="accountAddress"></div>
                    <div class="balance" id="accountBalance">0 ETH</div>
                </div>
                <button class="btn" onclick="disconnectWallet()" style="background: rgba(255,255,255,0.2);">
                    <i class="fas fa-sign-out-alt"></i> Disconnect
                </button>
            </div>
        </div>

        <!-- Connect Wallet Button -->
        <div class="card" id="connectSection">
            <div class="card-title">
                <i class="fas fa-link"></i> Connect to Get Started
            </div>
            <p style="margin-bottom: 1.5rem;">Connect your MetaMask wallet to access the insurance platform.</p>
            <button class="btn" onclick="connectWallet()">
                <i class="fas fa-wallet"></i> Connect MetaMask Wallet
            </button>
        </div>

        <!-- Navigation Tabs -->
        <div class="nav-tabs" id="mainTabs" style="display: none;">
            <button class="tab-btn active" onclick="showTab('dashboard')">
                <i class="fas fa-tachometer-alt"></i> Dashboard
            </button>
            <button class="tab-btn" onclick="showTab('vehicles')">
                <i class="fas fa-car"></i> Vehicles
            </button>
            <button class="tab-btn" onclick="showTab('policies')">
                <i class="fas fa-shield-alt"></i> Policies
            </button>
            <button class="tab-btn" onclick="showTab('claims')">
                <i class="fas fa-file-medical"></i> Claims
            </button>
            <button class="tab-btn" onclick="showTab('admin')">
                <i class="fas fa-cog"></i> Admin
            </button>
        </div>

        <!-- Dashboard Tab -->
        <div class="tab-content active" id="dashboard">
            <div class="card-title">
                <i class="fas fa-tachometer-alt"></i> Dashboard Overview
            </div>
            <div class="grid">
                <div class="card">
                    <h3><i class="fas fa-car"></i> My Vehicles</h3>
                    <div style="font-size: 2rem; font-weight: 700; color: #667eea;" id="vehicleCount">0</div>
                    <p>Registered vehicles</p>
                </div>
                <div class="card">
                    <h3><i class="fas fa-shield-alt"></i> Active Policies</h3>
                    <div style="font-size: 2rem; font-weight: 700; color: #10b981;" id="policyCount">0</div>
                    <p>Insurance policies</p>
                </div>
                <div class="card">
                    <h3><i class="fas fa-file-medical"></i> Claims</h3>
                    <div style="font-size: 2rem; font-weight: 700; color: #f59e0b;" id="claimCount">0</div>
                    <p>Submitted claims</p>
                </div>
                <div class="card">
                    <h3><i class="fas fa-coins"></i> Total Coverage</h3>
                    <div style="font-size: 2rem; font-weight: 700; color: #8b5cf6;" id="totalCoverage">0 ETH</div>
                    <p>Insurance coverage</p>
                </div>
            </div>

            <div class="card">
                <h3><i class="fas fa-chart-line"></i> Recent Activity</h3>
                <div id="recentActivity">
                    <p style="text-align: center; color: #6b7280; padding: 2rem;">No recent activity</p>
                </div>
            </div>
        </div>

        <!-- Vehicles Tab -->
        <div class="tab-content" id="vehicles">
            <div class="card-title">
                <i class="fas fa-car"></i> Vehicle Management
            </div>

            <div class="card">
                <h3><i class="fas fa-plus"></i> Register New Vehicle</h3>
                <form id="vehicleForm">
                    <div class="grid">
                        <div class="form-group">
                            <label class="form-label">VIN Number</label>
                            <input type="text" class="form-input" id="vehicleVin" placeholder="Enter VIN number" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Make</label>
                            <input type="text" class="form-input" id="vehicleMake" placeholder="e.g., Toyota" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Model</label>
                            <input type="text" class="form-input" id="vehicleModel" placeholder="e.g., Camry" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Year</label>
                            <input type="number" class="form-input" id="vehicleYear" placeholder="2023" min="1900" max="2024" required>
                        </div>
                    </div>
                    <button type="submit" class="btn">
                        <i class="fas fa-save"></i> Register Vehicle
                    </button>
                </form>
            </div>

            <div class="card">
                <h3><i class="fas fa-list"></i> My Vehicles</h3>
                <div id="vehiclesList">
                    <p style="text-align: center; color: #6b7280; padding: 2rem;">No vehicles registered</p>
                </div>
            </div>
        </div>

        <!-- Policies Tab -->
        <div class="tab-content" id="policies">
            <div class="card-title">
                <i class="fas fa-shield-alt"></i> Insurance Policies
            </div>

            <div class="card">
                <h3><i class="fas fa-plus"></i> Create New Policy</h3>
                <form id="policyForm">
                    <div class="grid">
                        <div class="form-group">
                            <label class="form-label">Select Vehicle</label>
                            <select class="form-input" id="policyVehicle" required>
                                <option value="">Select a vehicle</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Coverage Type</label>
                            <select class="form-input" id="policyCoverage" required>
                                <option value="">Select coverage</option>
                                <option value="basic">Basic Coverage</option>
                                <option value="comprehensive">Comprehensive</option>
                                <option value="premium">Premium</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Premium Amount (ETH)</label>
                            <input type="number" class="form-input" id="policyPremium" placeholder="0.1" step="0.001" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Coverage Amount (ETH)</label>
                            <input type="number" class="form-input" id="policyCoverageAmount" placeholder="10" step="0.1" required>
                        </div>
                    </div>
                    <button type="submit" class="btn">
                        <i class="fas fa-shield-alt"></i> Create Policy
                    </button>
                </form>
            </div>

            <div class="card">
                <h3><i class="fas fa-list"></i> My Policies</h3>
                <div id="policiesList">
                    <p style="text-align: center; color: #6b7280; padding: 2rem;">No policies created</p>
                </div>
            </div>
        </div>

        <!-- Claims Tab -->
        <div class="tab-content" id="claims">
            <div class="card-title">
                <i class="fas fa-file-medical"></i> Insurance Claims
            </div>

            <div class="card">
                <h3><i class="fas fa-plus"></i> Submit New Claim</h3>
                <form id="claimForm">
                    <div class="grid">
                        <div class="form-group">
                            <label class="form-label">Select Policy</label>
                            <select class="form-input" id="claimPolicy" required>
                                <option value="">Select a policy</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Claim Amount (ETH)</label>
                            <input type="number" class="form-input" id="claimAmount" placeholder="1.0" step="0.001" required>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Description</label>
                        <textarea class="form-input" id="claimDescription" rows="4" placeholder="Describe the incident..." required></textarea>
                    </div>
                    <button type="submit" class="btn">
                        <i class="fas fa-file-medical"></i> Submit Claim
                    </button>
                </form>
            </div>

            <div class="card">
                <h3><i class="fas fa-list"></i> My Claims</h3>
                <div id="claimsList">
                    <p style="text-align: center; color: #6b7280; padding: 2rem;">No claims submitted</p>
                </div>
            </div>
        </div>

        <!-- Admin Tab -->
        <div class="tab-content" id="admin">
            <div class="card-title">
                <i class="fas fa-cog"></i> Admin Panel
            </div>

            <div class="grid">
                <div class="card">
                    <h3><i class="fas fa-database"></i> Database Status</h3>
                    <button class="btn" onclick="initializeDatabase()">
                        <i class="fas fa-sync"></i> Initialize Database
                    </button>
                    <button class="btn" onclick="clearDatabase()" style="background: #ef4444; margin-left: 1rem;">
                        <i class="fas fa-trash"></i> Clear Database
                    </button>
                </div>

                <div class="card">
                    <h3><i class="fas fa-link"></i> Blockchain Sync</h3>
                    <button class="btn" onclick="syncWithBlockchain()">
                        <i class="fas fa-sync"></i> Sync Data
                    </button>
                    <button class="btn" onclick="deployContracts()" style="margin-left: 1rem;">
                        <i class="fas fa-rocket"></i> Deploy Contracts
                    </button>
                </div>
            </div>

            <div class="card">
                <h3><i class="fas fa-file-medical"></i> Pending Claims</h3>
                <div id="pendingClaims">
                    <p style="text-align: center; color: #6b7280; padding: 2rem;">No pending claims</p>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/ethers/6.7.1/ethers.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/sql.js/1.8.0/sql-wasm.js"></script>
    <script>
        // Global variables
        let provider = null;
        let signer = null;
        let account = null;
        let contracts = {};
        let db = null;

        // Contract addresses (update these with your deployed contracts)
        const CONTRACT_ADDRESSES = {
            vehicleRegistry: '******************************************',
            insurancePolicy: '******************************************',
            claimManager: '******************************************'
        };

        // Contract ABIs (simplified for demo)
        const CONTRACT_ABIS = {
            vehicleRegistry: [
                "function registerVehicle(string memory vin, string memory make, string memory model, uint256 year) public",
                "function getVehicle(uint256 vehicleId) public view returns (string memory, string memory, string memory, uint256, address)",
                "function getVehiclesByOwner(address owner) public view returns (uint256[] memory)",
                "event VehicleRegistered(uint256 indexed vehicleId, address indexed owner, string vin)"
            ],
            insurancePolicy: [
                "function createPolicy(uint256 vehicleId, uint256 premium, uint256 coverageAmount, string memory coverageType) public payable",
                "function getPolicy(uint256 policyId) public view returns (uint256, uint256, uint256, string memory, address, bool)",
                "function getPoliciesByOwner(address owner) public view returns (uint256[] memory)",
                "event PolicyCreated(uint256 indexed policyId, address indexed owner, uint256 vehicleId)"
            ],
            claimManager: [
                "function submitClaim(uint256 policyId, uint256 amount, string memory description) public",
                "function approveClaim(uint256 claimId) public",
                "function getClaim(uint256 claimId) public view returns (uint256, uint256, string memory, address, bool, bool)",
                "function getClaimsByOwner(address owner) public view returns (uint256[] memory)",
                "event ClaimSubmitted(uint256 indexed claimId, address indexed claimant, uint256 policyId)"
            ]
        };

        // Initialize the application
        async function init() {
            await initializeDatabase();
            updateStatus();
            setupEventListeners();
        }

        // Initialize SQLite database
        async function initializeDatabase() {
            try {
                const SQL = await initSqlJs({
                    locateFile: file => `https://cdnjs.cloudflare.com/ajax/libs/sql.js/1.8.0/${file}`
                });

                db = new SQL.Database();

                // Create tables
                db.run(`
                    CREATE TABLE IF NOT EXISTS vehicles (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        vin TEXT UNIQUE,
                        make TEXT,
                        model TEXT,
                        year INTEGER,
                        owner TEXT,
                        blockchain_id INTEGER,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                `);

                db.run(`
                    CREATE TABLE IF NOT EXISTS policies (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        vehicle_id INTEGER,
                        premium REAL,
                        coverage_amount REAL,
                        coverage_type TEXT,
                        owner TEXT,
                        blockchain_id INTEGER,
                        active BOOLEAN DEFAULT 1,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (vehicle_id) REFERENCES vehicles (id)
                    )
                `);

                db.run(`
                    CREATE TABLE IF NOT EXISTS claims (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        policy_id INTEGER,
                        amount REAL,
                        description TEXT,
                        owner TEXT,
                        blockchain_id INTEGER,
                        approved BOOLEAN DEFAULT 0,
                        processed BOOLEAN DEFAULT 0,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (policy_id) REFERENCES policies (id)
                    )
                `);

                updateDatabaseStatus(true);
                showNotification('Database initialized successfully!');
            } catch (error) {
                console.error('Database initialization failed:', error);
                updateDatabaseStatus(false);
                showNotification('Database initialization failed!', 'error');
            }
        }

        // Connect to MetaMask wallet
        async function connectWallet() {
            if (typeof window.ethereum !== 'undefined') {
                try {
                    // Request account access
                    const accounts = await window.ethereum.request({ method: 'eth_requestAccounts' });

                    if (accounts.length === 0) {
                        throw new Error('No accounts found. Please unlock MetaMask.');
                    }

                    provider = new ethers.BrowserProvider(window.ethereum);
                    signer = await provider.getSigner();
                    account = await signer.getAddress();

                    // Check network
                    const network = await provider.getNetwork();
                    console.log('Connected to network:', network.chainId);

                    if (network.chainId !== 1337n) {
                        showNotification('Please switch to the local network (Chain ID: 1337)', 'error');
                        await switchToLocalNetwork();
                        return;
                    }

                    // Try to initialize contracts (optional - app works without them)
                    try {
                        contracts.vehicleRegistry = new ethers.Contract(
                            CONTRACT_ADDRESSES.vehicleRegistry,
                            CONTRACT_ABIS.vehicleRegistry,
                            signer
                        );
                        contracts.insurancePolicy = new ethers.Contract(
                            CONTRACT_ADDRESSES.insurancePolicy,
                            CONTRACT_ABIS.insurancePolicy,
                            signer
                        );
                        contracts.claimManager = new ethers.Contract(
                            CONTRACT_ADDRESSES.claimManager,
                            CONTRACT_ABIS.claimManager,
                            signer
                        );
                        console.log('Smart contracts initialized');
                    } catch (contractError) {
                        console.warn('Smart contracts not available:', contractError.message);
                        showNotification('Connected! (Smart contracts not deployed - using database only)', 'warning');
                    }

                    updateWalletStatus(true);
                    updateBlockchainStatus(true);
                    showConnectedInterface();
                    await updateBalance();
                    await loadData();

                    showNotification('Wallet connected successfully!');
                } catch (error) {
                    console.error('Wallet connection failed:', error);
                    let errorMessage = 'Failed to connect wallet!';

                    if (error.code === 4001) {
                        errorMessage = 'Connection rejected by user';
                    } else if (error.code === -32002) {
                        errorMessage = 'Connection request already pending';
                    } else if (error.message.includes('No accounts')) {
                        errorMessage = 'No accounts found. Please unlock MetaMask.';
                    }

                    showNotification(errorMessage, 'error');
                }
            } else {
                showNotification('MetaMask not found! Please install MetaMask extension.', 'error');
                window.open('https://metamask.io/download/', '_blank');
            }
        }

        // Switch to local network
        async function switchToLocalNetwork() {
            try {
                await window.ethereum.request({
                    method: 'wallet_switchEthereumChain',
                    params: [{ chainId: '0x539' }], // 1337 in hex
                });
                showNotification('Switched to local network!');
                // Retry connection
                setTimeout(connectWallet, 1000);
            } catch (switchError) {
                if (switchError.code === 4902) {
                    // Network doesn't exist, add it
                    try {
                        await window.ethereum.request({
                            method: 'wallet_addEthereumChain',
                            params: [{
                                chainId: '0x539',
                                chainName: 'Himaya Local Network',
                                nativeCurrency: {
                                    name: 'Ethereum',
                                    symbol: 'ETH',
                                    decimals: 18,
                                },
                                rpcUrls: ['http://localhost:8545'],
                                blockExplorerUrls: null,
                            }],
                        });
                        showNotification('Local network added! Please try connecting again.');
                    } catch (addError) {
                        console.error('Failed to add network:', addError);
                        showNotification('Failed to add local network. Please add manually.', 'error');
                    }
                } else {
                    console.error('Failed to switch network:', switchError);
                    showNotification('Failed to switch network. Please switch manually.', 'error');
                }
            }
        }

        // Disconnect wallet
        function disconnectWallet() {
            provider = null;
            signer = null;
            account = null;
            contracts = {};

            updateWalletStatus(false);
            updateBlockchainStatus(false);
            showDisconnectedInterface();
            showNotification('Wallet disconnected');
        }

        // Update wallet balance
        async function updateBalance() {
            if (provider && account) {
                try {
                    const balance = await provider.getBalance(account);
                    const balanceInEth = ethers.formatEther(balance);
                    document.getElementById('accountBalance').textContent = `${parseFloat(balanceInEth).toFixed(4)} ETH`;
                } catch (error) {
                    console.error('Failed to get balance:', error);
                }
            }
        }

        // Show connected interface
        function showConnectedInterface() {
            document.getElementById('connectSection').style.display = 'none';
            document.getElementById('walletSection').style.display = 'block';
            document.getElementById('mainTabs').style.display = 'flex';
            document.getElementById('accountAddress').textContent = account;
        }

        // Show disconnected interface
        function showDisconnectedInterface() {
            document.getElementById('connectSection').style.display = 'block';
            document.getElementById('walletSection').style.display = 'none';
            document.getElementById('mainTabs').style.display = 'none';

            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.style.display = 'none';
            });
        }

        // Update status indicators
        function updateWalletStatus(connected) {
            const dot = document.getElementById('walletStatus');
            const text = document.getElementById('walletText');
            if (connected) {
                dot.classList.add('connected');
                text.textContent = 'Connected';
            } else {
                dot.classList.remove('connected');
                text.textContent = 'Disconnected';
            }
        }

        function updateBlockchainStatus(connected) {
            const dot = document.getElementById('blockchainStatus');
            const text = document.getElementById('blockchainText');
            if (connected) {
                dot.classList.add('connected');
                text.textContent = 'Connected';
            } else {
                dot.classList.remove('connected');
                text.textContent = 'Disconnected';
            }
        }

        function updateDatabaseStatus(connected) {
            const dot = document.getElementById('databaseStatus');
            const text = document.getElementById('databaseText');
            if (connected) {
                dot.classList.add('connected');
                text.textContent = 'Connected';
            } else {
                dot.classList.remove('connected');
                text.textContent = 'Disconnected';
            }
        }

        function updateStatus() {
            updateWalletStatus(!!account);
            updateBlockchainStatus(!!provider);
            updateDatabaseStatus(!!db);
        }

        // Tab management
        function showTab(tabName) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });

            // Remove active class from all tab buttons
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });

            // Show selected tab
            document.getElementById(tabName).classList.add('active');

            // Add active class to clicked button
            event.target.classList.add('active');

            // Load data for the tab
            if (tabName === 'dashboard') {
                updateDashboard();
            } else if (tabName === 'vehicles') {
                loadVehicles();
            } else if (tabName === 'policies') {
                loadPolicies();
                updateVehicleDropdown();
            } else if (tabName === 'claims') {
                loadClaims();
                updatePolicyDropdown();
            } else if (tabName === 'admin') {
                loadPendingClaims();
            }
        }

        // Show notification
        function showNotification(message, type = 'success') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.remove();
            }, 4000);
        }

        // Setup event listeners
        function setupEventListeners() {
            // Vehicle form
            document.getElementById('vehicleForm').addEventListener('submit', async (e) => {
                e.preventDefault();
                await registerVehicle();
            });

            // Policy form
            document.getElementById('policyForm').addEventListener('submit', async (e) => {
                e.preventDefault();
                await createPolicy();
            });

            // Claim form
            document.getElementById('claimForm').addEventListener('submit', async (e) => {
                e.preventDefault();
                await submitClaim();
            });
        }

        // Vehicle management
        async function registerVehicle() {
            const vin = document.getElementById('vehicleVin').value.trim();
            const make = document.getElementById('vehicleMake').value.trim();
            const model = document.getElementById('vehicleModel').value.trim();
            const year = parseInt(document.getElementById('vehicleYear').value);

            if (!vin || !make || !model || !year) {
                showNotification('Please fill in all vehicle details', 'error');
                return;
            }

            if (vin.length < 10) {
                showNotification('VIN must be at least 10 characters long', 'error');
                return;
            }

            try {
                // Save to server database for persistence
                const response = await fetch('/api/vehicles', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        vin: vin,
                        make: make,
                        model: model,
                        year: year,
                        owner: account
                    })
                });

                const result = await response.json();

                if (!response.ok) {
                    if (result.error && result.error.includes('UNIQUE constraint')) {
                        showNotification('This VIN is already registered!', 'error');
                        return;
                    }
                    throw new Error(result.error || 'Failed to register vehicle');
                }

                // Also save to local database
                try {
                    db.run(`
                        INSERT OR REPLACE INTO vehicles (vin, make, model, year, owner)
                        VALUES (?, ?, ?, ?, ?)
                    `, [vin, make, model, year, account]);
                } catch (localError) {
                    console.warn('Local database save failed:', localError);
                }

                // Try to register on blockchain if contracts are available
                if (contracts.vehicleRegistry) {
                    try {
                        const tx = await contracts.vehicleRegistry.registerVehicle(vin, make, model, year);
                        await tx.wait();
                        showNotification('Vehicle registered successfully on blockchain!');
                    } catch (blockchainError) {
                        console.warn('Blockchain registration failed:', blockchainError);
                        showNotification('Vehicle saved to database (blockchain unavailable)', 'warning');
                    }
                } else {
                    showNotification('Vehicle registered successfully!');
                }

                document.getElementById('vehicleForm').reset();
                await loadVehicles();
                await updateDashboard();

            } catch (error) {
                console.error('Vehicle registration failed:', error);
                if (error.message.includes('fetch')) {
                    showNotification('Server connection failed - using local storage only', 'warning');
                    // Fallback to local storage
                    try {
                        db.run(`
                            INSERT OR REPLACE INTO vehicles (vin, make, model, year, owner)
                            VALUES (?, ?, ?, ?, ?)
                        `, [vin, make, model, year, account]);
                        showNotification('Vehicle saved locally');
                        document.getElementById('vehicleForm').reset();
                        await loadVehicles();
                        await updateDashboard();
                    } catch (localError) {
                        showNotification('Vehicle registration failed!', 'error');
                    }
                } else {
                    showNotification(error.message, 'error');
                }
            }
        }

        async function loadVehicles() {
            try {
                if (!account) return;

                let vehicles = [];

                // Try to load from server first
                try {
                    const response = await fetch(`/api/vehicles/${account}`);
                    if (response.ok) {
                        vehicles = await response.json();

                        // Also update local database
                        vehicles.forEach(vehicle => {
                            try {
                                db.run(`
                                    INSERT OR REPLACE INTO vehicles (id, vin, make, model, year, owner, blockchain_id, created_at)
                                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                                `, [vehicle.id, vehicle.vin, vehicle.make, vehicle.model, vehicle.year, vehicle.owner, vehicle.blockchain_id, vehicle.created_at]);
                            } catch (localError) {
                                console.warn('Local database update failed:', localError);
                            }
                        });
                    }
                } catch (serverError) {
                    console.warn('Server load failed, using local data:', serverError);

                    // Fallback to local database
                    if (db) {
                        const result = db.exec("SELECT * FROM vehicles WHERE owner = ? ORDER BY created_at DESC", [account]);
                        vehicles = result.length > 0 ? result[0].values.map(row => ({
                            id: row[0],
                            vin: row[1],
                            make: row[2],
                            model: row[3],
                            year: row[4],
                            owner: row[5],
                            blockchain_id: row[6],
                            created_at: row[7]
                        })) : [];
                    }
                }

                const vehiclesList = document.getElementById('vehiclesList');
                if (vehicles.length === 0) {
                    vehiclesList.innerHTML = '<p style="text-align: center; color: #6b7280; padding: 2rem;">No vehicles registered</p>';
                    return;
                }

                let html = '<table class="data-table"><thead><tr><th>VIN</th><th>Make</th><th>Model</th><th>Year</th><th>Status</th></tr></thead><tbody>';
                vehicles.forEach(vehicle => {
                    html += `
                        <tr>
                            <td>${vehicle.vin}</td>
                            <td>${vehicle.make}</td>
                            <td>${vehicle.model}</td>
                            <td>${vehicle.year}</td>
                            <td><span class="badge success">Registered</span></td>
                        </tr>
                    `;
                });
                html += '</tbody></table>';
                vehiclesList.innerHTML = html;
            } catch (error) {
                console.error('Failed to load vehicles:', error);
            }
        }

        // Policy management
        async function createPolicy() {
            const vehicleId = document.getElementById('policyVehicle').value;
            const coverage = document.getElementById('policyCoverage').value;
            const premium = parseFloat(document.getElementById('policyPremium').value);
            const coverageAmount = parseFloat(document.getElementById('policyCoverageAmount').value);

            if (!vehicleId || !coverage || !premium || !coverageAmount) {
                showNotification('Please fill in all policy details', 'error');
                return;
            }

            if (premium <= 0 || coverageAmount <= 0) {
                showNotification('Premium and coverage amounts must be greater than 0', 'error');
                return;
            }

            try {
                // Save to server database for persistence
                const response = await fetch('/api/policies', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        vehicle_id: vehicleId,
                        premium: premium,
                        coverage_amount: coverageAmount,
                        coverage_type: coverage,
                        owner: account
                    })
                });

                const result = await response.json();

                if (!response.ok) {
                    throw new Error(result.error || 'Failed to create policy');
                }

                // Also save to local database
                try {
                    db.run(`
                        INSERT INTO policies (vehicle_id, premium, coverage_amount, coverage_type, owner)
                        VALUES (?, ?, ?, ?, ?)
                    `, [vehicleId, premium, coverageAmount, coverage, account]);
                } catch (localError) {
                    console.warn('Local database save failed:', localError);
                }

                // Try to create on blockchain if contracts are available
                if (contracts.insurancePolicy && provider) {
                    try {
                        const premiumWei = ethers.parseEther(premium.toString());
                        const tx = await contracts.insurancePolicy.createPolicy(
                            vehicleId, premiumWei, ethers.parseEther(coverageAmount.toString()), coverage,
                            { value: premiumWei }
                        );
                        await tx.wait();
                        showNotification('Policy created successfully on blockchain!');
                    } catch (blockchainError) {
                        console.warn('Blockchain policy creation failed:', blockchainError);
                        showNotification('Policy saved to database (blockchain unavailable)', 'warning');
                    }
                } else {
                    showNotification('Policy created successfully!');
                }

                document.getElementById('policyForm').reset();
                await loadPolicies();
                await updateDashboard();
                await updateVehicleDropdown();
                await updatePolicyDropdown();

            } catch (error) {
                console.error('Policy creation failed:', error);
                if (error.message.includes('fetch')) {
                    showNotification('Server connection failed - using local storage only', 'warning');
                    // Fallback to local storage
                    try {
                        db.run(`
                            INSERT INTO policies (vehicle_id, premium, coverage_amount, coverage_type, owner)
                            VALUES (?, ?, ?, ?, ?)
                        `, [vehicleId, premium, coverageAmount, coverage, account]);
                        showNotification('Policy saved locally');
                        document.getElementById('policyForm').reset();
                        await loadPolicies();
                        await updateDashboard();
                    } catch (localError) {
                        showNotification('Policy creation failed!', 'error');
                    }
                } else {
                    showNotification(error.message, 'error');
                }
            }
        }

        async function loadPolicies() {
            try {
                if (!account) return;

                let policies = [];

                // Try to load from server first
                try {
                    const response = await fetch(`/api/policies/${account}`);
                    if (response.ok) {
                        policies = await response.json();
                    }
                } catch (serverError) {
                    console.warn('Server load failed, using local data:', serverError);

                    // Fallback to local database
                    if (db) {
                        const result = db.exec(`
                            SELECT p.*, v.make, v.model, v.year
                            FROM policies p
                            JOIN vehicles v ON p.vehicle_id = v.id
                            WHERE p.owner = ?
                            ORDER BY p.created_at DESC
                        `, [account]);

                        policies = result.length > 0 ? result[0].values.map(row => ({
                            id: row[0],
                            vehicle_id: row[1],
                            premium: row[2],
                            coverage_amount: row[3],
                            coverage_type: row[4],
                            owner: row[5],
                            blockchain_id: row[6],
                            active: row[7],
                            created_at: row[8],
                            make: row[9],
                            model: row[10],
                            year: row[11]
                        })) : [];
                    }
                }

                const policiesList = document.getElementById('policiesList');
                if (policies.length === 0) {
                    policiesList.innerHTML = '<p style="text-align: center; color: #6b7280; padding: 2rem;">No policies created</p>';
                    return;
                }

                let html = '<table class="data-table"><thead><tr><th>Vehicle</th><th>Coverage</th><th>Premium</th><th>Coverage Amount</th><th>Status</th></tr></thead><tbody>';
                policies.forEach(policy => {
                    html += `
                        <tr>
                            <td>${policy.make} ${policy.model} (${policy.year})</td>
                            <td>${policy.coverage_type}</td>
                            <td>${policy.premium} ETH</td>
                            <td>${policy.coverage_amount} ETH</td>
                            <td><span class="badge ${policy.active ? 'success' : 'error'}">${policy.active ? 'Active' : 'Inactive'}</span></td>
                        </tr>
                    `;
                });
                html += '</tbody></table>';
                policiesList.innerHTML = html;
            } catch (error) {
                console.error('Failed to load policies:', error);
            }
        }

        // Claims management
        async function submitClaim() {
            const policyId = document.getElementById('claimPolicy').value;
            const amount = parseFloat(document.getElementById('claimAmount').value);
            const description = document.getElementById('claimDescription').value.trim();

            if (!policyId || !amount || !description) {
                showNotification('Please fill in all claim details', 'error');
                return;
            }

            if (amount <= 0) {
                showNotification('Claim amount must be greater than 0', 'error');
                return;
            }

            try {
                // Save to server database for persistence
                const response = await fetch('/api/claims', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        policy_id: policyId,
                        amount: amount,
                        description: description,
                        owner: account
                    })
                });

                const result = await response.json();

                if (!response.ok) {
                    throw new Error(result.error || 'Failed to submit claim');
                }

                // Also save to local database
                try {
                    db.run(`
                        INSERT INTO claims (policy_id, amount, description, owner)
                        VALUES (?, ?, ?, ?)
                    `, [policyId, amount, description, account]);
                } catch (localError) {
                    console.warn('Local database save failed:', localError);
                }

                // Try to submit on blockchain if contracts are available
                if (contracts.claimManager) {
                    try {
                        const tx = await contracts.claimManager.submitClaim(
                            policyId, ethers.parseEther(amount.toString()), description
                        );
                        await tx.wait();
                        showNotification('Claim submitted successfully on blockchain!');
                    } catch (blockchainError) {
                        console.warn('Blockchain claim submission failed:', blockchainError);
                        showNotification('Claim saved to database (blockchain unavailable)', 'warning');
                    }
                } else {
                    showNotification('Claim submitted successfully!');
                }

                document.getElementById('claimForm').reset();
                await loadClaims();
                await updateDashboard();

            } catch (error) {
                console.error('Claim submission failed:', error);
                if (error.message.includes('fetch')) {
                    showNotification('Server connection failed - using local storage only', 'warning');
                    // Fallback to local storage
                    try {
                        db.run(`
                            INSERT INTO claims (policy_id, amount, description, owner)
                            VALUES (?, ?, ?, ?)
                        `, [policyId, amount, description, account]);
                        showNotification('Claim saved locally');
                        document.getElementById('claimForm').reset();
                        await loadClaims();
                        await updateDashboard();
                    } catch (localError) {
                        showNotification('Claim submission failed!', 'error');
                    }
                } else {
                    showNotification(error.message, 'error');
                }
            }
        }

        async function loadClaims() {
            try {
                if (!account) return;

                let claims = [];

                // Try to load from server first
                try {
                    const response = await fetch(`/api/claims/${account}`);
                    if (response.ok) {
                        claims = await response.json();
                    }
                } catch (serverError) {
                    console.warn('Server load failed, using local data:', serverError);

                    // Fallback to local database
                    if (db) {
                        const result = db.exec(`
                            SELECT c.*, p.coverage_type, v.make, v.model
                            FROM claims c
                            JOIN policies p ON c.policy_id = p.id
                            JOIN vehicles v ON p.vehicle_id = v.id
                            WHERE c.owner = ?
                            ORDER BY c.created_at DESC
                        `, [account]);

                        claims = result.length > 0 ? result[0].values.map(row => ({
                            id: row[0],
                            policy_id: row[1],
                            amount: row[2],
                            description: row[3],
                            owner: row[4],
                            blockchain_id: row[5],
                            approved: row[6],
                            processed: row[7],
                            created_at: row[8],
                            coverage_type: row[9],
                            make: row[10],
                            model: row[11]
                        })) : [];
                    }
                }

                const claimsList = document.getElementById('claimsList');
                if (claims.length === 0) {
                    claimsList.innerHTML = '<p style="text-align: center; color: #6b7280; padding: 2rem;">No claims submitted</p>';
                    return;
                }

                let html = '<table class="data-table"><thead><tr><th>Vehicle</th><th>Amount</th><th>Description</th><th>Status</th></tr></thead><tbody>';
                claims.forEach(claim => {
                    let status = 'Pending';
                    let statusClass = 'warning';
                    if (claim.processed) {
                        status = claim.approved ? 'Approved' : 'Rejected';
                        statusClass = claim.approved ? 'success' : 'error';
                    }

                    html += `
                        <tr>
                            <td>${claim.make} ${claim.model}</td>
                            <td>${claim.amount} ETH</td>
                            <td>${claim.description}</td>
                            <td><span class="badge ${statusClass}">${status}</span></td>
                        </tr>
                    `;
                });
                html += '</tbody></table>';
                claimsList.innerHTML = html;
            } catch (error) {
                console.error('Failed to load claims:', error);
            }
        }

        // Utility functions
        async function updateVehicleDropdown() {
            if (!account) return;

            try {
                let vehicles = [];

                // Try to load from server first
                try {
                    const response = await fetch(`/api/vehicles/${account}`);
                    if (response.ok) {
                        vehicles = await response.json();
                    }
                } catch (serverError) {
                    // Fallback to local database
                    if (db) {
                        const result = db.exec("SELECT * FROM vehicles WHERE owner = ?", [account]);
                        vehicles = result.length > 0 ? result[0].values.map(row => ({
                            id: row[0],
                            vin: row[1],
                            make: row[2],
                            model: row[3],
                            year: row[4],
                            owner: row[5]
                        })) : [];
                    }
                }

                const select = document.getElementById('policyVehicle');
                select.innerHTML = '<option value="">Select a vehicle</option>';

                vehicles.forEach(vehicle => {
                    const option = document.createElement('option');
                    option.value = vehicle.id;
                    option.textContent = `${vehicle.make} ${vehicle.model} (${vehicle.year}) - ${vehicle.vin}`;
                    select.appendChild(option);
                });
            } catch (error) {
                console.error('Failed to update vehicle dropdown:', error);
            }
        }

        async function updatePolicyDropdown() {
            if (!account) return;

            try {
                let policies = [];

                // Try to load from server first
                try {
                    const response = await fetch(`/api/policies/${account}`);
                    if (response.ok) {
                        const allPolicies = await response.json();
                        policies = allPolicies.filter(policy => policy.active);
                    }
                } catch (serverError) {
                    // Fallback to local database
                    if (db) {
                        const result = db.exec(`
                            SELECT p.*, v.make, v.model, v.year
                            FROM policies p
                            JOIN vehicles v ON p.vehicle_id = v.id
                            WHERE p.owner = ? AND p.active = 1
                        `, [account]);

                        policies = result.length > 0 ? result[0].values.map(row => ({
                            id: row[0],
                            vehicle_id: row[1],
                            premium: row[2],
                            coverage_amount: row[3],
                            coverage_type: row[4],
                            owner: row[5],
                            blockchain_id: row[6],
                            active: row[7],
                            created_at: row[8],
                            make: row[9],
                            model: row[10],
                            year: row[11]
                        })) : [];
                    }
                }

                const select = document.getElementById('claimPolicy');
                select.innerHTML = '<option value="">Select a policy</option>';

                policies.forEach(policy => {
                    const option = document.createElement('option');
                    option.value = policy.id;
                    option.textContent = `${policy.make} ${policy.model} (${policy.year}) - ${policy.coverage_type}`;
                    select.appendChild(option);
                });
            } catch (error) {
                console.error('Failed to update policy dropdown:', error);
            }
        }

        async function updateDashboard() {
            if (!account) return;

            try {
                // Try to load from server first
                try {
                    const response = await fetch(`/api/dashboard/${account}`);
                    if (response.ok) {
                        const stats = await response.json();
                        document.getElementById('vehicleCount').textContent = stats.vehicles || 0;
                        document.getElementById('policyCount').textContent = stats.policies || 0;
                        document.getElementById('claimCount').textContent = stats.claims || 0;
                        document.getElementById('totalCoverage').textContent = `${stats.totalCoverage || 0} ETH`;
                    } else {
                        throw new Error('Server stats unavailable');
                    }
                } catch (serverError) {
                    console.warn('Server dashboard failed, using local data:', serverError);

                    // Fallback to local database
                    if (db) {
                        // Count vehicles
                        const vehicleResult = db.exec("SELECT COUNT(*) FROM vehicles WHERE owner = ?", [account]);
                        const vehicleCount = vehicleResult.length > 0 ? vehicleResult[0].values[0][0] : 0;
                        document.getElementById('vehicleCount').textContent = vehicleCount;

                        // Count policies
                        const policyResult = db.exec("SELECT COUNT(*) FROM policies WHERE owner = ? AND active = 1", [account]);
                        const policyCount = policyResult.length > 0 ? policyResult[0].values[0][0] : 0;
                        document.getElementById('policyCount').textContent = policyCount;

                        // Count claims
                        const claimResult = db.exec("SELECT COUNT(*) FROM claims WHERE owner = ?", [account]);
                        const claimCount = claimResult.length > 0 ? claimResult[0].values[0][0] : 0;
                        document.getElementById('claimCount').textContent = claimCount;

                        // Calculate total coverage
                        const coverageResult = db.exec("SELECT SUM(coverage_amount) FROM policies WHERE owner = ? AND active = 1", [account]);
                        const totalCoverage = (coverageResult.length > 0 && coverageResult[0].values[0][0]) ? coverageResult[0].values[0][0] : 0;
                        document.getElementById('totalCoverage').textContent = `${totalCoverage} ETH`;
                    }
                }

                // Load recent activity
                loadRecentActivity();
            } catch (error) {
                console.error('Failed to update dashboard:', error);
            }
        }

        function loadRecentActivity() {
            if (!db || !account) return;

            try {
                const activities = [];

                // Get recent vehicles
                const vehicles = db.exec(`
                    SELECT 'Vehicle Registered' as type, make || ' ' || model as description, created_at
                    FROM vehicles WHERE owner = ?
                    ORDER BY created_at DESC LIMIT 3
                `, [account]);

                if (vehicles[0]) {
                    vehicles[0].values.forEach(row => {
                        activities.push({
                            type: row[0],
                            description: row[1],
                            date: row[2]
                        });
                    });
                }

                // Get recent policies
                const policies = db.exec(`
                    SELECT 'Policy Created' as type, coverage_type || ' Coverage' as description, created_at
                    FROM policies WHERE owner = ?
                    ORDER BY created_at DESC LIMIT 3
                `, [account]);

                if (policies[0]) {
                    policies[0].values.forEach(row => {
                        activities.push({
                            type: row[0],
                            description: row[1],
                            date: row[2]
                        });
                    });
                }

                // Sort by date
                activities.sort((a, b) => new Date(b.date) - new Date(a.date));

                const activityDiv = document.getElementById('recentActivity');
                if (activities.length === 0) {
                    activityDiv.innerHTML = '<p style="text-align: center; color: #6b7280; padding: 2rem;">No recent activity</p>';
                    return;
                }

                let html = '<div>';
                activities.slice(0, 5).forEach(activity => {
                    html += `
                        <div style="padding: 1rem; border-bottom: 1px solid #e5e7eb; display: flex; justify-content: space-between;">
                            <div>
                                <strong>${activity.type}</strong><br>
                                <span style="color: #6b7280;">${activity.description}</span>
                            </div>
                            <div style="color: #6b7280; font-size: 0.8rem;">
                                ${new Date(activity.date).toLocaleDateString()}
                            </div>
                        </div>
                    `;
                });
                html += '</div>';
                activityDiv.innerHTML = html;
            } catch (error) {
                console.error('Failed to load recent activity:', error);
            }
        }

        // Admin functions
        async function loadPendingClaims() {
            try {
                let pendingClaims = [];

                // Try to load from server first
                try {
                    const response = await fetch('/api/claims/all');
                    if (response.ok) {
                        const allClaims = await response.json();
                        pendingClaims = allClaims.filter(claim => !claim.processed);
                    } else {
                        throw new Error('Server unavailable');
                    }
                } catch (serverError) {
                    console.warn('Server load failed, using local data:', serverError);

                    // Fallback to local database - get all pending claims from all users
                    if (db) {
                        const result = db.exec(`
                            SELECT c.*, p.coverage_type, v.make, v.model, v.year, v.vin
                            FROM claims c
                            JOIN policies p ON c.policy_id = p.id
                            JOIN vehicles v ON p.vehicle_id = v.id
                            WHERE c.processed = 0
                            ORDER BY c.created_at DESC
                        `);

                        pendingClaims = result.length > 0 ? result[0].values.map(row => ({
                            id: row[0],
                            policy_id: row[1],
                            amount: row[2],
                            description: row[3],
                            owner: row[4],
                            blockchain_id: row[5],
                            approved: row[6],
                            processed: row[7],
                            created_at: row[8],
                            coverage_type: row[9],
                            make: row[10],
                            model: row[11],
                            year: row[12],
                            vin: row[13]
                        })) : [];
                    }
                }

                const pendingDiv = document.getElementById('pendingClaims');

                if (pendingClaims.length === 0) {
                    pendingDiv.innerHTML = '<p style="text-align: center; color: #6b7280; padding: 2rem;">No pending claims</p>';
                    return;
                }

                let html = `
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Claim ID</th>
                                <th>Vehicle</th>
                                <th>Owner</th>
                                <th>Amount</th>
                                <th>Description</th>
                                <th>Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                `;

                pendingClaims.forEach(claim => {
                    const date = new Date(claim.created_at).toLocaleDateString();
                    const shortOwner = claim.owner ? `${claim.owner.substring(0, 6)}...${claim.owner.substring(claim.owner.length - 4)}` : 'Unknown';

                    html += `
                        <tr>
                            <td>#${claim.id}</td>
                            <td>${claim.make} ${claim.model} (${claim.year})<br><small>${claim.vin}</small></td>
                            <td>${shortOwner}</td>
                            <td>${claim.amount} ETH</td>
                            <td>${claim.description}</td>
                            <td>${date}</td>
                            <td>
                                <button class="btn" onclick="approveClaim(${claim.id})" style="background: #10b981; margin-right: 0.5rem; padding: 0.5rem 1rem; font-size: 0.8rem;">
                                    <i class="fas fa-check"></i> Approve
                                </button>
                                <button class="btn" onclick="rejectClaim(${claim.id})" style="background: #ef4444; padding: 0.5rem 1rem; font-size: 0.8rem;">
                                    <i class="fas fa-times"></i> Reject
                                </button>
                            </td>
                        </tr>
                    `;
                });

                html += '</tbody></table>';
                pendingDiv.innerHTML = html;

            } catch (error) {
                console.error('Failed to load pending claims:', error);
                const pendingDiv = document.getElementById('pendingClaims');
                pendingDiv.innerHTML = '<p style="text-align: center; color: #ef4444; padding: 2rem;">Error loading pending claims</p>';
            }
        }

        // Admin claim management functions
        async function approveClaim(claimId) {
            if (!confirm('Are you sure you want to approve this claim?')) {
                return;
            }

            try {
                // Try to update on server first
                try {
                    const response = await fetch(`/api/claims/${claimId}/approve`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        }
                    });

                    if (!response.ok) {
                        throw new Error('Server update failed');
                    }
                } catch (serverError) {
                    console.warn('Server update failed, updating locally:', serverError);
                }

                // Update local database
                if (db) {
                    db.run(`
                        UPDATE claims
                        SET approved = 1, processed = 1
                        WHERE id = ?
                    `, [claimId]);
                }

                // Try blockchain transaction if available
                if (contracts.claimManager) {
                    try {
                        const tx = await contracts.claimManager.approveClaim(claimId);
                        await tx.wait();
                        showNotification('Claim approved on blockchain!');
                    } catch (blockchainError) {
                        console.warn('Blockchain approval failed:', blockchainError);
                        showNotification('Claim approved locally (blockchain unavailable)', 'warning');
                    }
                } else {
                    showNotification('Claim approved successfully!');
                }

                // Reload pending claims
                await loadPendingClaims();

            } catch (error) {
                console.error('Failed to approve claim:', error);
                showNotification('Failed to approve claim!', 'error');
            }
        }

        async function rejectClaim(claimId) {
            if (!confirm('Are you sure you want to reject this claim?')) {
                return;
            }

            try {
                // Try to update on server first
                try {
                    const response = await fetch(`/api/claims/${claimId}/reject`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        }
                    });

                    if (!response.ok) {
                        throw new Error('Server update failed');
                    }
                } catch (serverError) {
                    console.warn('Server update failed, updating locally:', serverError);
                }

                // Update local database
                if (db) {
                    db.run(`
                        UPDATE claims
                        SET approved = 0, processed = 1
                        WHERE id = ?
                    `, [claimId]);
                }

                showNotification('Claim rejected');

                // Reload pending claims
                await loadPendingClaims();

            } catch (error) {
                console.error('Failed to reject claim:', error);
                showNotification('Failed to reject claim!', 'error');
            }
        }

        async function syncWithBlockchain() {
            showNotification('Syncing with blockchain...');
            // Implementation would sync local database with blockchain state
            setTimeout(() => {
                showNotification('Blockchain sync completed!');
            }, 2000);
        }

        async function deployContracts() {
            showNotification('Contract deployment is handled separately');
        }

        function clearDatabase() {
            if (confirm('Are you sure you want to clear all local data?')) {
                db.run("DELETE FROM vehicles");
                db.run("DELETE FROM policies");
                db.run("DELETE FROM claims");
                showNotification('Database cleared!');
                updateDashboard();
                loadVehicles();
                loadPolicies();
                loadClaims();
            }
        }

        // Load all data
        async function loadData() {
            if (account && db) {
                updateDashboard();
                loadVehicles();
                loadPolicies();
                loadClaims();
                updateVehicleDropdown();
                updatePolicyDropdown();
            }
        }

        // Initialize app when page loads
        window.addEventListener('load', init);
    </script>
</body>
</html>
