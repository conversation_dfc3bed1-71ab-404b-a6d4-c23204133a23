// Script to send 20 ETH to specified wallet address
const Web3 = require('web3');

// Configuration
const RECIPIENT_ADDRESS = '******************************************';
const AMOUNT_ETH = '20';
const GANACHE_URL = 'http://localhost:8545';

// Ganache default accounts (these have 100 ETH each by default)
const GANACHE_ACCOUNTS = [
    {
        address: '******************************************',
        privateKey: '0xc87509a1c067bbde78beb793e6fa76530b6382a4c0241e5e4a9ec0a0f44dc0d3'
    },
    {
        address: '******************************************',
        privateKey: '0xae6ae8e5ccbfb04590405997ee2d52d2b330726137b875053c36d94e974d162f'
    }
];

async function sendETH() {
    try {
        console.log('🚀 Starting ETH transfer...');
        console.log(`📍 Recipient: ${RECIPIENT_ADDRESS}`);
        console.log(`💰 Amount: ${AMOUNT_ETH} ETH`);
        console.log('');

        // Initialize Web3
        const web3 = new Web3(GANACHE_URL);
        
        // Check connection
        const isConnected = await web3.eth.net.isListening();
        if (!isConnected) {
            throw new Error('❌ Cannot connect to Ganache. Make sure it\'s running on http://localhost:8545');
        }
        console.log('✅ Connected to Ganache');

        // Use first Ganache account as sender
        const senderAccount = GANACHE_ACCOUNTS[0];
        console.log(`📤 Sender: ${senderAccount.address}`);

        // Check sender balance
        const senderBalance = await web3.eth.getBalance(senderAccount.address);
        const senderBalanceETH = web3.utils.fromWei(senderBalance, 'ether');
        console.log(`💳 Sender balance: ${senderBalanceETH} ETH`);

        if (parseFloat(senderBalanceETH) < parseFloat(AMOUNT_ETH)) {
            throw new Error(`❌ Insufficient balance. Need ${AMOUNT_ETH} ETH, have ${senderBalanceETH} ETH`);
        }

        // Check recipient balance before
        const recipientBalanceBefore = await web3.eth.getBalance(RECIPIENT_ADDRESS);
        const recipientBalanceBeforeETH = web3.utils.fromWei(recipientBalanceBefore, 'ether');
        console.log(`📥 Recipient balance before: ${recipientBalanceBeforeETH} ETH`);
        console.log('');

        // Prepare transaction
        const amountWei = web3.utils.toWei(AMOUNT_ETH, 'ether');
        const gasPrice = await web3.eth.getGasPrice();
        const gasLimit = 21000; // Standard ETH transfer gas limit

        const transaction = {
            from: senderAccount.address,
            to: RECIPIENT_ADDRESS,
            value: amountWei,
            gas: gasLimit,
            gasPrice: gasPrice
        };

        console.log('📋 Transaction details:');
        console.log(`   From: ${transaction.from}`);
        console.log(`   To: ${transaction.to}`);
        console.log(`   Value: ${AMOUNT_ETH} ETH`);
        console.log(`   Gas: ${gasLimit}`);
        console.log(`   Gas Price: ${web3.utils.fromWei(gasPrice, 'gwei')} Gwei`);
        console.log('');

        // Sign and send transaction
        console.log('🔐 Signing transaction...');
        const signedTx = await web3.eth.accounts.signTransaction(transaction, senderAccount.privateKey);
        
        console.log('📡 Sending transaction...');
        const receipt = await web3.eth.sendSignedTransaction(signedTx.rawTransaction);
        
        console.log('✅ Transaction successful!');
        console.log(`🔗 Transaction Hash: ${receipt.transactionHash}`);
        console.log(`📦 Block Number: ${receipt.blockNumber}`);
        console.log(`⛽ Gas Used: ${receipt.gasUsed}`);
        console.log('');

        // Check recipient balance after
        const recipientBalanceAfter = await web3.eth.getBalance(RECIPIENT_ADDRESS);
        const recipientBalanceAfterETH = web3.utils.fromWei(recipientBalanceAfter, 'ether');
        console.log(`📥 Recipient balance after: ${recipientBalanceAfterETH} ETH`);
        
        const difference = parseFloat(recipientBalanceAfterETH) - parseFloat(recipientBalanceBeforeETH);
        console.log(`📈 Balance increase: ${difference.toFixed(4)} ETH`);
        console.log('');
        console.log('🎉 Transfer completed successfully!');

        return {
            success: true,
            txHash: receipt.transactionHash,
            blockNumber: receipt.blockNumber,
            gasUsed: receipt.gasUsed,
            recipientBalanceAfter: recipientBalanceAfterETH
        };

    } catch (error) {
        console.error('❌ Error sending ETH:', error.message);
        
        if (error.message.includes('connect')) {
            console.log('');
            console.log('💡 Troubleshooting:');
            console.log('1. Make sure Ganache is running');
            console.log('2. Check that Ganache is on port 8545');
            console.log('3. Verify network settings');
        }
        
        return {
            success: false,
            error: error.message
        };
    }
}

// Alternative function for MetaMask/Mainnet (DO NOT USE ON MAINNET WITHOUT PROPER SETUP)
async function sendETHMetaMask() {
    console.log('⚠️  MetaMask transfer function (for reference only)');
    console.log('⚠️  DO NOT USE ON MAINNET WITHOUT PROPER SECURITY MEASURES');
    
    // This would require proper private key management and security
    // Only use on testnets or local networks
    
    return {
        success: false,
        error: 'MetaMask transfer not implemented for security reasons'
    };
}

// Function to fund multiple test accounts
async function fundTestAccounts() {
    const testAccounts = [
        '******************************************', // Your specified address
        '******************************************', // Demo address from app
    ];
    
    console.log('🏦 Funding multiple test accounts...');
    
    for (const address of testAccounts) {
        console.log(`\n💰 Funding ${address}...`);
        
        // Temporarily change recipient for this iteration
        const originalRecipient = RECIPIENT_ADDRESS;
        global.RECIPIENT_ADDRESS = address;
        
        const result = await sendETH();
        
        if (result.success) {
            console.log(`✅ Successfully funded ${address}`);
        } else {
            console.log(`❌ Failed to fund ${address}: ${result.error}`);
        }
        
        // Restore original recipient
        global.RECIPIENT_ADDRESS = originalRecipient;
    }
}

// Export functions for use in other scripts
module.exports = {
    sendETH,
    sendETHMetaMask,
    fundTestAccounts
};

// Run if called directly
if (require.main === module) {
    console.log('💸 HIMAYA ETH TRANSFER SCRIPT');
    console.log('================================');
    console.log('');
    
    sendETH().then(result => {
        if (result.success) {
            console.log('');
            console.log('🎯 Next steps:');
            console.log('1. Import the wallet in MetaMask');
            console.log('2. Add Ganache network (http://localhost:8545, Chain ID: 1337)');
            console.log('3. Test the Himaya DApp with real balance');
            console.log('');
            console.log('🔗 Ganache Network Settings:');
            console.log('   RPC URL: http://localhost:8545');
            console.log('   Chain ID: 1337');
            console.log('   Currency Symbol: ETH');
        }
        process.exit(result.success ? 0 : 1);
    });
}
