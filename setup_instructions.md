# HIMAYA VEHICLE INSURANCE DAPP - SETUP AND DEMO GUIDE

## STEP 1: Install Prerequisites

### 1.1 Install Node.js
```bash
# Download and install Node.js 18+ from https://nodejs.org/
# Verify installation:
node --version
npm --version
```

### 1.2 Install Git
```bash
# Download from https://git-scm.com/
# Verify installation:
git --version
```

### 1.3 Install MetaMask Browser Extension
- Go to https://metamask.io/
- Add to Chrome/Firefox/Edge
- Create a new wallet or import existing one

## STEP 2: Project Setup

### 2.1 Create Project Directory
```bash
mkdir himaya-vehicle-insurance-dapp
cd himaya-vehicle-insurance-dapp
```

### 2.2 Initialize Project
```bash
npm init -y
npm install web3 express cors body-parser multer
npm install -g truffle ganache-cli
```

### 2.3 Install Truffle and Ganache
```bash
# Install Truffle globally
npm install -g truffle

# Install Ganache CLI for local blockchain
npm install -g ganache-cli
```

## STEP 3: Blockchain Setup

### 3.1 Start Local Blockchain
```bash
# Terminal 1: Start Ganache (local blockchain)
ganache-cli --deterministic --accounts 10 --host 0.0.0.0 --port 8545
```

### 3.2 Initialize Truffle Project
```bash
# Terminal 2: In project directory
truffle init
```

### 3.3 Configure Truffle
Create `truffle-config.js`:
```javascript
module.exports = {
  networks: {
    development: {
      host: "127.0.0.1",
      port: 8545,
      network_id: "*",
      gas: 8000000,
      gasPrice: ***********
    }
  },
  compilers: {
    solc: {
      version: "0.8.19"
    }
  }
};
```

## STEP 4: Smart Contracts

### 4.1 Create Smart Contracts
Create these files in `contracts/` directory:
- VehicleRegistry.sol
- InsurancePolicy.sol
- ClaimManager.sol

### 4.2 Create Migration Files
Create migration files in `migrations/` directory

### 4.3 Compile and Deploy
```bash
truffle compile
truffle migrate --reset
```

## STEP 5: Frontend Applications

### 5.1 Create Application Files
Create these HTML files in project root:
- main.html (Landing page)
- client-registration.html (New client registration)
- client-app.html (Verified client interface)
- insurer-app.html (Insurer dashboard)
- admin-app.html (Admin panel)
- verification-status.html (Status tracking)

### 5.2 Start Web Server
```bash
# Terminal 3: Start simple web server
python -m http.server 8080
# OR
npx http-server -p 8080
```

## STEP 6: MetaMask Configuration

### 6.1 Add Local Network
- Open MetaMask
- Networks → Add Network
- Network Name: Himaya Local
- RPC URL: http://localhost:8545
- Chain ID: 1337
- Currency Symbol: ETH

### 6.2 Import Test Accounts
- Copy private keys from Ganache output
- Import accounts into MetaMask

## STEP 7: Demo Scenario Preparation

### 7.1 Prepare Demo Data
- Create test user: Ahmed Ben Ali
- Prepare vehicle data: 2022 Toyota Camry
- Prepare documents: ID card images, selfie
- Prepare claim scenario: Minor accident

### 7.2 Demo Flow
1. Landing page navigation
2. Client registration process
3. Admin verification
4. Vehicle registration
5. Insurance policy purchase
6. Claim submission
7. Claim processing and payment

## STEP 8: Video Recording Setup

### 8.1 Screen Recording Tools
- OBS Studio (Free, professional)
- Camtasia (Paid, easy to use)
- Loom (Online, simple)
- Windows Game Bar (Built-in Windows)

### 8.2 Recording Preparation
- Close unnecessary applications
- Set browser to full screen
- Prepare script/talking points
- Test audio quality
- Have demo data ready
