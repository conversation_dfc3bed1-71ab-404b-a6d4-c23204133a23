<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Himaya - Espace Client</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #0f172a;
            min-height: 100vh;
            color: #e2e8f0;
        }

        .header {
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            border-bottom: 1px solid rgba(148, 163, 184, 0.2);
            color: white;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: bold;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .wallet-status {
            background: rgba(255, 255, 255, 0.2);
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .card {
            background: #1e293b;
            border: 1px solid rgba(148, 163, 184, 0.2);
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            transition: transform 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            border-color: rgba(139, 92, 246, 0.4);
        }

        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .card-icon {
            font-size: 2rem;
            margin-right: 1rem;
        }

        .card-title {
            font-size: 1.3rem;
            font-weight: bold;
            color: #f1f5f9;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
            color: white;
            border: 1px solid rgba(139, 92, 246, 0.3);
        }

        .btn-success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            border: 1px solid rgba(16, 185, 129, 0.3);
        }

        .btn-warning {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            color: white;
            border: 1px solid rgba(245, 158, 11, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: #1e293b;
            border: 1px solid rgba(148, 163, 184, 0.2);
            padding: 1.5rem;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #8b5cf6;
        }

        .stat-label {
            color: #94a3b8;
            margin-top: 0.5rem;
        }

        .vehicle-list {
            background: #1e293b;
            border: 1px solid rgba(148, 163, 184, 0.2);
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .vehicle-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            border-bottom: 1px solid #e2e8f0;
        }

        .vehicle-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .vehicle-icon {
            font-size: 2rem;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            background-color: #1e293b;
            border: 1px solid rgba(148, 163, 184, 0.2);
            margin: 5% auto;
            padding: 2rem;
            border-radius: 15px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
            color: #e2e8f0;
        }

        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: #000;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #f1f5f9;
        }

        input, select, textarea {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid rgba(148, 163, 184, 0.3);
            border-radius: 8px;
            font-size: 1rem;
            background: #0f172a;
            color: #e2e8f0;
        }

        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #8b5cf6;
            background: #1e293b;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        .plan-card {
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            padding: 1.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .plan-card:hover {
            border-color: #667eea;
            background: #f0f4ff;
            transform: translateY(-2px);
        }

        .plan-card.selected {
            border-color: #667eea;
            background: #f0f4ff;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.2);
        }

        .plan-card.recommended {
            border-color: #48bb78;
            background: #f0fff4;
        }

        .recommended-badge {
            position: absolute;
            top: -10px;
            right: 10px;
            background: #48bb78;
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .plan-price {
            font-size: 1.2rem;
            font-weight: bold;
            color: #667eea;
            margin: 0.5rem 0;
        }

        .plan-coverage {
            color: #718096;
            margin-bottom: 1rem;
            font-weight: 600;
        }

        .plan-features {
            list-style: none;
            padding: 0;
        }

        .plan-features li {
            padding: 0.25rem 0;
            font-size: 0.9rem;
        }

        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }

            .dashboard-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo">HIMAYA 🚗</div>
        <div class="user-info">
            <div class="wallet-status" id="walletStatus">
                🔗 Connecter MetaMask
            </div>
            <a href="main.html" class="btn btn-secondary" style="background: rgba(255,255,255,0.2);">Déconnexion</a>
        </div>
    </div>

    <div class="container">
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number" id="vehicleCount">0</div>
                <div class="stat-label">Véhicules Enregistrés</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="policyCount">0</div>
                <div class="stat-label">Polices Actives</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="claimCount">0</div>
                <div class="stat-label">Réclamations</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="walletBalance">0.00 ETH</div>
                <div class="stat-label">Solde (<span id="balanceMAD">0.00 MAD</span>)</div>
            </div>
        </div>

        <div class="dashboard-grid">
            <div class="card">
                <div class="card-header">
                    <div class="card-icon">🚗</div>
                    <div class="card-title">Mes Véhicules</div>
                </div>
                <p>Gérez vos véhicules enregistrés sur la blockchain</p>
                <div style="margin-top: 1rem;">
                    <button class="btn btn-primary" onclick="openModal('vehicleModal')">Enregistrer un Véhicule</button>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <div class="card-icon">🛡️</div>
                    <div class="card-title">Assurance</div>
                </div>
                <p>Souscrivez à une police d'assurance pour vos véhicules</p>
                <div style="margin-top: 1rem;">
                    <button class="btn btn-success" onclick="openModal('insuranceModal')">Souscrire une Police</button>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <div class="card-icon">📋</div>
                    <div class="card-title">Réclamations</div>
                </div>
                <p>Déposez et suivez vos réclamations d'assurance</p>
                <div style="margin-top: 1rem;">
                    <button class="btn btn-warning" onclick="openModal('claimModal')">Nouvelle Réclamation</button>
                </div>
            </div>
        </div>

        <div class="vehicle-list">
            <h3>📋 Mes Véhicules et Polices</h3>
            <div id="vehiclesList">
                <div style="text-align: center; padding: 2rem; color: #718096;">
                    <div style="font-size: 3rem; margin-bottom: 1rem;">🚗</div>
                    <p>Aucun véhicule enregistré</p>
                    <p style="font-size: 0.9rem;">Cliquez sur "Enregistrer un Véhicule" pour commencer</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Vehicle Registration Modal -->
    <div id="vehicleModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('vehicleModal')">&times;</span>
            <h2>🚗 Enregistrer un Véhicule</h2>
            <form id="vehicleForm">
                <div class="form-row">
                    <div class="form-group">
                        <label for="licensePlate">Plaque d'Immatriculation *</label>
                        <input type="text" id="licensePlate" placeholder="123456-A-07" required>
                    </div>
                    <div class="form-group">
                        <label for="make">Marque *</label>
                        <select id="make" required>
                            <option value="">Sélectionner...</option>
                            <option value="Toyota">Toyota</option>
                            <option value="Renault">Renault</option>
                            <option value="Peugeot">Peugeot</option>
                            <option value="Dacia">Dacia</option>
                            <option value="Mercedes">Mercedes</option>
                            <option value="BMW">BMW</option>
                        </select>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="model">Modèle *</label>
                        <input type="text" id="model" placeholder="Camry" required>
                    </div>
                    <div class="form-group">
                        <label for="year">Année *</label>
                        <input type="number" id="year" min="1990" max="2024" required>
                    </div>
                </div>
                <div class="form-group">
                    <label for="city">Ville *</label>
                    <select id="city" required>
                        <option value="">Sélectionner...</option>
                        <option value="Casablanca">Casablanca</option>
                        <option value="Rabat">Rabat</option>
                        <option value="Marrakech">Marrakech</option>
                        <option value="Fès">Fès</option>
                        <option value="Tanger">Tanger</option>
                    </select>
                </div>
                <div style="margin-top: 2rem;">
                    <button type="submit" class="btn btn-primary">Enregistrer le Véhicule</button>
                    <button type="button" class="btn btn-secondary" onclick="closeModal('vehicleModal')">Annuler</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Insurance Modal -->
    <div id="insuranceModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('insuranceModal')">&times;</span>
            <h2>🛡️ Souscrire une Police d'Assurance</h2>
            <div id="insurancePlans">
                <div style="display: grid; gap: 1rem; margin-bottom: 2rem;">
                    <div class="plan-card" data-plan="basic" onclick="selectPlan('basic')">
                        <h4>🥉 Plan Basique</h4>
                        <div class="plan-price">0.05 ETH/mois <small>(~1,350 MAD)</small></div>
                        <div class="plan-coverage">Couverture: 5 ETH (~135,000 MAD)</div>
                        <ul class="plan-features">
                            <li>✅ Accident de la route</li>
                            <li>✅ Vol du véhicule</li>
                            <li>✅ Assistance 24/7</li>
                            <li>✅ Dépannage d'urgence</li>
                            <li>❌ Dommages naturels</li>
                            <li>❌ Couverture internationale</li>
                        </ul>
                    </div>
                    <div class="plan-card recommended" data-plan="standard" onclick="selectPlan('standard')">
                        <div class="recommended-badge">⭐ RECOMMANDÉ</div>
                        <h4>🥈 Plan Standard</h4>
                        <div class="plan-price">0.08 ETH/mois <small>(~2,160 MAD)</small></div>
                        <div class="plan-coverage">Couverture: 10 ETH (~270,000 MAD)</div>
                        <ul class="plan-features">
                            <li>✅ Tout du plan Basique</li>
                            <li>✅ Dommages naturels (grêle, inondation)</li>
                            <li>✅ Vandalisme</li>
                            <li>✅ Bris de glace</li>
                            <li>✅ Véhicule de remplacement</li>
                            <li>❌ Couverture internationale</li>
                        </ul>
                    </div>
                    <div class="plan-card" data-plan="premium" onclick="selectPlan('premium')">
                        <h4>🥇 Plan Premium</h4>
                        <div class="plan-price">0.12 ETH/mois <small>(~3,240 MAD)</small></div>
                        <div class="plan-coverage">Couverture: 20 ETH (~540,000 MAD)</div>
                        <ul class="plan-features">
                            <li>✅ Tout du plan Standard</li>
                            <li>✅ Couverture internationale</li>
                            <li>✅ Assistance juridique</li>
                            <li>✅ Protection du conducteur</li>
                            <li>✅ Équipements personnels</li>
                            <li>✅ Service premium 24/7</li>
                        </ul>
                    </div>
                </div>
                <div style="text-align: center; margin: 1rem 0; padding: 1rem; background: #f0f4ff; border-radius: 8px;">
                    <p><strong>Véhicule sélectionné:</strong> <span id="selectedVehicleForInsurance">Aucun véhicule sélectionné</span></p>
                </div>
                <button class="btn btn-success" onclick="purchaseInsurance()">Souscrire au Plan Sélectionné</button>
                <button class="btn btn-secondary" onclick="closeModal('insuranceModal')">Annuler</button>
            </div>
        </div>
    </div>

    <!-- Claim Modal -->
    <div id="claimModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('claimModal')">&times;</span>
            <h2>📋 Nouvelle Réclamation</h2>
            <form id="claimForm">
                <div class="form-group">
                    <label for="claimType">Type de Réclamation *</label>
                    <select id="claimType" required>
                        <option value="">Sélectionner...</option>
                        <option value="accident">Accident</option>
                        <option value="theft">Vol</option>
                        <option value="damage">Dommages</option>
                        <option value="vandalism">Vandalisme</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="claimDescription">Description *</label>
                    <textarea id="claimDescription" rows="4" placeholder="Décrivez l'incident en détail..." required></textarea>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="claimAmount">Montant Réclamé (ETH) *</label>
                        <input type="number" id="claimAmount" step="0.01" min="0.1" max="20" required>
                    </div>
                    <div class="form-group">
                        <label for="incidentDate">Date de l'Incident *</label>
                        <input type="date" id="incidentDate" required>
                    </div>
                </div>
                <div class="form-group">
                    <label for="location">Lieu de l'Incident *</label>
                    <input type="text" id="location" placeholder="Avenue Mohammed V, Casablanca" required>
                </div>
                <div style="margin-top: 2rem;">
                    <button type="submit" class="btn btn-warning">Soumettre la Réclamation</button>
                    <button type="button" class="btn btn-secondary" onclick="closeModal('claimModal')">Annuler</button>
                </div>
            </form>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/web3@latest/dist/web3.min.js"></script>
    <script src="web3-integration.js"></script>
    <script>
        let web3;
        let account;
        let selectedPlan = 'standard';

        // ETH to MAD conversion rate (approximate)
        const ETH_TO_MAD = 27000; // 1 ETH ≈ 27,000 MAD

        // Initialize Web3 and connect to MetaMask
        async function initWeb3() {
            try {
                // Initialize blockchain connection
                const connected = await window.himayaBlockchain.init();

                if (connected) {
                    account = window.himayaBlockchain.account;
                    document.getElementById('walletStatus').innerHTML = `🔗 ${account.substring(0, 6)}...${account.substring(38)}`;

                    // Get real balance
                    const ethBalance = await window.himayaBlockchain.getBalance();
                    const madBalance = parseFloat(ethBalance) * ETH_TO_MAD;

                    document.getElementById('walletBalance').textContent = parseFloat(ethBalance).toFixed(4) + ' ETH';
                    document.getElementById('balanceMAD').textContent = madBalance.toLocaleString('fr-FR', {minimumFractionDigits: 2}) + ' MAD';

                } else {
                    // Demo mode with simulated balance for testing
                    document.getElementById('walletStatus').innerHTML = '🔗 Demo Mode (0.5 ETH)';
                    document.getElementById('walletBalance').textContent = '0.5000 ETH';
                    document.getElementById('balanceMAD').textContent = (0.5 * ETH_TO_MAD).toLocaleString('fr-FR', {minimumFractionDigits: 2}) + ' MAD';

                    // Store demo balance for validation
                    window.demoBalance = 0.5;
                }

            } catch (error) {
                console.error('Error initializing Web3:', error);
                document.getElementById('walletStatus').innerHTML = '❌ Erreur de connexion';
                document.getElementById('walletBalance').textContent = '0.0000 ETH';
                document.getElementById('balanceMAD').textContent = '0.00 MAD';
            }
        }

        // Modal functions
        function openModal(modalId) {
            document.getElementById(modalId).style.display = 'block';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        // Vehicle registration
        document.getElementById('vehicleForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const vehicleData = {
                licensePlate: document.getElementById('licensePlate').value,
                make: document.getElementById('make').value,
                model: document.getElementById('model').value,
                year: parseInt(document.getElementById('year').value),
                city: document.getElementById('city').value
            };

            try {
                // Register vehicle on blockchain
                const result = await window.himayaBlockchain.registerVehicle(vehicleData);

                if (result.success) {
                    // Store additional UI data
                    const newVehicle = {
                        id: result.vehicleId,
                        ...vehicleData,
                        registrationDate: new Date().toISOString(),
                        hasInsurance: false,
                        txHash: result.txHash
                    };

                    let vehicles = JSON.parse(localStorage.getItem('userVehicles') || '[]');
                    vehicles.push(newVehicle);
                    localStorage.setItem('userVehicles', JSON.stringify(vehicles));

                    alert(`🚗 Véhicule enregistré avec succès sur la blockchain!\nID du véhicule: ${result.vehicleId}\nTransaction: ${result.txHash}`);

                    // Update UI
                    updateVehiclesList();
                    updateStats();

                    closeModal('vehicleModal');
                    this.reset();
                } else {
                    throw new Error('Échec de l\'enregistrement du véhicule');
                }

            } catch (error) {
                console.error('Vehicle registration error:', error);
                alert('❌ Erreur lors de l\'enregistrement du véhicule: ' + error.message);
            }
        });

        // Insurance plan selection
        function selectPlan(plan) {
            selectedPlan = plan;
            document.querySelectorAll('.plan-card').forEach(card => {
                card.classList.remove('selected');
            });

            document.querySelector(`[data-plan="${plan}"]`).classList.add('selected');
        }

        // Open insurance modal
        function openInsuranceModal() {
            const vehicles = JSON.parse(localStorage.getItem('userVehicles') || '[]');
            if (vehicles.length === 0) {
                alert('⚠️ Vous devez d\'abord enregistrer un véhicule avant de souscrire une assurance.');
                return;
            }

            // Show first vehicle for demo
            const vehicle = vehicles[0];
            document.getElementById('selectedVehicleForInsurance').textContent =
                `${vehicle.make} ${vehicle.model} ${vehicle.year} (${vehicle.licensePlate})`;

            openModal('insuranceModal');
        }

        // Purchase insurance
        async function purchaseInsurance() {
            const vehicles = JSON.parse(localStorage.getItem('userVehicles') || '[]');
            if (vehicles.length === 0) {
                alert('⚠️ Aucun véhicule disponible pour l\'assurance.');
                return;
            }

            const plans = {
                basic: { price: '0.05', coverage: '5', priceMAD: '1,350' },
                standard: { price: '0.08', coverage: '10', priceMAD: '2,160' },
                premium: { price: '0.12', coverage: '20', priceMAD: '3,240' }
            };

            const plan = plans[selectedPlan];

            // Check wallet balance (use demo balance if available)
            let currentBalance;
            if (window.himayaBlockchain.isConnected) {
                currentBalance = parseFloat(await window.himayaBlockchain.getBalance());
            } else {
                currentBalance = window.demoBalance || 0;
            }

            const requiredAmount = parseFloat(plan.price);

            if (currentBalance < requiredAmount) {
                alert(`❌ Solde insuffisant!\nSolde actuel: ${currentBalance.toFixed(4)} ETH\nMontant requis: ${plan.price} ETH\n\nVeuillez recharger votre portefeuille.`);
                return;
            }

            if (confirm(`Confirmer l'achat du plan ${selectedPlan.toUpperCase()}?\nPrix: ${plan.price} ETH/mois (~${plan.priceMAD} MAD)\nCouverture: ${plan.coverage} ETH`)) {
                try {
                    const policyData = {
                        vehicleId: vehicles[0].id,
                        plan: selectedPlan,
                        premium: plan.price,
                        coverage: plan.coverage,
                        duration: 30 // days
                    };

                    // Purchase insurance on blockchain
                    const result = await window.himayaBlockchain.purchaseInsurance(policyData);

                    if (result.success) {
                        // Update demo balance if in demo mode
                        if (!window.himayaBlockchain.isConnected) {
                            window.demoBalance -= requiredAmount;
                            const madBalance = window.demoBalance * ETH_TO_MAD;
                            document.getElementById('walletBalance').textContent = window.demoBalance.toFixed(4) + ' ETH';
                            document.getElementById('balanceMAD').textContent = madBalance.toLocaleString('fr-FR', {minimumFractionDigits: 2}) + ' MAD';
                        }

                        // Store UI data
                        let policies = JSON.parse(localStorage.getItem('userPolicies') || '[]');
                        const policyId = 'POL-' + selectedPlan.toUpperCase() + '-' + Date.now().toString().slice(-6);
                        const newPolicy = {
                            id: policyId,
                            vehicleId: vehicles[0].id,
                            plan: selectedPlan,
                            price: plan.price,
                            coverage: plan.coverage,
                            startDate: new Date().toISOString(),
                            endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
                            active: true,
                            txHash: result.txHash
                        };
                        policies.push(newPolicy);
                        localStorage.setItem('userPolicies', JSON.stringify(policies));

                        // Update vehicle insurance status
                        vehicles[0].hasInsurance = true;
                        vehicles[0].policyId = policyId;
                        localStorage.setItem('userVehicles', JSON.stringify(vehicles));

                        alert(`🛡️ Police d'assurance souscrite avec succès!\nNuméro de police: ${policyId}\nTransaction: ${result.txHash}`);

                        // Update UI
                        updateVehiclesList();
                        updateStats();

                        closeModal('insuranceModal');
                    } else {
                        throw new Error('Échec de la souscription');
                    }

                } catch (error) {
                    console.error('Insurance purchase error:', error);
                    alert('❌ Erreur lors de la souscription: ' + error.message);
                }
            }
        }

        // Claim submission
        document.getElementById('claimForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const vehicles = JSON.parse(localStorage.getItem('userVehicles') || '[]');
            const registrationData = JSON.parse(localStorage.getItem('registrationData') || '{}');

            if (vehicles.length === 0) {
                alert('⚠️ Aucun véhicule disponible pour la réclamation.');
                return;
            }

            const claimData = {
                id: 'CLM-' + Date.now().toString().slice(-8),
                type: document.getElementById('claimType').value,
                description: document.getElementById('claimDescription').value,
                amount: document.getElementById('claimAmount').value,
                incidentDate: document.getElementById('incidentDate').value,
                location: document.getElementById('location').value,
                submissionDate: new Date().toISOString(),
                clientName: `${registrationData.firstName || 'Client'} ${registrationData.lastName || 'Inconnu'}`,
                vehicleInfo: `${vehicles[0].make} ${vehicles[0].model} ${vehicles[0].year} (${vehicles[0].licensePlate})`,
                status: 'pending'
            };

            // Store claim data
            let claims = JSON.parse(localStorage.getItem('userClaims') || '[]');
            claims.push(claimData);
            localStorage.setItem('userClaims', JSON.stringify(claims));

            // Simulate blockchain transaction
            alert('📋 Réclamation soumise avec succès!\nID de réclamation: ' + claimData.id + '\nMontant: ' + claimData.amount + ' ETH');

            // Update UI
            updateStats();

            closeModal('claimModal');
            this.reset();
        });

        // Close modal when clicking outside
        window.onclick = function(event) {
            if (event.target.classList.contains('modal')) {
                event.target.style.display = 'none';
            }
        }

        // Update vehicles list
        function updateVehiclesList() {
            const vehicles = JSON.parse(localStorage.getItem('userVehicles') || '[]');
            const vehiclesList = document.getElementById('vehiclesList');

            if (vehicles.length === 0) {
                vehiclesList.innerHTML = `
                    <div style="text-align: center; padding: 2rem; color: #718096;">
                        <div style="font-size: 3rem; margin-bottom: 1rem;">🚗</div>
                        <p>Aucun véhicule enregistré</p>
                        <p style="font-size: 0.9rem;">Cliquez sur "Enregistrer un Véhicule" pour commencer</p>
                    </div>
                `;
            } else {
                vehiclesList.innerHTML = vehicles.map(vehicle => `
                    <div class="vehicle-item">
                        <div class="vehicle-info">
                            <div class="vehicle-icon">🚗</div>
                            <div>
                                <strong>${vehicle.make} ${vehicle.model} ${vehicle.year}</strong><br>
                                <small>Plaque: ${vehicle.licensePlate} ${vehicle.hasInsurance ? '| Police: ' + vehicle.policyId : '| Non assuré'}</small>
                            </div>
                        </div>
                        <div>
                            <span class="btn ${vehicle.hasInsurance ? 'btn-success' : 'btn-warning'}" style="font-size: 0.8rem; padding: 0.3rem 0.8rem;">
                                ${vehicle.hasInsurance ? 'Assuré' : 'Non assuré'}
                            </span>
                        </div>
                    </div>
                `).join('');
            }
        }

        // Update statistics
        function updateStats() {
            const vehicles = JSON.parse(localStorage.getItem('userVehicles') || '[]');
            const policies = JSON.parse(localStorage.getItem('userPolicies') || '[]');
            const claims = JSON.parse(localStorage.getItem('userClaims') || '[]');

            document.getElementById('vehicleCount').textContent = vehicles.length;
            document.getElementById('policyCount').textContent = policies.filter(p => p.active).length;
            document.getElementById('claimCount').textContent = claims.length;
        }

        // Update insurance modal button
        document.querySelector('button[onclick="openModal(\'insuranceModal\')"]').setAttribute('onclick', 'openInsuranceModal()');

        // Initialize on page load
        window.addEventListener('load', function() {
            initWeb3();
            updateVehiclesList();
            updateStats();
        });
    </script>
</body>
</html>
