const express = require('express');
const { body, validationResult } = require('express-validator');
const Claim = require('../models/Claim');
const Policy = require('../models/Policy');
const { auth, authorize } = require('../middleware/auth');
const { contracts, accounts, sendTransaction } = require('../config/blockchain');

const router = express.Router();

// Get user's claims
router.get('/my-claims', auth, async (req, res) => {
  try {
    const claims = await Claim.find({
      claimant: req.user.walletAddress,
    }).sort({ createdAt: -1 });

    res.json({ claims });
  } catch (error) {
    console.error('Get user claims error:', error);
    res.status(500).json({ error: 'Failed to fetch your claims' });
  }
});

// Get claims statistics (admin/agent only) - must be before /:id route
router.get('/stats/overview', auth, authorize('agent', 'admin'), async (req, res) => {
  try {
    const totalClaims = await Claim.countDocuments();
    const pendingClaims = await Claim.countDocuments({
      status: { $in: ['Submitted', 'UnderReview', 'RequiresDocuments'] }
    });
    const approvedClaims = await Claim.countDocuments({ status: 'Approved' });
    const rejectedClaims = await Claim.countDocuments({ status: 'Rejected' });
    const paidClaims = await Claim.countDocuments({ status: 'Paid' });

    // Calculate total amounts
    const totalClaimedAmount = await Claim.aggregate([
      { $group: { _id: null, total: { $sum: '$claimedAmount' } } }
    ]);

    const totalApprovedAmount = await Claim.aggregate([
      { $match: { status: { $in: ['Approved', 'Paid'] } } },
      { $group: { _id: null, total: { $sum: '$approvedAmount' } } }
    ]);

    // Claims by type
    const claimsByType = await Claim.aggregate([
      { $group: { _id: '$claimType', count: { $sum: 1 } } }
    ]);

    res.json({
      overview: {
        totalClaims,
        pendingClaims,
        approvedClaims,
        rejectedClaims,
        paidClaims,
        totalClaimedAmount: totalClaimedAmount[0]?.total || 0,
        totalApprovedAmount: totalApprovedAmount[0]?.total || 0
      },
      claimsByType
    });

  } catch (error) {
    console.error('Claims stats error:', error);
    res.status(500).json({ error: 'Failed to fetch claims statistics' });
  }
});

// Get all claims (agent/admin only)
router.get('/', auth, authorize('agent', 'admin'), async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const query = {};
    if (req.query.status) {
      query.status = req.query.status;
    }
    if (req.query.claimType) {
      query.claimType = req.query.claimType;
    }

    const claims = await Claim.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const total = await Claim.countDocuments(query);

    res.json({
      claims,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Get claims error:', error);
    res.status(500).json({ error: 'Failed to fetch claims' });
  }
});

// Get claim by ID
router.get('/:id', auth, async (req, res) => {
  try {
    const claim = await Claim.findOne({
      blockchainId: req.params.id
    });

    if (!claim) {
      return res.status(404).json({ error: 'Claim not found' });
    }

    // Check access permissions
    const hasAccess = claim.claimant === req.user.walletAddress ||
                     claim.reviewer === req.user.walletAddress ||
                     req.user.role === 'admin' ||
                     req.user.role === 'agent';

    if (!hasAccess) {
      return res.status(403).json({ error: 'Access denied' });
    }

    res.json({ claim });
  } catch (error) {
    console.error('Get claim error:', error);
    res.status(500).json({ error: 'Failed to fetch claim' });
  }
});

// Submit new claim
router.post('/submit', auth, [
  body('policyId').isInt({ min: 1 }).withMessage('Valid policy ID required'),
  body('claimType').isIn(['Collision', 'Theft', 'Vandalism', 'NaturalDisaster', 'Fire', 'Other']).withMessage('Invalid claim type'),
  body('description').trim().isLength({ min: 10 }).withMessage('Description must be at least 10 characters'),
  body('claimedAmount').isFloat({ min: 0 }).withMessage('Claimed amount must be positive'),
  body('incidentDate').isISO8601().withMessage('Valid incident date required'),
  body('location').trim().isLength({ min: 1 }).withMessage('Location is required'),
  body('documentHashes').optional().isArray()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const {
      policyId,
      claimType,
      description,
      claimedAmount,
      incidentDate,
      location,
      documentHashes = [],
      policeReportNumber,
      witnesses,
      thirdPartyInfo,
      medicalInfo
    } = req.body;

    // Verify policy exists and is active
    const policy = await Policy.findOne({ blockchainId: policyId });
    if (!policy) {
      return res.status(404).json({ error: 'Policy not found' });
    }

    if (policy.policyholder !== req.user.walletAddress) {
      return res.status(403).json({ error: 'Only policyholder can submit claims' });
    }

    if (!policy.isActive) {
      return res.status(400).json({ error: 'Policy is not active' });
    }

    // Check if policy covers this claim type
    if (!policy.coversClaim(claimType)) {
      return res.status(400).json({ 
        error: `Policy does not cover ${claimType} claims. Coverage type: ${policy.coverageType}` 
      });
    }

    // Check claimed amount doesn't exceed coverage
    if (claimedAmount > policy.coverageAmount) {
      return res.status(400).json({ error: 'Claimed amount exceeds policy coverage' });
    }

    // Submit claim on blockchain
    const claimManager = contracts.claimManager;
    const claimTypeIndex = ['Collision', 'Theft', 'Vandalism', 'NaturalDisaster', 'Fire', 'Other'].indexOf(claimType);
    
    const transaction = claimManager.methods.submitClaim(
      policyId,
      claimTypeIndex,
      description,
      Math.floor(claimedAmount * 100), // Convert to cents
      Math.floor(new Date(incidentDate).getTime() / 1000), // Convert to timestamp
      location,
      documentHashes
    );

    const result = await sendTransaction(transaction, {
      address: req.user.walletAddress
    });

    // Get the claim ID from the event
    const claimSubmittedEvent = result.events.ClaimSubmitted;
    const blockchainId = parseInt(claimSubmittedEvent.returnValues.claimId);

    // Generate claim number
    const claimNumber = `CLM-${Date.now()}-${blockchainId}`;

    // Create claim record
    const claim = new Claim({
      blockchainId,
      claimNumber,
      policyId,
      claimant: req.user.walletAddress,
      claimType,
      description,
      claimedAmount,
      incidentDate: new Date(incidentDate),
      submissionDate: new Date(),
      location,
      policeReportNumber,
      witnesses,
      thirdPartyInfo,
      medicalInfo,
      documents: documentHashes.map(hash => ({
        type: hash,
        description: 'Initial submission document',
        category: 'other'
      }))
    });

    // Calculate fraud score
    claim.calculateFraudScore();

    // Add initial timeline entry
    claim.addTimelineEntry(
      'Claim Submitted',
      'Claim submitted by policyholder',
      req.user.walletAddress
    );

    await claim.save();

    res.status(201).json({
      message: 'Claim submitted successfully',
      claim,
      transactionHash: result.transactionHash
    });

  } catch (error) {
    console.error('Claim submission error:', error);
    res.status(500).json({ error: 'Failed to submit claim' });
  }
});

// Update claim status (agent/admin only)
router.put('/:id/status', auth, authorize('agent', 'admin'), [
  body('status').isIn(['Submitted', 'UnderReview', 'RequiresDocuments', 'Approved', 'Rejected', 'Paid']).withMessage('Invalid status'),
  body('reviewNotes').optional().trim()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { status, reviewNotes = '' } = req.body;

    const claim = await Claim.findOne({
      blockchainId: req.params.id
    });

    if (!claim) {
      return res.status(404).json({ error: 'Claim not found' });
    }

    // Update status on blockchain
    const claimManager = contracts.claimManager;
    const statusIndex = ['Submitted', 'UnderReview', 'RequiresDocuments', 'Approved', 'Rejected', 'Paid'].indexOf(status);
    
    const transaction = claimManager.methods.updateClaimStatus(
      parseInt(req.params.id),
      statusIndex,
      reviewNotes
    );

    const result = await sendTransaction(transaction, {
      address: req.user.walletAddress
    });

    // Update database
    const previousStatus = claim.status;
    claim.status = status;
    claim.reviewer = req.user.walletAddress;
    claim.reviewNotes = reviewNotes;
    claim.reviewDate = new Date();

    // Add timeline entry
    claim.addTimelineEntry(
      `Status Changed to ${status}`,
      reviewNotes || `Status updated from ${previousStatus} to ${status}`,
      req.user.walletAddress
    );

    await claim.save();

    res.json({
      message: 'Claim status updated successfully',
      claim,
      transactionHash: result.transactionHash
    });

  } catch (error) {
    console.error('Claim status update error:', error);
    res.status(500).json({ error: 'Failed to update claim status' });
  }
});

// Approve claim (agent/admin only)
router.post('/:id/approve', auth, authorize('agent', 'admin'), [
  body('approvedAmount').isFloat({ min: 0 }).withMessage('Approved amount must be positive'),
  body('reviewNotes').optional().trim()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { approvedAmount, reviewNotes = '' } = req.body;

    const claim = await Claim.findOne({
      blockchainId: req.params.id
    });

    if (!claim) {
      return res.status(404).json({ error: 'Claim not found' });
    }

    if (claim.status !== 'UnderReview') {
      return res.status(400).json({ error: 'Claim must be under review to approve' });
    }

    if (approvedAmount > claim.claimedAmount) {
      return res.status(400).json({ error: 'Approved amount cannot exceed claimed amount' });
    }

    // Get policy to check deductible
    const policy = await Policy.findOne({ blockchainId: claim.policyId });
    if (approvedAmount < policy.deductible) {
      return res.status(400).json({ error: 'Approved amount must be at least the deductible amount' });
    }

    // Approve claim on blockchain
    const claimManager = contracts.claimManager;
    const transaction = claimManager.methods.approveClaim(
      parseInt(req.params.id),
      Math.floor(approvedAmount * 100), // Convert to cents
      reviewNotes
    );

    const result = await sendTransaction(transaction, {
      address: req.user.walletAddress
    });

    // Update database
    claim.status = 'Approved';
    claim.approvedAmount = approvedAmount;
    claim.reviewer = req.user.walletAddress;
    claim.reviewNotes = reviewNotes;
    claim.reviewDate = new Date();

    // Add timeline entry
    claim.addTimelineEntry(
      'Claim Approved',
      `Approved for $${approvedAmount}. ${reviewNotes}`,
      req.user.walletAddress
    );

    await claim.save();

    res.json({
      message: 'Claim approved successfully',
      claim,
      transactionHash: result.transactionHash
    });

  } catch (error) {
    console.error('Claim approval error:', error);
    res.status(500).json({ error: 'Failed to approve claim' });
  }
});

// Reject claim (agent/admin only)
router.post('/:id/reject', auth, authorize('agent', 'admin'), [
  body('reason').trim().isLength({ min: 10 }).withMessage('Rejection reason must be at least 10 characters')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { reason } = req.body;

    const claim = await Claim.findOne({
      blockchainId: req.params.id
    });

    if (!claim) {
      return res.status(404).json({ error: 'Claim not found' });
    }

    if (claim.status !== 'UnderReview') {
      return res.status(400).json({ error: 'Claim must be under review to reject' });
    }

    // Reject claim on blockchain
    const claimManager = contracts.claimManager;
    const transaction = claimManager.methods.rejectClaim(
      parseInt(req.params.id),
      reason
    );

    const result = await sendTransaction(transaction, {
      address: req.user.walletAddress
    });

    // Update database
    claim.status = 'Rejected';
    claim.reviewer = req.user.walletAddress;
    claim.reviewNotes = reason;
    claim.reviewDate = new Date();

    // Add timeline entry
    claim.addTimelineEntry(
      'Claim Rejected',
      reason,
      req.user.walletAddress
    );

    await claim.save();

    res.json({
      message: 'Claim rejected successfully',
      claim,
      transactionHash: result.transactionHash
    });

  } catch (error) {
    console.error('Claim rejection error:', error);
    res.status(500).json({ error: 'Failed to reject claim' });
  }
});

// Add documents to claim
router.post('/:id/documents', auth, [
  body('documents').isArray().withMessage('Documents must be an array'),
  body('documents.*.type').trim().isLength({ min: 1 }).withMessage('Document type required'),
  body('documents.*.description').trim().isLength({ min: 1 }).withMessage('Document description required'),
  body('documents.*.category').isIn(['photos', 'police_report', 'medical', 'repair_estimate', 'other']).withMessage('Invalid document category')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { documents } = req.body;

    const claim = await Claim.findOne({
      blockchainId: req.params.id
    });

    if (!claim) {
      return res.status(404).json({ error: 'Claim not found' });
    }

    // Check if user can add documents
    const canAddDocuments = claim.claimant === req.user.walletAddress ||
                           claim.reviewer === req.user.walletAddress ||
                           req.user.role === 'admin' ||
                           req.user.role === 'agent';

    if (!canAddDocuments) {
      return res.status(403).json({ error: 'Access denied' });
    }

    // Add documents to blockchain (for each document hash)
    const claimManager = contracts.claimManager;
    
    for (const doc of documents) {
      const transaction = claimManager.methods.addDocument(
        parseInt(req.params.id),
        doc.type // This should be the IPFS hash or file identifier
      );

      await sendTransaction(transaction, {
        address: req.user.walletAddress
      });
    }

    // Update database
    claim.documents.push(...documents);
    
    // Add timeline entry
    claim.addTimelineEntry(
      'Documents Added',
      `Added ${documents.length} document(s)`,
      req.user.walletAddress
    );

    await claim.save();

    res.json({
      message: 'Documents added successfully',
      claim
    });

  } catch (error) {
    console.error('Add documents error:', error);
    res.status(500).json({ error: 'Failed to add documents' });
  }
});

module.exports = router;
