const mongoose = require('mongoose');

const policySchema = new mongoose.Schema({
  blockchainId: {
    type: Number,
    required: true,
    unique: true
  },
  policyNumber: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  vehicleId: {
    type: Number,
    required: true
  },
  policyholder: {
    type: String,
    required: true,
    lowercase: true
  },
  insurer: {
    type: String,
    required: true,
    lowercase: true
  },
  coverageType: {
    type: String,
    enum: ['Liability', 'Comprehensive', 'Collision', 'Full'],
    required: true
  },
  premiumAmount: {
    type: Number,
    required: true,
    min: 0
  },
  coverageAmount: {
    type: Number,
    required: true,
    min: 0
  },
  deductible: {
    type: Number,
    required: true,
    min: 0
  },
  startDate: {
    type: Date,
    required: true
  },
  endDate: {
    type: Date,
    required: true
  },
  status: {
    type: String,
    enum: ['Active', 'Expired', 'Cancelled', 'Suspended'],
    default: 'Active'
  },
  // Additional off-chain data
  paymentFrequency: {
    type: String,
    enum: ['monthly', 'quarterly', 'semi-annual', 'annual'],
    default: 'monthly'
  },
  paymentMethod: {
    type: String,
    enum: ['credit_card', 'bank_transfer', 'crypto'],
    default: 'credit_card'
  },
  beneficiaries: [{
    name: String,
    relationship: String,
    percentage: {
      type: Number,
      min: 0,
      max: 100
    }
  }],
  coverageDetails: {
    bodilyInjuryLiability: Number,
    propertyDamageLiability: Number,
    medicalPayments: Number,
    uninsuredMotorist: Number,
    personalInjuryProtection: Number,
    comprehensiveDeductible: Number,
    collisionDeductible: Number
  },
  discounts: [{
    type: String,
    description: String,
    amount: Number,
    percentage: Number
  }],
  documents: [{
    type: String,
    description: String,
    uploadDate: {
      type: Date,
      default: Date.now
    }
  }],
  renewalDate: {
    type: Date
  },
  isAutoRenewal: {
    type: Boolean,
    default: false
  },
  notes: {
    type: String
  }
}, {
  timestamps: true
});

// Indexes
policySchema.index({ blockchainId: 1 });
policySchema.index({ policyNumber: 1 });
policySchema.index({ policyholder: 1 });
policySchema.index({ insurer: 1 });
policySchema.index({ vehicleId: 1 });
policySchema.index({ status: 1 });
policySchema.index({ startDate: 1, endDate: 1 });

// Virtual for policy duration
policySchema.virtual('duration').get(function() {
  const diffTime = Math.abs(this.endDate - this.startDate);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays;
});

// Virtual for days remaining
policySchema.virtual('daysRemaining').get(function() {
  const now = new Date();
  if (now > this.endDate) return 0;
  const diffTime = this.endDate - now;
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays;
});

// Virtual for is active
policySchema.virtual('isActive').get(function() {
  const now = new Date();
  return this.status === 'Active' && 
         now >= this.startDate && 
         now <= this.endDate;
});

// Method to check if policy covers specific claim type
policySchema.methods.coversClaim = function(claimType) {
  const coverage = this.coverageType;
  
  switch (claimType) {
    case 'Collision':
      return coverage === 'Collision' || coverage === 'Full';
    case 'Theft':
    case 'Vandalism':
    case 'NaturalDisaster':
    case 'Fire':
      return coverage === 'Comprehensive' || coverage === 'Full';
    default:
      return coverage === 'Liability' || coverage === 'Full';
  }
};

module.exports = mongoose.model('Policy', policySchema);
