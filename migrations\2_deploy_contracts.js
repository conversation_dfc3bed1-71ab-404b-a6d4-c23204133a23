const VehicleRegistry = artifacts.require("VehicleRegistry");
const InsurancePolicy = artifacts.require("InsurancePolicy");
const ClaimManager = artifacts.require("ClaimManager");

module.exports = async function(deployer, network, accounts) {
  // Deploy VehicleRegistry first
  await deployer.deploy(VehicleRegistry);
  const vehicleRegistry = await VehicleRegistry.deployed();
  
  // Deploy InsurancePolicy with VehicleRegistry address and insurer address
  const insurerAddress = accounts[1]; // Second account as insurer
  await deployer.deploy(InsurancePolicy, vehicleRegistry.address, insurerAddress);
  const insurancePolicy = await InsurancePolicy.deployed();
  
  // Deploy ClaimManager with InsurancePolicy address and insurer address
  await deployer.deploy(ClaimManager, insurancePolicy.address, insurerAddress);
  const claimManager = await ClaimManager.deployed();
  
  console.log("=== DEPLOYMENT SUMMARY ===");
  console.log("VehicleRegistry deployed at:", vehicleRegistry.address);
  console.log("InsurancePolicy deployed at:", insurancePolicy.address);
  console.log("ClaimManager deployed at:", claimManager.address);
  console.log("Insurer Address:", insurerAddress);
  console.log("========================");
};
