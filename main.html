<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Himaya - Assurance Véhicule Blockchain</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #e2e8f0;
        }

        .container {
            background: rgba(30, 41, 59, 0.95);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(148, 163, 184, 0.2);
            padding: 3rem;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            text-align: center;
            max-width: 500px;
            width: 90%;
        }

        .logo {
            font-size: 3rem;
            font-weight: bold;
            color: #f1f5f9;
            margin-bottom: 1rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .arabic-name {
            font-size: 1.5rem;
            color: #94a3b8;
            margin-bottom: 2rem;
            font-weight: 600;
        }

        .subtitle {
            font-size: 1.2rem;
            color: #cbd5e1;
            margin-bottom: 3rem;
            line-height: 1.6;
        }

        .button-group {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .btn {
            padding: 1rem 2rem;
            border: none;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
            color: white;
            border: 1px solid rgba(139, 92, 246, 0.3);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
            color: white;
            border: 1px solid rgba(6, 182, 212, 0.3);
        }

        .btn-tertiary {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            border: 1px solid rgba(16, 185, 129, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        .features {
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid #e2e8f0;
        }

        .feature {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1rem;
            color: #94a3b8;
        }

        .feature-icon {
            margin-right: 0.5rem;
            font-size: 1.2rem;
        }

        @media (max-width: 600px) {
            .container {
                padding: 2rem;
            }
            
            .logo {
                font-size: 2.5rem;
            }
            
            .subtitle {
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">HIMAYA</div>
        <div class="arabic-name">حماية</div>
        <div class="subtitle">
            Plateforme d'Assurance Véhicule sur Blockchain<br>
            <small>Transparence • Sécurité • Rapidité</small>
        </div>
        
        <div class="button-group">
            <a href="client-registration.html" class="btn btn-primary">
                🚗 Nouveau Client
            </a>
            
            <a href="verification-status.html" class="btn btn-secondary">
                📋 Vérifier le Statut
            </a>
            
            <a href="client-app.html" class="btn btn-tertiary">
                👤 Espace Client Vérifié
            </a>
            
            <a href="insurer-app.html" class="btn btn-primary">
                🏢 Espace Assureur
            </a>
            
            <a href="admin-app.html" class="btn btn-secondary">
                ⚙️ Administration
            </a>
        </div>

        <div class="features">
            <div class="feature">
                <span class="feature-icon">⛓️</span>
                <span>Blockchain Ethereum Privé</span>
            </div>
            <div class="feature">
                <span class="feature-icon">🔒</span>
                <span>Sécurité Cryptographique</span>
            </div>
            <div class="feature">
                <span class="feature-icon">⚡</span>
                <span>Traitement Instantané</span>
            </div>
            <div class="feature">
                <span class="feature-icon">🇲🇦</span>
                <span>Conçu pour le Maroc</span>
            </div>
        </div>
    </div>

    <script>
        // Add some interactive effects
        document.querySelectorAll('.btn').forEach(btn => {
            btn.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-3px) scale(1.02)';
            });
            
            btn.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });

        // Check if MetaMask is installed
        window.addEventListener('load', function() {
            if (typeof window.ethereum === 'undefined') {
                const warning = document.createElement('div');
                warning.style.cssText = `
                    position: fixed;
                    top: 20px;
                    left: 50%;
                    transform: translateX(-50%);
                    background: #fed7d7;
                    color: #c53030;
                    padding: 1rem 2rem;
                    border-radius: 10px;
                    border: 1px solid #feb2b2;
                    z-index: 1000;
                `;
                warning.innerHTML = '⚠️ MetaMask requis pour utiliser cette application';
                document.body.appendChild(warning);
                
                setTimeout(() => {
                    warning.remove();
                }, 5000);
            }
        });
    </script>
</body>
</html>
