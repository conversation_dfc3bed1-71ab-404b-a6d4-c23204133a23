# HIMAYA VEHICLE INSURANCE DAPP - DEMO SCRIPT

## 🎬 VIDEO DEMO SCRIPT (5-7 minutes)

### **PREPARATION CHECKLIST:**
- [ ] Ganache running on localhost:8545
- [ ] Smart contracts deployed
- [ ] Web server running on localhost:8080
- [ ] MetaMask configured with local network
- [ ] Test accounts imported
- [ ] Demo data prepared
- [ ] Screen recording software ready

---

## **SCENE 1: INTRODUCTION (30 seconds)**

**[Show main.html page]**

**Script:**
"<PERSON><PERSON><PERSON>, je vais vous présenter <PERSON>, notre DApp d'assurance véhicule basée sur la blockchain. <PERSON><PERSON>, qui signifie 'protection' en arabe, révolutionne l'assurance automobile au Maroc grâce à la technologie blockchain."

**Actions:**
- Show main landing page
- Highlight the Arabic name "حماية"
- Point to key features: Blockchain, Security, Speed

---

## **SCENE 2: CLIENT REGISTRATION (1 minute)**

**[Navigate to client-registration.html]**

**Script:**
"Commençons par l'inscription d'un nouveau client. <PERSON> Ben <PERSON> souhaite assurer sa nouvelle Toyota Camry."

**Actions:**
1. Click "Nouveau Client"
2. Fill registration form:
   - Nom: Ahmed Ben Ali
   - CIN: AB123456
   - Date de naissance: 15/03/1992
   - Téléphone: +212 661-234567
   - Email: <EMAIL>
   - Adresse: Rue Hassan II, Casablanca

3. Upload documents:
   - CIN Front (show file upload)
   - CIN Back (show file upload)
   - Selfie (show file upload)

4. Submit registration
5. Show confirmation with verification ID

**Script:**
"Le système génère automatiquement un ID de vérification. Les documents sont stockés de manière sécurisée et envoyés pour validation administrative."

---

## **SCENE 3: ADMIN VERIFICATION (1 minute)**

**[Navigate to admin-app.html]**

**Script:**
"Passons maintenant à l'interface administrateur pour valider l'identité d'Ahmed."

**Actions:**
1. Open admin interface
2. Show pending verification requests
3. Click on Ahmed's request
4. Review uploaded documents:
   - Verify CIN front and back
   - Check selfie against ID photo
   - Validate information consistency

5. Approve verification
6. Show wallet generation process
7. Display generated wallet address

**Script:**
"L'administrateur vérifie l'authenticité des documents. Une fois approuvé, le système génère automatiquement un portefeuille sécurisé pour Ahmed."

---

## **SCENE 4: CLIENT ACCESS & VEHICLE REGISTRATION (1.5 minutes)**

**[Navigate to client-app.html]**

**Script:**
"Ahmed peut maintenant accéder à son espace client avec son portefeuille MetaMask."

**Actions:**
1. Connect MetaMask wallet
2. Show client dashboard
3. Navigate to vehicle registration
4. Fill vehicle information:
   - Plaque: 123456-A-07
   - Marque: Toyota
   - Modèle: Camry
   - Année: 2022
   - Ville: Casablanca

5. Submit vehicle registration
6. Show blockchain transaction
7. Display vehicle ID and confirmation

**Script:**
"Ahmed enregistre son véhicule sur la blockchain. Chaque véhicule reçoit un ID unique et l'historique complet est stocké de manière immuable."

---

## **SCENE 5: INSURANCE POLICY PURCHASE (1.5 minutes)**

**[Continue in client-app.html]**

**Script:**
"Maintenant, Ahmed va souscrire à une police d'assurance pour son véhicule."

**Actions:**
1. Navigate to insurance plans
2. Show plan comparison:
   - Basic: 0.05 ETH/mois
   - Standard: 0.08 ETH/mois  
   - Premium: 0.12 ETH/mois

3. Select Standard plan
4. Review coverage details
5. Confirm purchase with MetaMask
6. Show transaction processing
7. Display policy number and confirmation

**Script:**
"Ahmed choisit le plan Standard. Le paiement se fait directement en ETH via smart contract. La police est activée instantanément sur la blockchain."

---

## **SCENE 6: CLAIM SUBMISSION (1 minute)**

**[Continue in client-app.html]**

**Script:**
"Simulons maintenant un accident. Ahmed doit déposer une réclamation."

**Actions:**
1. Navigate to claims section
2. Click "Nouvelle Réclamation"
3. Fill claim details:
   - Type: Accident
   - Description: Collision mineure, pare-chocs endommagé
   - Montant: 1.5 ETH
   - Lieu: Avenue Mohammed V, Casablanca

4. Upload evidence:
   - Photos des dégâts
   - Rapport de police
   - Devis de réparation

5. Submit claim
6. Show claim ID generation

**Script:**
"Ahmed soumet sa réclamation avec toutes les preuves nécessaires. Le système génère un ID unique et enregistre tout sur la blockchain."

---

## **SCENE 7: INSURER CLAIM PROCESSING (1 minute)**

**[Navigate to insurer-app.html]**

**Script:**
"L'assureur reçoit automatiquement la réclamation et peut la traiter immédiatement."

**Actions:**
1. Open insurer dashboard
2. Show new claim notification
3. Review claim details
4. Examine uploaded evidence
5. Verify policy coverage
6. Approve claim
7. Process payment via smart contract
8. Show automatic fund transfer

**Script:**
"L'assureur vérifie la réclamation et l'approuve. Le paiement est automatiquement transféré à Ahmed via smart contract. Tout est transparent et traçable."

---

## **SCENE 8: REAL-TIME UPDATES & CONCLUSION (30 seconds)**

**[Switch back to client-app.html]**

**Script:**
"Ahmed reçoit instantanément la confirmation de paiement dans son portefeuille."

**Actions:**
1. Show updated claim status
2. Display payment confirmation
3. Show wallet balance increase
4. Highlight transaction hash

**Script:**
"En quelques minutes, Ahmed a reçu son indemnisation. Comparé aux semaines nécessaires avec l'assurance traditionnelle, Himaya offre une expérience révolutionnaire."

---

## **SCENE 9: FINAL SUMMARY (30 seconds)**

**[Show main.html or summary slide]**

**Script:**
"Himaya transforme l'assurance véhicule au Maroc avec:
- Transparence totale via blockchain
- Traitement instantané des réclamations  
- Sécurité cryptographique
- Réduction des coûts de 40%
- Disponibilité 24/7

Cette DApp positionne le Maroc comme leader de l'innovation blockchain dans l'assurance."

---

## **TECHNICAL NOTES:**

### **Camera/Recording Setup:**
- Use 1080p resolution minimum
- Ensure clear audio
- Screen recording at 30fps
- Close unnecessary applications

### **Browser Setup:**
- Use Chrome/Edge for best Web3 support
- Zoom to 125% for better visibility
- Clear cache before recording
- Have MetaMask ready

### **Timing:**
- Keep each scene under specified time
- Practice transitions between pages
- Have backup plans for technical issues
- Test all functionality before recording

### **Post-Production:**
- Add title cards for each section
- Include Arabic subtitles if needed
- Add background music (optional)
- Export in MP4 format for compatibility
