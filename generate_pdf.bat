@echo off
echo Generating Professional PDF Report...

REM Check if pandoc is installed
where pandoc >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo Pandoc is not installed. Please install pandoc first.
    echo Download from: https://pandoc.org/installing.html
    pause
    exit /b 1
)

REM Check if LaTeX is installed
where pdflatex >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo LaTeX is not installed. Please install MiKTeX or TeX Live first.
    echo MiKTeX: https://miktex.org/download
    echo TeX Live: https://www.tug.org/texlive/
    pause
    exit /b 1
)

echo Converting Markdown to PDF...

pandoc "HIMAYA_PROFESSIONAL_REPORT.md" ^
    -o "HIMAYA_PROFESSIONAL_REPORT.pdf" ^
    --pdf-engine=pdflatex ^
    --template=default ^
    --toc ^
    --toc-depth=3 ^
    --number-sections ^
    --highlight-style=tango ^
    --variable=geometry:margin=1in ^
    --variable=fontsize:11pt ^
    --variable=linestretch:1.2 ^
    --variable=documentclass:article ^
    --variable=papersize:a4 ^
    --variable=colorlinks:true ^
    --variable=linkcolor:blue ^
    --variable=urlcolor:blue ^
    --variable=citecolor:blue ^
    --filter pandoc-crossref ^
    --citeproc

if %ERRORLEVEL% EQU 0 (
    echo PDF generated successfully: HIMAYA_PROFESSIONAL_REPORT.pdf
    echo Opening PDF...
    start "" "HIMAYA_PROFESSIONAL_REPORT.pdf"
) else (
    echo Error generating PDF. Please check the markdown file and try again.
)

pause
