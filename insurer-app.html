<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Himaya - Espace Assureur</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #0f172a;
            min-height: 100vh;
            color: #e2e8f0;
        }

        .header {
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            border-bottom: 1px solid rgba(148, 163, 184, 0.2);
            color: white;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: bold;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
        }

        .stat-label {
            color: #718096;
            margin-top: 0.5rem;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
        }

        .card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .card-title {
            font-size: 1.3rem;
            font-weight: bold;
            color: #4a5568;
            margin-bottom: 1rem;
        }

        .claim-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            border-bottom: 1px solid #e2e8f0;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        .claim-item:hover {
            background-color: #f7fafc;
        }

        .claim-info h4 {
            color: #4a5568;
            margin-bottom: 0.25rem;
        }

        .claim-info p {
            color: #718096;
            font-size: 0.9rem;
        }

        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .status-pending {
            background: #fed7d7;
            color: #c53030;
        }

        .status-review {
            background: #feebc8;
            color: #c05621;
        }

        .status-approved {
            background: #c6f6d5;
            color: #22543d;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin: 0.25rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 2rem;
            border-radius: 15px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: #000;
        }

        .detail-row {
            display: flex;
            justify-content: space-between;
            padding: 0.5rem 0;
            border-bottom: 1px solid #e2e8f0;
        }

        .detail-label {
            font-weight: 600;
            color: #4a5568;
        }

        .detail-value {
            color: #718096;
        }

        @media (max-width: 768px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo">HIMAYA 🏢 Espace Assureur</div>
        <div>
            <a href="main.html" class="btn" style="background: rgba(255,255,255,0.2); color: white;">Déconnexion</a>
        </div>
    </div>

    <div class="container">
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number" id="pendingClaims">0</div>
                <div class="stat-label">Réclamations en Attente</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="activePolicies">0</div>
                <div class="stat-label">Polices Actives</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="ethCollected">0.00</div>
                <div class="stat-label">ETH Collectés ce Mois</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="ethPaid">0.00</div>
                <div class="stat-label">ETH Payés en Réclamations</div>
            </div>
        </div>

        <div class="dashboard-grid">
            <div class="card">
                <div class="card-title">📋 Réclamations en Attente</div>
                <div id="claimsList">
                    <!-- Real claims data will be loaded here -->
                </div>
            </div>

            <div class="card">
                <div class="card-title">📊 Statistiques Récentes</div>
                <div style="margin-bottom: 1rem;">
                    <h4>Réclamations par Type (Ce Mois)</h4>
                    <div style="margin: 1rem 0;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                            <span>Accidents</span>
                            <span>60%</span>
                        </div>
                        <div style="background: #e2e8f0; height: 8px; border-radius: 4px;">
                            <div style="background: #667eea; height: 100%; width: 60%; border-radius: 4px;"></div>
                        </div>
                    </div>
                    
                    <div style="margin: 1rem 0;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                            <span>Vol</span>
                            <span>25%</span>
                        </div>
                        <div style="background: #e2e8f0; height: 8px; border-radius: 4px;">
                            <div style="background: #f6ad55; height: 100%; width: 25%; border-radius: 4px;"></div>
                        </div>
                    </div>
                    
                    <div style="margin: 1rem 0;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                            <span>Dommages</span>
                            <span>15%</span>
                        </div>
                        <div style="background: #e2e8f0; height: 8px; border-radius: 4px;">
                            <div style="background: #48bb78; height: 100%; width: 15%; border-radius: 4px;"></div>
                        </div>
                    </div>
                </div>
                
                <div style="margin-top: 2rem;">
                    <h4>Temps de Traitement Moyen</h4>
                    <p style="font-size: 2rem; color: #667eea; font-weight: bold; margin: 0.5rem 0;">1.8 jours</p>
                    <p style="color: #48bb78; font-size: 0.9rem;">↓ 85% plus rapide que traditionnel</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Claim Detail Modal -->
    <div id="claimModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal()">&times;</span>
            <h2>📋 Détails de la Réclamation</h2>
            
            <div id="claimDetails">
                <div class="detail-row">
                    <span class="detail-label">ID Réclamation:</span>
                    <span class="detail-value" id="claimId">CLM-12345678</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Client:</span>
                    <span class="detail-value" id="clientName">Ahmed Ben Ali</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Véhicule:</span>
                    <span class="detail-value" id="vehicleInfo">Toyota Camry 2022 (123456-A-07)</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Type:</span>
                    <span class="detail-value" id="claimType">Accident</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Montant Réclamé:</span>
                    <span class="detail-value" id="claimAmount">1.5 ETH</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Date de l'Incident:</span>
                    <span class="detail-value" id="incidentDate">15/04/2024</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Lieu:</span>
                    <span class="detail-value" id="incidentLocation">Avenue Mohammed V, Casablanca</span>
                </div>
            </div>

            <div style="margin: 2rem 0;">
                <h4>Description:</h4>
                <p id="claimDescription" style="background: #f7fafc; padding: 1rem; border-radius: 8px; margin-top: 0.5rem;">
                    Collision mineure avec un autre véhicule. Dommages au pare-chocs avant et au phare droit. Rapport de police disponible.
                </p>
            </div>

            <div style="margin: 2rem 0;">
                <h4>Documents Fournis:</h4>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 1rem; margin-top: 1rem;">
                    <div style="text-align: center; padding: 1rem; border: 2px dashed #cbd5e0; border-radius: 8px;">
                        <div style="font-size: 2rem;">📄</div>
                        <p>Rapport de Police</p>
                    </div>
                    <div style="text-align: center; padding: 1rem; border: 2px dashed #cbd5e0; border-radius: 8px;">
                        <div style="font-size: 2rem;">📸</div>
                        <p>Photos des Dégâts</p>
                    </div>
                    <div style="text-align: center; padding: 1rem; border: 2px dashed #cbd5e0; border-radius: 8px;">
                        <div style="font-size: 2rem;">💰</div>
                        <p>Devis de Réparation</p>
                    </div>
                </div>
            </div>

            <div style="margin-top: 2rem; text-align: center;">
                <button class="btn btn-success" onclick="approveClaim()">✅ Approuver</button>
                <button class="btn btn-danger" onclick="rejectClaim()">❌ Rejeter</button>
                <button class="btn btn-primary" onclick="payClaim()">💰 Payer</button>
            </div>
        </div>
    </div>

    <script>
        let currentClaimId = '';

        // Load real claims data
        function loadClaims() {
            const claims = JSON.parse(localStorage.getItem('userClaims') || '[]');
            const claimsList = document.getElementById('claimsList');

            if (claims.length === 0) {
                claimsList.innerHTML = `
                    <div style="text-align: center; padding: 2rem; color: #718096;">
                        <div style="font-size: 3rem; margin-bottom: 1rem;">📋</div>
                        <p>Aucune réclamation en attente</p>
                        <p style="font-size: 0.9rem;">Les nouvelles réclamations apparaîtront ici</p>
                    </div>
                `;
            } else {
                claimsList.innerHTML = claims.map(claim => {
                    const submissionDate = new Date(claim.submissionDate).toLocaleDateString('fr-FR');
                    return `
                        <div class="claim-item" onclick="openClaimModal('${claim.id}')">
                            <div class="claim-info">
                                <h4>${claim.clientName} - ${claim.type}</h4>
                                <p>${claim.vehicleInfo} | Montant: ${claim.amount} ETH</p>
                                <small>Soumis le ${submissionDate}</small>
                            </div>
                            <span class="status-badge status-pending">En Attente</span>
                        </div>
                    `;
                }).join('');
            }
        }

        function openClaimModal(claimId) {
            currentClaimId = claimId;

            // Get real claim data
            const claims = JSON.parse(localStorage.getItem('userClaims') || '[]');
            const claim = claims.find(c => c.id === claimId);

            if (claim) {
                document.getElementById('claimId').textContent = claimId;
                document.getElementById('clientName').textContent = claim.clientName;
                document.getElementById('vehicleInfo').textContent = claim.vehicleInfo;
                document.getElementById('claimType').textContent = claim.type;
                document.getElementById('claimAmount').textContent = claim.amount + ' ETH';
                document.getElementById('incidentDate').textContent = new Date(claim.incidentDate).toLocaleDateString('fr-FR');
                document.getElementById('incidentLocation').textContent = claim.location;
                document.getElementById('claimDescription').textContent = claim.description;
            }

            document.getElementById('claimModal').style.display = 'block';
        }

        function closeModal() {
            document.getElementById('claimModal').style.display = 'none';
        }

        function approveClaim() {
            if (confirm('Approuver cette réclamation?')) {
                alert('✅ Réclamation approuvée avec succès!\nTransaction enregistrée sur la blockchain.');
                closeModal();
                // Update UI to show approved status
            }
        }

        function rejectClaim() {
            const reason = prompt('Raison du rejet:');
            if (reason) {
                alert('❌ Réclamation rejetée.\nRaison: ' + reason);
                closeModal();
                // Update UI to show rejected status
            }
        }

        function payClaim() {
            if (confirm('Procéder au paiement de cette réclamation?')) {
                alert('💰 Paiement effectué avec succès!\nFonds transférés au portefeuille du client via smart contract.');
                closeModal();
                // Update UI and stats
            }
        }

        // Update statistics
        function updateStats() {
            const claims = JSON.parse(localStorage.getItem('userClaims') || '[]');
            const policies = JSON.parse(localStorage.getItem('userPolicies') || '[]');

            document.getElementById('pendingClaims').textContent = claims.length;
            document.getElementById('activePolicies').textContent = policies.filter(p => p.active).length;

            // Calculate collected ETH from policies
            const collected = policies.reduce((sum, policy) => sum + parseFloat(policy.price), 0);
            document.getElementById('ethCollected').textContent = collected.toFixed(2);

            // ETH paid in claims (for demo, assume all claims are paid)
            const paid = claims.reduce((sum, claim) => sum + parseFloat(claim.amount), 0);
            document.getElementById('ethPaid').textContent = paid.toFixed(2);
        }

        // Initialize on page load
        window.addEventListener('load', function() {
            loadClaims();
            updateStats();
        });

        // Close modal when clicking outside
        window.onclick = function(event) {
            if (event.target.classList.contains('modal')) {
                event.target.style.display = 'none';
            }
        }
    </script>
</body>
</html>
