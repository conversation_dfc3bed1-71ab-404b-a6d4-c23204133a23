<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Himaya - Vérification du Statut</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            padding: 3rem;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            text-align: center;
            max-width: 600px;
            width: 100%;
        }

        .header {
            margin-bottom: 2rem;
        }

        .header h1 {
            font-size: 2.5rem;
            color: #4a5568;
            margin-bottom: 0.5rem;
        }

        .header p {
            color: #718096;
            font-size: 1.1rem;
        }

        .search-section {
            margin-bottom: 2rem;
        }

        .search-group {
            display: flex;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        input {
            flex: 1;
            padding: 1rem;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        input:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn {
            padding: 1rem 2rem;
            border: none;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-secondary {
            background: #e2e8f0;
            color: #4a5568;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        .status-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin: 2rem 0;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            display: none;
        }

        .status-pending {
            border-left: 5px solid #f6ad55;
        }

        .status-approved {
            border-left: 5px solid #48bb78;
        }

        .status-rejected {
            border-left: 5px solid #f56565;
        }

        .status-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
        }

        .status-title {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 1rem;
        }

        .status-details {
            text-align: left;
            margin-top: 1.5rem;
        }

        .detail-row {
            display: flex;
            justify-content: space-between;
            padding: 0.5rem 0;
            border-bottom: 1px solid #e2e8f0;
        }

        .detail-label {
            font-weight: 600;
            color: #4a5568;
        }

        .detail-value {
            color: #718096;
        }

        .timeline {
            margin-top: 2rem;
            text-align: left;
        }

        .timeline-item {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
            padding: 1rem;
            background: #f7fafc;
            border-radius: 8px;
        }

        .timeline-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            font-weight: bold;
        }

        .timeline-completed {
            background: #48bb78;
            color: white;
        }

        .timeline-current {
            background: #f6ad55;
            color: white;
        }

        .timeline-pending {
            background: #e2e8f0;
            color: #a0aec0;
        }

        .wallet-info {
            background: #e6fffa;
            border: 2px solid #38b2ac;
            border-radius: 10px;
            padding: 1.5rem;
            margin-top: 1rem;
        }

        .wallet-address {
            font-family: monospace;
            background: #2d3748;
            color: #68d391;
            padding: 0.5rem;
            border-radius: 5px;
            word-break: break-all;
            margin: 0.5rem 0;
        }

        .no-result {
            color: #e53e3e;
            font-size: 1.1rem;
            margin: 2rem 0;
            display: none;
        }

        @media (max-width: 768px) {
            .search-group {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📋 Vérification du Statut</h1>
            <p>Suivez l'état de votre demande d'inscription</p>
        </div>

        <div class="search-section">
            <div class="search-group">
                <input type="text" id="verificationId" placeholder="Entrez votre ID de vérification (ex: VER-12345678)">
                <button class="btn btn-primary" onclick="checkStatus()">Vérifier</button>
            </div>
            <p style="color: #718096; font-size: 0.9rem;">
                Votre ID de vérification vous a été fourni lors de votre inscription
            </p>
        </div>

        <div class="no-result" id="noResult">
            ❌ Aucune demande trouvée avec cet ID de vérification
        </div>

        <!-- Pending Status -->
        <div class="status-card status-pending" id="pendingStatus">
            <div class="status-icon">⏳</div>
            <div class="status-title" style="color: #d69e2e;">Vérification en Cours</div>
            <p>Votre demande est en cours de traitement par notre équipe administrative.</p>
            
            <div class="status-details">
                <div class="detail-row">
                    <span class="detail-label">ID de Vérification:</span>
                    <span class="detail-value" id="pendingId">-</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Date de Soumission:</span>
                    <span class="detail-value" id="pendingDate">-</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Nom Complet:</span>
                    <span class="detail-value" id="pendingName">-</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">CIN:</span>
                    <span class="detail-value" id="pendingCin">-</span>
                </div>
            </div>

            <div class="timeline">
                <h4>Étapes de Vérification:</h4>
                <div class="timeline-item">
                    <div class="timeline-icon timeline-completed">✓</div>
                    <div>
                        <strong>Soumission Reçue</strong><br>
                        <small>Documents téléchargés avec succès</small>
                    </div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-icon timeline-current">2</div>
                    <div>
                        <strong>Vérification Administrative</strong><br>
                        <small>Validation des documents en cours</small>
                    </div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-icon timeline-pending">3</div>
                    <div>
                        <strong>Génération du Portefeuille</strong><br>
                        <small>En attente d'approbation</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Approved Status -->
        <div class="status-card status-approved" id="approvedStatus">
            <div class="status-icon">✅</div>
            <div class="status-title" style="color: #38a169;">Vérification Approuvée</div>
            <p>Félicitations! Votre identité a été vérifiée avec succès.</p>
            
            <div class="status-details">
                <div class="detail-row">
                    <span class="detail-label">ID de Vérification:</span>
                    <span class="detail-value" id="approvedId">-</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Date d'Approbation:</span>
                    <span class="detail-value" id="approvedDate">-</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Nom Complet:</span>
                    <span class="detail-value" id="approvedName">-</span>
                </div>
            </div>

            <div class="wallet-info">
                <h4>🔐 Informations du Portefeuille</h4>
                <p><strong>Adresse:</strong></p>
                <div class="wallet-address" id="walletAddress">******************************************</div>
                <p style="margin-top: 1rem;"><strong>Instructions:</strong></p>
                <ol style="margin-left: 1rem; margin-top: 0.5rem;">
                    <li>Configurez MetaMask avec le réseau Himaya</li>
                    <li>Importez votre portefeuille avec la clé privée fournie</li>
                    <li>Accédez à votre espace client</li>
                </ol>
            </div>

            <div style="margin-top: 2rem;">
                <a href="client-app.html" class="btn btn-primary">Accéder à Mon Espace</a>
            </div>
        </div>

        <div style="margin-top: 2rem;">
            <a href="main.html" class="btn btn-secondary">← Retour à l'Accueil</a>
        </div>
    </div>

    <script>
        function checkStatus() {
            const verificationId = document.getElementById('verificationId').value.trim();
            
            if (!verificationId) {
                alert('Veuillez entrer votre ID de vérification');
                return;
            }

            // Hide all status cards
            document.querySelectorAll('.status-card').forEach(card => {
                card.style.display = 'none';
            });
            document.getElementById('noResult').style.display = 'none';

            // Check localStorage for demo data
            const registrationData = localStorage.getItem('registrationData');
            
            if (registrationData) {
                const data = JSON.parse(registrationData);
                
                if (data.verificationId === verificationId) {
                    if (data.status === 'pending') {
                        showPendingStatus(data);
                    } else if (data.status === 'approved') {
                        showApprovedStatus(data);
                    }
                    return;
                }
            }

            // Demo data for testing
            const demoData = {
                'VER-12345678': {
                    status: 'pending',
                    firstName: 'Ahmed',
                    lastName: 'Ben Ali',
                    cin: 'AB123456',
                    submissionDate: '2024-12-06T10:30:00Z'
                },
                'VER-87654321': {
                    status: 'approved',
                    firstName: 'Fatima',
                    lastName: 'El Mansouri',
                    cin: 'FM789012',
                    approvalDate: '2024-12-05T14:20:00Z',
                    walletAddress: '******************************************'
                }
            };

            if (demoData[verificationId]) {
                const data = demoData[verificationId];
                if (data.status === 'pending') {
                    showPendingStatus(data);
                } else if (data.status === 'approved') {
                    showApprovedStatus(data);
                }
            } else {
                document.getElementById('noResult').style.display = 'block';
            }
        }

        function showPendingStatus(data) {
            document.getElementById('pendingStatus').style.display = 'block';
            document.getElementById('pendingId').textContent = data.verificationId || 'VER-12345678';
            document.getElementById('pendingDate').textContent = formatDate(data.submissionDate || new Date().toISOString());
            document.getElementById('pendingName').textContent = `${data.firstName || 'Ahmed'} ${data.lastName || 'Ben Ali'}`;
            document.getElementById('pendingCin').textContent = data.cin || 'AB123456';
        }

        function showApprovedStatus(data) {
            document.getElementById('approvedStatus').style.display = 'block';
            document.getElementById('approvedId').textContent = data.verificationId || 'VER-87654321';
            document.getElementById('approvedDate').textContent = formatDate(data.approvalDate || new Date().toISOString());
            document.getElementById('approvedName').textContent = `${data.firstName || 'Fatima'} ${data.lastName || 'El Mansouri'}`;
            document.getElementById('walletAddress').textContent = data.walletAddress || '******************************************';
        }

        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('fr-FR', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        // Auto-fill if coming from registration
        window.addEventListener('load', function() {
            const urlParams = new URLSearchParams(window.location.search);
            const id = urlParams.get('id');
            if (id) {
                document.getElementById('verificationId').value = id;
                checkStatus();
            }
        });

        // Enter key support
        document.getElementById('verificationId').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                checkStatus();
            }
        });
    </script>
</body>
</html>
