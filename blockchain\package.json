{"name": "insurance-blockchain", "version": "1.0.0", "description": "Smart contracts for vehicle insurance claims DApp", "main": "index.js", "scripts": {"compile": "hardhat compile", "deploy": "hardhat run scripts/deploy.js --network ganache", "test": "hardhat test", "node": "hardhat node"}, "keywords": ["blockchain", "insurance", "hyperledger", "besu"], "author": "Student Project", "license": "MIT", "devDependencies": {"@nomicfoundation/hardhat-toolbox": "^4.0.0", "hardhat": "^2.19.0"}, "dependencies": {"@openzeppelin/contracts": "^5.0.0", "dotenv": "^16.3.1"}}