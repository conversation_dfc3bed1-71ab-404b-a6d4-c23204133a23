[{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "OwnableInvalidOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "OwnableUnauthorizedAccount", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "vehicleId", "type": "uint256"}, {"indexed": true, "internalType": "string", "name": "vin", "type": "string"}, {"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "registrationDate", "type": "uint256"}], "name": "VehicleRegistered", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "vehicleId", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "VehicleTransferred", "type": "event"}, {"inputs": [{"internalType": "address", "name": "_owner", "type": "address"}], "name": "getOwnerVehicles", "outputs": [{"internalType": "uint256[]", "name": "", "type": "uint256[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getTotalVehicles", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_vehicleId", "type": "uint256"}], "name": "getVehicle", "outputs": [{"components": [{"internalType": "uint256", "name": "id", "type": "uint256"}, {"internalType": "string", "name": "vin", "type": "string"}, {"internalType": "string", "name": "make", "type": "string"}, {"internalType": "string", "name": "model", "type": "string"}, {"internalType": "uint256", "name": "year", "type": "uint256"}, {"internalType": "string", "name": "color", "type": "string"}, {"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "bool", "name": "isRegistered", "type": "bool"}, {"internalType": "uint256", "name": "registrationDate", "type": "uint256"}], "internalType": "struct VehicleRegistry.Vehicle", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "_vin", "type": "string"}], "name": "getVehicleByVin", "outputs": [{"components": [{"internalType": "uint256", "name": "id", "type": "uint256"}, {"internalType": "string", "name": "vin", "type": "string"}, {"internalType": "string", "name": "make", "type": "string"}, {"internalType": "string", "name": "model", "type": "string"}, {"internalType": "uint256", "name": "year", "type": "uint256"}, {"internalType": "string", "name": "color", "type": "string"}, {"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "bool", "name": "isRegistered", "type": "bool"}, {"internalType": "uint256", "name": "registrationDate", "type": "uint256"}], "internalType": "struct VehicleRegistry.Vehicle", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "_vin", "type": "string"}, {"internalType": "string", "name": "_make", "type": "string"}, {"internalType": "string", "name": "_model", "type": "string"}, {"internalType": "uint256", "name": "_year", "type": "uint256"}, {"internalType": "string", "name": "_color", "type": "string"}], "name": "registerVehicle", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}]