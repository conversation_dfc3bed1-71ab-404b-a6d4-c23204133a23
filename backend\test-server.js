const express = require('express');
const cors = require('cors');
require('dotenv').config();

const { initializeBlockchain } = require('./config/blockchain');

const app = express();

// Basic middleware
app.use(cors());
app.use(express.json());

// Health check
app.get('/health', (req, res) => {
  res.json({ status: 'OK', message: 'Test server running' });
});

// Blockchain info endpoint
app.get('/api/blockchain/info', async (req, res) => {
  try {
    const { web3, contracts } = require('./config/blockchain');
    
    const blockNumber = await web3.eth.getBlockNumber();
    const networkId = await web3.eth.net.getId();
    
    res.json({
      blockNumber: blockNumber.toString(),
      networkId: networkId.toString(),
      contracts: {
        vehicleRegistry: contracts.vehicleRegistry.options.address,
        insurancePolicy: contracts.insurancePolicy.options.address,
        claimManager: contracts.claimManager.options.address
      }
    });
  } catch (error) {
    console.error('Blockchain info error:', error);
    res.status(500).json({ error: 'Failed to get blockchain info' });
  }
});

const PORT = process.env.PORT || 3001;

// Initialize blockchain first
initializeBlockchain()
  .then(() => {
    app.listen(PORT, () => {
      console.log(`🚀 Test server running on port ${PORT}`);
      console.log(`🔗 Blockchain: ${process.env.ETHEREUM_RPC_URL}`);
    });
  })
  .catch((error) => {
    console.error('Failed to initialize blockchain:', error);
    process.exit(1);
  });

module.exports = app;
