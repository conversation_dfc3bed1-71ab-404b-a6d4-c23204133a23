<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔗 Test Blockchain Himaya</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 50%, #1a1a1a 100%);
            color: #e5e5e5;
            min-height: 100vh;
            padding: 2rem;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 3rem;
            background: rgba(139, 92, 246, 0.1);
            border: 1px solid #8b5cf6;
            border-radius: 20px;
            padding: 2rem;
        }
        
        .title {
            font-size: 2.5rem;
            color: #8b5cf6;
            margin-bottom: 1rem;
        }
        
        .card {
            background: #2d3748;
            border: 1px solid #4a5568;
            border-radius: 16px;
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .card-title {
            font-size: 1.5rem;
            color: #f7fafc;
            margin-bottom: 1rem;
        }
        
        .btn {
            background: #8b5cf6;
            border: 1px solid #8b5cf6;
            border-radius: 8px;
            padding: 0.75rem 1.5rem;
            color: white;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            margin: 0.5rem;
        }
        
        .btn:hover {
            background: #7c3aed;
            transform: translateY(-1px);
        }
        
        .btn-success {
            background: #10b981;
            border-color: #10b981;
        }
        
        .btn-danger {
            background: #ef4444;
            border-color: #ef4444;
        }
        
        .status {
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
        }
        
        .status-success {
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid #10b981;
            color: #10b981;
        }
        
        .status-error {
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid #ef4444;
            color: #ef4444;
        }
        
        .status-info {
            background: rgba(139, 92, 246, 0.1);
            border: 1px solid #8b5cf6;
            color: #8b5cf6;
        }
        
        .log {
            background: #374151;
            border: 1px solid #4a5568;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
            font-family: monospace;
            font-size: 0.9rem;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .form-input {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #4a5568;
            border-radius: 8px;
            background: #374151;
            color: #e5e7eb;
            margin-bottom: 1rem;
        }
        
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">🔗 Test Blockchain Himaya</h1>
            <p>Vérification de la connectivité et des smart contracts</p>
        </div>

        <!-- Connection Status -->
        <div class="card">
            <h2 class="card-title">📡 Statut de Connexion</h2>
            <div id="connectionStatus" class="status status-info">
                🔄 Vérification de la connexion...
            </div>
            <button class="btn" onclick="testConnection()">🔄 Tester la Connexion</button>
            <button class="btn" onclick="connectWallet()">🦊 Connecter MetaMask</button>
        </div>

        <!-- Contract Information -->
        <div class="card">
            <h2 class="card-title">📋 Informations des Contrats</h2>
            <div id="contractInfo" class="log">
                Chargement des informations des contrats...
            </div>
            <button class="btn" onclick="loadContractInfo()">📊 Charger Infos Contrats</button>
        </div>

        <!-- Test Functions -->
        <div class="grid">
            <!-- Vehicle Registration Test -->
            <div class="card">
                <h3 class="card-title">🚗 Test Enregistrement Véhicule</h3>
                <input type="text" id="vehiclePlate" class="form-input" placeholder="Immatriculation (ex: 123456-A-07)">
                <input type="text" id="vehicleMake" class="form-input" placeholder="Marque (ex: Toyota)">
                <input type="text" id="vehicleModel" class="form-input" placeholder="Modèle (ex: Camry)">
                <input type="number" id="vehicleYear" class="form-input" placeholder="Année (ex: 2023)">
                <button class="btn btn-success" onclick="testVehicleRegistration()">🚗 Enregistrer sur Blockchain</button>
                <div id="vehicleResult" class="log" style="display: none;"></div>
            </div>

            <!-- Policy Creation Test -->
            <div class="card">
                <h3 class="card-title">📋 Test Création Police</h3>
                <input type="number" id="vehicleId" class="form-input" placeholder="ID Véhicule">
                <input type="number" id="premiumAmount" class="form-input" placeholder="Montant Prime (ETH)" step="0.01">
                <input type="number" id="coverageAmount" class="form-input" placeholder="Montant Couverture (ETH)" step="0.01">
                <input type="number" id="duration" class="form-input" placeholder="Durée (jours)">
                <button class="btn btn-success" onclick="testPolicyCreation()">📋 Créer Police</button>
                <div id="policyResult" class="log" style="display: none;"></div>
            </div>
        </div>

        <!-- Claim Test -->
        <div class="card">
            <h2 class="card-title">🔍 Test Réclamation</h2>
            <div class="grid">
                <div>
                    <input type="number" id="claimPolicyId" class="form-input" placeholder="ID Police">
                    <input type="number" id="claimAmount" class="form-input" placeholder="Montant Réclamation (ETH)" step="0.01">
                    <input type="text" id="claimDescription" class="form-input" placeholder="Description de la réclamation">
                </div>
                <div>
                    <button class="btn btn-success" onclick="testClaimSubmission()">🔍 Soumettre Réclamation</button>
                    <button class="btn" onclick="testClaimApproval()">✅ Approuver Réclamation</button>
                    <button class="btn btn-danger" onclick="testClaimRejection()">❌ Rejeter Réclamation</button>
                </div>
            </div>
            <div id="claimResult" class="log" style="display: none;"></div>
        </div>

        <!-- Transaction Log -->
        <div class="card">
            <h2 class="card-title">📜 Journal des Transactions</h2>
            <div id="transactionLog" class="log">
                Aucune transaction pour le moment...
            </div>
            <button class="btn" onclick="clearLog()">🗑️ Effacer Journal</button>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/web3@4.2.0/dist/web3.min.js"></script>
    <script>
        // Blockchain Test Application
        const BlockchainTest = {
            web3: null,
            account: null,
            contracts: {},
            connected: false
        };

        // Contract addresses from deployment
        const CONTRACT_ADDRESSES = {
            VehicleRegistry: "0x880EC53Af800b5Cd051531672EF4fc4De233bD5d",
            InsurancePolicy: "0x3Dc2cd8F2E345951508427872d8ac9f635fBe0EC",
            ClaimManager: "0xe213D8b68cA3d01e51a6dBA669De59AC9A8359eE"
        };

        // Simplified ABIs for testing
        const CONTRACT_ABIS = {
            VehicleRegistry: [
                {
                    "inputs": [{"type": "string", "name": "_licensePlate"}, {"type": "string", "name": "_make"}, {"type": "string", "name": "_model"}, {"type": "uint256", "name": "_year"}, {"type": "string", "name": "_city"}],
                    "name": "registerVehicle",
                    "outputs": [{"type": "uint256"}],
                    "type": "function"
                },
                {
                    "inputs": [{"type": "uint256", "name": "_vehicleId"}],
                    "name": "getVehicle",
                    "outputs": [{"type": "tuple", "components": [{"type": "uint256", "name": "id"}, {"type": "string", "name": "licensePlate"}, {"type": "string", "name": "make"}, {"type": "string", "name": "model"}, {"type": "uint256", "name": "year"}, {"type": "address", "name": "owner"}, {"type": "string", "name": "city"}, {"type": "bool", "name": "isActive"}]}],
                    "type": "function"
                }
            ],
            InsurancePolicy: [
                {
                    "inputs": [{"type": "uint256", "name": "_vehicleId"}, {"type": "address", "name": "_policyholder"}, {"type": "uint8", "name": "_coverageType"}, {"type": "uint256", "name": "_premiumAmount"}, {"type": "uint256", "name": "_coverageAmount"}, {"type": "uint256", "name": "_deductible"}, {"type": "uint256", "name": "_durationInDays"}, {"type": "string", "name": "_policyNumber"}],
                    "name": "createPolicy",
                    "outputs": [{"type": "uint256"}],
                    "type": "function"
                }
            ],
            ClaimManager: [
                {
                    "inputs": [{"type": "uint256", "name": "_policyId"}, {"type": "string", "name": "_description"}, {"type": "uint256", "name": "_claimedAmount"}, {"type": "string[]", "name": "_evidenceHashes"}],
                    "name": "submitClaim",
                    "outputs": [{"type": "uint256"}],
                    "type": "function"
                }
            ]
        };

        // Utility Functions
        function log(message) {
            const logElement = document.getElementById('transactionLog');
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function showStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.className = `status status-${type}`;
            element.innerHTML = message;
        }

        function showResult(elementId, message) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.innerHTML = message;
        }

        // Connection Functions
        async function testConnection() {
            try {
                showStatus('connectionStatus', '🔄 Test de connexion au blockchain...', 'info');
                
                // Test direct connection to blockchain
                const web3 = new Web3('http://localhost:8545');
                const isConnected = await web3.eth.net.isListening();
                
                if (isConnected) {
                    const chainId = await web3.eth.getChainId();
                    const blockNumber = await web3.eth.getBlockNumber();
                    const accounts = await web3.eth.getAccounts();
                    
                    showStatus('connectionStatus', 
                        `✅ Connexion réussie!\n` +
                        `🔗 Chain ID: ${chainId}\n` +
                        `📦 Bloc actuel: ${blockNumber}\n` +
                        `👥 Comptes disponibles: ${accounts.length}`, 'success');
                    
                    log(`✅ Connexion blockchain réussie - Chain ID: ${chainId}, Bloc: ${blockNumber}`);
                    
                    BlockchainTest.web3 = web3;
                    return true;
                } else {
                    throw new Error('Blockchain non accessible');
                }
            } catch (error) {
                showStatus('connectionStatus', `❌ Erreur de connexion: ${error.message}`, 'error');
                log(`❌ Erreur de connexion: ${error.message}`);
                return false;
            }
        }

        async function connectWallet() {
            if (!window.ethereum) {
                showStatus('connectionStatus', '❌ MetaMask non détecté', 'error');
                return;
            }

            try {
                showStatus('connectionStatus', '🔄 Connexion à MetaMask...', 'info');
                
                const accounts = await window.ethereum.request({
                    method: 'eth_requestAccounts'
                });

                if (accounts.length > 0) {
                    BlockchainTest.account = accounts[0];
                    BlockchainTest.web3 = new Web3(window.ethereum);
                    BlockchainTest.connected = true;

                    const balance = await BlockchainTest.web3.eth.getBalance(BlockchainTest.account);
                    const ethBalance = BlockchainTest.web3.utils.fromWei(balance, 'ether');

                    showStatus('connectionStatus', 
                        `✅ MetaMask connecté!\n` +
                        `👤 Compte: ${BlockchainTest.account.slice(0, 6)}...${BlockchainTest.account.slice(-4)}\n` +
                        `💰 Solde: ${parseFloat(ethBalance).toFixed(4)} ETH`, 'success');
                    
                    log(`✅ MetaMask connecté - Compte: ${BlockchainTest.account}`);
                    
                    // Initialize contracts
                    initializeContracts();
                }
            } catch (error) {
                showStatus('connectionStatus', `❌ Erreur MetaMask: ${error.message}`, 'error');
                log(`❌ Erreur MetaMask: ${error.message}`);
            }
        }

        function initializeContracts() {
            try {
                BlockchainTest.contracts.VehicleRegistry = new BlockchainTest.web3.eth.Contract(
                    CONTRACT_ABIS.VehicleRegistry,
                    CONTRACT_ADDRESSES.VehicleRegistry
                );
                
                BlockchainTest.contracts.InsurancePolicy = new BlockchainTest.web3.eth.Contract(
                    CONTRACT_ABIS.InsurancePolicy,
                    CONTRACT_ADDRESSES.InsurancePolicy
                );
                
                BlockchainTest.contracts.ClaimManager = new BlockchainTest.web3.eth.Contract(
                    CONTRACT_ABIS.ClaimManager,
                    CONTRACT_ADDRESSES.ClaimManager
                );
                
                log('✅ Contrats initialisés avec succès');
            } catch (error) {
                log(`❌ Erreur initialisation contrats: ${error.message}`);
            }
        }

        function loadContractInfo() {
            const info = `
📋 INFORMATIONS DES CONTRATS DÉPLOYÉS:

🚗 VehicleRegistry:
   Adresse: ${CONTRACT_ADDRESSES.VehicleRegistry}
   
📋 InsurancePolicy:
   Adresse: ${CONTRACT_ADDRESSES.InsurancePolicy}
   
🔍 ClaimManager:
   Adresse: ${CONTRACT_ADDRESSES.ClaimManager}

🌐 Réseau: Himaya Private Blockchain
🔗 Chain ID: 1337
📡 RPC: http://localhost:8545

⚠️ Note: Assurez-vous que MetaMask est configuré pour ce réseau
            `;
            
            document.getElementById('contractInfo').innerText = info;
            log('📋 Informations des contrats chargées');
        }

        // Test Functions
        async function testVehicleRegistration() {
            if (!BlockchainTest.connected) {
                alert('⚠️ Veuillez d\'abord connecter MetaMask');
                return;
            }

            const plate = document.getElementById('vehiclePlate').value;
            const make = document.getElementById('vehicleMake').value;
            const model = document.getElementById('vehicleModel').value;
            const year = document.getElementById('vehicleYear').value;

            if (!plate || !make || !model || !year) {
                alert('⚠️ Veuillez remplir tous les champs');
                return;
            }

            try {
                showResult('vehicleResult', '🔄 Enregistrement du véhicule sur la blockchain...');
                log(`🚗 Tentative d'enregistrement véhicule: ${make} ${model} ${year}`);

                const tx = await BlockchainTest.contracts.VehicleRegistry.methods
                    .registerVehicle(plate, make, model, parseInt(year), 'Casablanca')
                    .send({
                        from: BlockchainTest.account,
                        gas: 500000
                    });

                showResult('vehicleResult', 
                    `✅ Véhicule enregistré avec succès!\n` +
                    `🔗 Hash de transaction: ${tx.transactionHash}\n` +
                    `⛽ Gas utilisé: ${tx.gasUsed}`);
                
                log(`✅ Véhicule enregistré - TX: ${tx.transactionHash}`);

                // Clear form
                document.getElementById('vehiclePlate').value = '';
                document.getElementById('vehicleMake').value = '';
                document.getElementById('vehicleModel').value = '';
                document.getElementById('vehicleYear').value = '';

            } catch (error) {
                showResult('vehicleResult', `❌ Erreur: ${error.message}`);
                log(`❌ Erreur enregistrement véhicule: ${error.message}`);
            }
        }

        async function testPolicyCreation() {
            if (!BlockchainTest.connected) {
                alert('⚠️ Veuillez d\'abord connecter MetaMask');
                return;
            }

            const vehicleId = document.getElementById('vehicleId').value;
            const premium = document.getElementById('premiumAmount').value;
            const coverage = document.getElementById('coverageAmount').value;
            const duration = document.getElementById('duration').value;

            if (!vehicleId || !premium || !coverage || !duration) {
                alert('⚠️ Veuillez remplir tous les champs');
                return;
            }

            try {
                showResult('policyResult', '🔄 Création de la police d\'assurance...');
                log(`📋 Tentative de création police pour véhicule ID: ${vehicleId}`);

                const premiumWei = BlockchainTest.web3.utils.toWei(premium, 'ether');
                const coverageWei = BlockchainTest.web3.utils.toWei(coverage, 'ether');
                const policyNumber = `POL-${Date.now()}`;

                const tx = await BlockchainTest.contracts.InsurancePolicy.methods
                    .createPolicy(
                        parseInt(vehicleId),
                        BlockchainTest.account,
                        1, // Comprehensive coverage
                        premiumWei,
                        coverageWei,
                        BlockchainTest.web3.utils.toWei('0.1', 'ether'), // Deductible
                        parseInt(duration),
                        policyNumber
                    )
                    .send({
                        from: BlockchainTest.account,
                        gas: 800000
                    });

                showResult('policyResult', 
                    `✅ Police créée avec succès!\n` +
                    `📋 Numéro: ${policyNumber}\n` +
                    `🔗 Hash: ${tx.transactionHash}\n` +
                    `⛽ Gas: ${tx.gasUsed}`);
                
                log(`✅ Police créée - ${policyNumber} - TX: ${tx.transactionHash}`);

            } catch (error) {
                showResult('policyResult', `❌ Erreur: ${error.message}`);
                log(`❌ Erreur création police: ${error.message}`);
            }
        }

        async function testClaimSubmission() {
            if (!BlockchainTest.connected) {
                alert('⚠️ Veuillez d\'abord connecter MetaMask');
                return;
            }

            const policyId = document.getElementById('claimPolicyId').value;
            const amount = document.getElementById('claimAmount').value;
            const description = document.getElementById('claimDescription').value;

            if (!policyId || !amount || !description) {
                alert('⚠️ Veuillez remplir tous les champs');
                return;
            }

            try {
                showResult('claimResult', '🔄 Soumission de la réclamation...');
                log(`🔍 Tentative de soumission réclamation pour police ID: ${policyId}`);

                const amountWei = BlockchainTest.web3.utils.toWei(amount, 'ether');
                const evidenceHashes = [`evidence_${Date.now()}`];

                const tx = await BlockchainTest.contracts.ClaimManager.methods
                    .submitClaim(parseInt(policyId), description, amountWei, evidenceHashes)
                    .send({
                        from: BlockchainTest.account,
                        gas: 600000
                    });

                showResult('claimResult', 
                    `✅ Réclamation soumise avec succès!\n` +
                    `💰 Montant: ${amount} ETH\n` +
                    `🔗 Hash: ${tx.transactionHash}\n` +
                    `⛽ Gas: ${tx.gasUsed}`);
                
                log(`✅ Réclamation soumise - TX: ${tx.transactionHash}`);

            } catch (error) {
                showResult('claimResult', `❌ Erreur: ${error.message}`);
                log(`❌ Erreur soumission réclamation: ${error.message}`);
            }
        }

        function testClaimApproval() {
            showResult('claimResult', '⚠️ Fonction d\'approbation nécessite des droits d\'assureur');
            log('⚠️ Test approbation réclamation - Droits insuffisants');
        }

        function testClaimRejection() {
            showResult('claimResult', '⚠️ Fonction de rejet nécessite des droits d\'assureur');
            log('⚠️ Test rejet réclamation - Droits insuffisants');
        }

        function clearLog() {
            document.getElementById('transactionLog').innerHTML = 'Journal effacé...\n';
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', () => {
            loadContractInfo();
            testConnection();
            log('🚀 Application de test blockchain initialisée');
        });
    </script>
</body>
</html>
