services:
  # PRIVATE Enterprise Ethereum Network - Maximum Security
  geth-private:
    image: ethereum/client-go:latest
    container_name: geth-private-enterprise
    ports:
      - "127.0.0.1:8545:8545"  # Bind only to localhost
      - "127.0.0.1:8546:8546"  # Bind only to localhost
    volumes:
      - ./blockchain/data:/root/.ethereum
      - ./blockchain/genesis.json:/genesis.json
      - ./blockchain/password.txt:/password.txt
      - ./blockchain/init.sh:/init.sh
    entrypoint: ["/bin/sh", "/init.sh"]
    environment:
      - PRIVATE_NETWORK=true
      - SECURITY_LEVEL=enterprise
    restart: unless-stopped
    networks:
      private-network:
        ipv4_address: ***********
    security_opt:
      - no-new-privileges:true
    read_only: false
    tmpfs:
      - /tmp:noexec,nosuid,size=100m

  # MongoDB for off-chain data
  mongodb:
    image: mongo:latest
    container_name: insurance-mongodb
    ports:
      - "27017:27017"
    environment:
      MONG<PERSON>_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password
      MONGO_INITDB_DATABASE: insurance_claims
    volumes:
      - mongodb_data:/data/db
    networks:
      - private-network

  # Redis for session management
  redis:
    image: redis:alpine
    container_name: insurance-redis
    ports:
      - "6379:6379"
    networks:
      - private-network

networks:
  private-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  mongodb_data:
