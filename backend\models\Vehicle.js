const mongoose = require('mongoose');

const vehicleSchema = new mongoose.Schema({
  blockchainId: {
    type: Number,
    required: true,
    unique: true
  },
  vin: {
    type: String,
    required: true,
    unique: true,
    uppercase: true,
    trim: true
  },
  make: {
    type: String,
    required: true,
    trim: true
  },
  model: {
    type: String,
    required: true,
    trim: true
  },
  year: {
    type: Number,
    required: true,
    min: 1900,
    max: 2030
  },
  color: {
    type: String,
    required: true,
    trim: true
  },
  owner: {
    type: String,
    required: true,
    lowercase: true
  },
  registrationDate: {
    type: Date,
    required: true
  },
  // Additional off-chain data
  engineNumber: {
    type: String,
    trim: true
  },
  fuelType: {
    type: String,
    enum: ['gasoline', 'diesel', 'electric', 'hybrid', 'other'],
    default: 'gasoline'
  },
  transmission: {
    type: String,
    enum: ['manual', 'automatic', 'cvt'],
    default: 'manual'
  },
  mileage: {
    type: Number,
    min: 0
  },
  purchaseDate: {
    type: Date
  },
  purchasePrice: {
    type: Number,
    min: 0
  },
  currentValue: {
    type: Number,
    min: 0
  },
  images: [{
    url: String,
    description: String,
    uploadDate: {
      type: Date,
      default: Date.now
    }
  }],
  documents: [{
    type: String,
    description: String,
    uploadDate: {
      type: Date,
      default: Date.now
    }
  }],
  isActive: {
    type: Boolean,
    default: true
  },
  notes: {
    type: String
  }
}, {
  timestamps: true
});

// Indexes
vehicleSchema.index({ blockchainId: 1 });
vehicleSchema.index({ vin: 1 });
vehicleSchema.index({ owner: 1 });
vehicleSchema.index({ make: 1, model: 1, year: 1 });

// Virtual for vehicle display name
vehicleSchema.virtual('displayName').get(function() {
  return `${this.year} ${this.make} ${this.model}`;
});

// Virtual for age
vehicleSchema.virtual('age').get(function() {
  return new Date().getFullYear() - this.year;
});

module.exports = mongoose.model('Vehicle', vehicleSchema);
