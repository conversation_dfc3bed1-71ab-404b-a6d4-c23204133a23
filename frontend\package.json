{"name": "insurance-frontend", "version": "1.0.0", "description": "Frontend for vehicle insurance claims DApp", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "react-router-dom": "^6.8.0", "axios": "^1.6.0", "web3": "^4.2.0", "@mui/material": "^5.15.0", "@mui/icons-material": "^5.15.0", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/x-data-grid": "^6.18.0", "@mui/x-date-pickers": "^6.18.0", "dayjs": "^1.11.10", "react-hook-form": "^7.48.0", "react-query": "^3.39.3", "react-hot-toast": "^2.4.1", "recharts": "^2.8.0", "ethers": "^6.8.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:3001"}